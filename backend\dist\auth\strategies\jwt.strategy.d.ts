import { Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
export interface JwtPayload {
    sub: string;
    email: string;
    roles: string[];
    tenantId?: string;
    iat?: number;
    exp?: number;
}
declare const JwtStrategy_base: new (...args: any[]) => Strategy;
export declare class JwtStrategy extends JwtStrategy_base {
    private configService;
    constructor(configService: ConfigService);
    validate(request: Request, payload: JwtPayload): Promise<{
        userId: string;
        email: string;
        roles: string[];
        tenantId: string | undefined;
    }>;
    private extractTenantFromRequest;
}
export {};
