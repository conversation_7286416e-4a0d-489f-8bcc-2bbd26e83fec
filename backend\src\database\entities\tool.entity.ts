import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, BaseEntity } from 'typeorm';
import { Organization } from './organization.entity';
import { User } from './user.entity';
import { ToolExecution } from './tool-execution.entity';

export enum ToolStatus {  
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  ARCHIVED = 'ARCHIVED'
}

export enum ToolType {
  API = 'API',
  DATABASE = 'DATABASE',
  EMAIL = 'EMAIL',
  FILE = 'FILE',
  WEBHOOK = 'WEBHOOK',
  CUSTOM = 'CUSTOM'
}

@Entity('tools')
export class Tool extends BaseEntity   {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  name!: string;  

  @Column({ type: 'text', nullable: true })
  description!: string;

  @Column({ type: 'enum', enum: ToolType })
  type!: ToolType;

  @Column({ type: 'jsonb', default: {} })
  config!: {
    endpoint?: string;
    method?: string;
    headers?: Record<string, string>;
    authentication?: {
      type: 'none' | 'bearer' | 'basic' | 'api_key';
      credentials: Record<string, string>;
    };
    inputSchema: Record<string, any>;
    outputSchema: Record<string, any>;
    timeout: number;
    retries: number;
  };

  @Column({ type: 'jsonb', default: {} })
  schema!: {
    input: Record<string, any>;
    output: Record<string, any>;
  };

  @Column({ type: 'enum', enum: ToolStatus, default: ToolStatus.DRAFT })
  status!: ToolStatus;

  @Column({ type: 'jsonb', default: {} })
  metrics!: {
    totalExecutions: number;
    successRate: number;
    avgResponseTime: number;
    totalCost: number;
  };

  @Column({ default: 1 })
  version!: number;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  @ManyToOne(() => Organization, organization => organization.tools)
  organization!: Organization;

  @Column({ nullable: true })
  organizationId!: string;

  @ManyToOne(() => User)
  createdBy!: User;

  @Column({ nullable: true })
  createdById!: string;

  @OneToMany(() => ToolExecution, execution => execution.tool)
  executions!: ToolExecution[];
}