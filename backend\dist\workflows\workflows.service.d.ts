import { Repository } from 'typeorm';
import { Workflow, WorkflowStatus } from './workflow.entity';
import { CreateWorkflowDto, UpdateWorkflowDto, WorkflowFiltersDto } from './dto/workflow.dto';
import { WorkflowValidationService } from './workflow-validation.service';
import { WorkflowAnalyticsService } from './workflow-analytics.service';
import { CustomLoggerService } from '../logging/logger.service';
export declare class WorkflowsService {
    private readonly workflowRepository;
    private readonly validationService;
    private readonly analyticsService;
    private readonly logger;
    constructor(workflowRepository: Repository<Workflow>, validationService: WorkflowValidationService, analyticsService: WorkflowAnalyticsService, logger: CustomLoggerService);
    createWorkflow(createWorkflowDto: CreateWorkflowDto, userId: string, organizationId: string): Promise<Workflow>;
    getWorkflows(filters: WorkflowFiltersDto, userId: string, organizationId: string): Promise<{
        workflows: Workflow[];
        total: number;
    }>;
    getWorkflowById(id: string, userId: string, organizationId: string): Promise<Workflow>;
    updateWorkflow(id: string, updateWorkflowDto: UpdateWorkflowDto, userId: string, organizationId: string): Promise<Workflow>;
    deleteWorkflow(id: string, userId: string, organizationId: string): Promise<void>;
    changeWorkflowStatus(id: string, status: WorkflowStatus, userId: string, organizationId: string): Promise<Workflow>;
    duplicateWorkflow(id: string, userId: string, organizationId: string): Promise<Workflow>;
    getWorkflowAnalytics(id: string, userId: string, organizationId: string): Promise<any>;
    private buildWorkflowQuery;
    private hasViewPermission;
    private hasEditPermission;
    private hasAdminPermission;
    private isValidStatusTransition;
    getRealTimeMetrics(id: string, userId: string, organizationId: string): Promise<any>;
}
