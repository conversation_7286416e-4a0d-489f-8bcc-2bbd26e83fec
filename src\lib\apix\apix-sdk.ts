import { z } from 'zod';

// APIX Protocol Schemas
const ApiXConnectionSchema = z.object({
    sessionId: z.string(),
    clientType: z.enum(['WEB_APP', 'MOBILE_APP', 'SDK_WIDGET', 'API_CLIENT', 'INTERNAL_SERVICE']),
    authentication: z.object({
        token: z.string(),
        organizationId: z.string().optional(),
    }),
    subscriptions: z.array(z.string()),
    metadata: z.record(z.any(), z.any()).optional(),
});

const ApiXEventSchema = z.object({
    type: z.string(),
    channel: z.string(),
    payload: z.any(),
    metadata: z.object({
        timestamp: z.number(),
        version: z.string(),
        correlation_id: z.string().optional(),
    }).optional(),
});

const ApiXSubscriptionSchema = z.object({
    channels: z.array(z.string()),
    filters: z.record(z.any(), z.any()).optional(),
    acknowledgment: z.boolean().default(false),
});

// AI Provider Event Types
const AIProviderEventTypes = {
    'provider.created': z.object({
        providerId: z.string(),
        organizationId: z.string(),
        name: z.string(),
        type: z.string()
    }),
    'provider.updated': z.object({
        providerId: z.string(),
        changes: z.array(z.string())
    }),
    'provider.deleted': z.object({
        providerId: z.string()
    }),
    'provider.request.start': z.object({
        requestId: z.string(),
        providerId: z.string(),
        modelId: z.string(),
        input: z.any()
    }),
    'provider.request.progress': z.object({
        requestId: z.string(),
        progress: z.number(),
        message: z.string().optional()
    }),
    'provider.request.complete': z.object({
        requestId: z.string(),
        output: z.any(),
        durationMs: z.number()
    }),
    'provider.request.error': z.object({
        requestId: z.string(),
        error: z.string(),
        retryable: z.boolean()
    }),
    'provider.usage.report': z.object({
        providerId: z.string(),
        date: z.date(),
        requests: z.number(),
        tokensUsed: z.number(),
        costInCents: z.number()
    }),
    'provider.health_update': z.object({
        providerId: z.string(),
        status: z.enum(['healthy', 'degraded', 'unhealthy']),
        latency: z.number()
    }),
};

export type AIProviderEventType = keyof typeof AIProviderEventTypes;
export type AIProviderEventPayload<T extends AIProviderEventType> = z.infer<typeof AIProviderEventTypes[T]>;

// APIX SDK Class
class ApiXSDK {
    private ws: WebSocket | null = null;
    private sessionId: string | null = null;
    private organizationId: string | null = null;
    private userId: string | null = null;
    private subscriptions = new Map<string, Set<(payload: any) => void>>();
    private reconnectAttempts = 0;
    private maxReconnectAttempts = 5;
    private reconnectDelay = 1000;
    private heartbeatInterval: NodeJS.Timeout | null = null;
    private messageQueue: Array<{ type: string; payload: any; timestamp: number }> = [];
    private isConnected = false;
    private connectionPromise: Promise<void> | null = null;

    constructor(
        private baseUrl: string = process.env.NEXT_PUBLIC_APIX_URL || 'ws://localhost:3001',
        private clientType: 'WEB_APP' | 'MOBILE_APP' | 'SDK_WIDGET' = 'WEB_APP'
    ) { }

    async connect(token: string, organizationId?: string, userId?: string): Promise<void> {
        if (this.connectionPromise) {
            return this.connectionPromise;
        }

        this.connectionPromise = this._connect(token, organizationId, userId);
        return this.connectionPromise;
    }

    private async _connect(token: string, organizationId?: string, userId?: string): Promise<void> {
        return new Promise((resolve, reject) => {
            try {
                this.ws = new WebSocket(`${this.baseUrl}/apix`);
                this.organizationId = organizationId || null;
                this.userId = userId || null;

                this.ws.onopen = () => {
                    console.log('APIX: WebSocket connected');
                    this.isConnected = true;
                    this.reconnectAttempts = 0;

                    // Send authentication
                    this.send({
                        type: 'connection.authenticate',
                        payload: {
                            token,
                            organizationId: this.organizationId,
                            clientType: this.clientType,
                            metadata: {
                                userAgent: navigator.userAgent,
                                timestamp: Date.now()
                            }
                        }
                    });

                    // Start heartbeat
                    this.startHeartbeat();

                    // Process queued messages
                    this.processMessageQueue();

                    resolve();
                };

                this.ws.onmessage = (event) => {
                    this.handleMessage(event.data);
                };

                this.ws.onclose = (event) => {
                    console.log('APIX: WebSocket disconnected', event.code, event.reason);
                    this.isConnected = false;
                    this.stopHeartbeat();

                    if (event.code !== 1000) { // Not a normal closure
                        this.scheduleReconnect();
                    }
                };

                this.ws.onerror = (error) => {
                    console.error('APIX: WebSocket error', error);
                    reject(new Error('WebSocket connection failed'));
                };

            } catch (error) {
                reject(error);
            }
        });
    }

    private handleMessage(data: string): void {
        try {
            const message = JSON.parse(data);
            const event = ApiXEventSchema.parse(message);

            console.log('APIX: Received event', event.type, event.channel);

            switch (event.type) {
                case 'connection.established':
                    this.sessionId = event.payload.sessionId;
                    console.log('APIX: Connection established', this.sessionId);
                    break;

                case 'connection.error':
                    console.error('APIX: Connection error', event.payload);
                    break;

                case 'subscription.added':
                    console.log('APIX: Subscription added', event.payload);
                    break;

                case 'subscription.removed':
                    console.log('APIX: Subscription removed', event.payload);
                    break;

                case 'heartbeat.pong':
                    // Heartbeat response received
                    break;

                default:
                    // Handle custom events
                    this.handleCustomEvent(event);
                    break;
            }
        } catch (error) {
            console.error('APIX: Failed to parse message', error, data);
        }
    }

    private handleCustomEvent(event: z.infer<typeof ApiXEventSchema>): void {
        const handlers = this.subscriptions.get(event.channel);
        if (handlers) {
            handlers.forEach(handler => {
                try {
                    handler(event.payload);
                } catch (error) {
                    console.error('APIX: Handler error', error);
                }
            });
        }
    }

    subscribe<T extends AIProviderEventType>(
        channel: string,
        eventType: T,
        handler: (payload: AIProviderEventPayload<T>) => void
    ): () => void {
        if (!this.subscriptions.has(channel)) {
            this.subscriptions.set(channel, new Set());
        }

        const handlers = this.subscriptions.get(channel)!;
        handlers.add(handler);

        // Send subscription request
        if (this.isConnected) {
            this.send({
                type: 'subscription.add',
                payload: {
                    channel,
                    eventType,
                    acknowledgment: true
                }
            });
        }

        // Return unsubscribe function
        return () => {
            handlers.delete(handler);
            if (handlers.size === 0) {
                this.subscriptions.delete(channel);

                // Send unsubscription request
                if (this.isConnected) {
                    this.send({
                        type: 'subscription.remove',
                        payload: { channel }
                    });
                }
            }
        };
    }

    // AI Provider specific subscriptions
    subscribeToProviderEvents(
        providerId: string,
        handlers: {
            onRequestStart?: (payload: AIProviderEventPayload<'provider.request.start'>) => void;
            onRequestProgress?: (payload: AIProviderEventPayload<'provider.request.progress'>) => void;
            onRequestComplete?: (payload: AIProviderEventPayload<'provider.request.complete'>) => void;
            onRequestError?: (payload: AIProviderEventPayload<'provider.request.error'>) => void;
            onHealthUpdate?: (payload: AIProviderEventPayload<'provider.health_update'>) => void;
        }
    ): () => void {
        const unsubscribers: Array<() => void> = [];

        if (handlers.onRequestStart) {
            unsubscribers.push(
                this.subscribe(`provider.request.${providerId}`, 'provider.request.start', handlers.onRequestStart)
            );
        }

        if (handlers.onRequestProgress) {
            unsubscribers.push(
                this.subscribe(`provider.request.${providerId}`, 'provider.request.progress', handlers.onRequestProgress)
            );
        }

        if (handlers.onRequestComplete) {
            unsubscribers.push(
                this.subscribe(`provider.request.${providerId}`, 'provider.request.complete', handlers.onRequestComplete)
            );
        }

        if (handlers.onRequestError) {
            unsubscribers.push(
                this.subscribe(`provider.request.${providerId}`, 'provider.request.error', handlers.onRequestError)
            );
        }

        if (handlers.onHealthUpdate) {
            unsubscribers.push(
                this.subscribe(`provider.${providerId}`, 'provider.health_update', handlers.onHealthUpdate)
            );
        }

        return () => {
            unsubscribers.forEach(unsub => unsub());
        };
    }

    subscribeToOrganizationEvents(
        organizationId: string,
        handlers: {
            onProviderCreated?: (payload: AIProviderEventPayload<'provider.created'>) => void;
            onProviderUpdated?: (payload: AIProviderEventPayload<'provider.updated'>) => void;
            onProviderDeleted?: (payload: AIProviderEventPayload<'provider.deleted'>) => void;
            onUsageReport?: (payload: AIProviderEventPayload<'provider.usage.report'>) => void;
        }
    ): () => void {
        const unsubscribers: Array<() => void> = [];

        if (handlers.onProviderCreated) {
            unsubscribers.push(
                this.subscribe(`providers.${organizationId}`, 'provider.created', handlers.onProviderCreated)
            );
        }

        if (handlers.onProviderUpdated) {
            unsubscribers.push(
                this.subscribe(`providers.${organizationId}`, 'provider.updated', handlers.onProviderUpdated)
            );
        }

        if (handlers.onProviderDeleted) {
            unsubscribers.push(
                this.subscribe(`providers.${organizationId}`, 'provider.deleted', handlers.onProviderDeleted)
            );
        }

        if (handlers.onUsageReport) {
            unsubscribers.push(
                this.subscribe(`providers.${organizationId}`, 'provider.usage.report', handlers.onUsageReport)
            );
        }

        return () => {
            unsubscribers.forEach(unsub => unsub());
        };
    }

    private send(message: { type: string; payload: any }): void {
        if (this.isConnected && this.ws) {
            this.ws.send(JSON.stringify({
                ...message,
                metadata: {
                    timestamp: Date.now(),
                    version: '1.0.0',
                    sessionId: this.sessionId
                }
            }));
        } else {
            // Queue message for later
            this.messageQueue.push({
                type: message.type,
                payload: message.payload,
                timestamp: Date.now()
            });
        }
    }

    private processMessageQueue(): void {
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            if (message) {
                this.send(message);
            }
        }
    }

    private startHeartbeat(): void {
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                this.send({
                    type: 'heartbeat.ping',
                    payload: { timestamp: Date.now() }
                });
            }
        }, 30000); // 30 seconds
    }

    private stopHeartbeat(): void {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    private scheduleReconnect(): void {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

            console.log(`APIX: Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);

            setTimeout(() => {
                this.reconnect();
            }, delay);
        } else {
            console.error('APIX: Max reconnection attempts reached');
        }
    }

    private async reconnect(): Promise<void> {
        try {
            const token = localStorage.getItem('token');
            if (token) {
                await this.connect(token, this.organizationId || undefined, this.userId || undefined);
            }
        } catch (error) {
            console.error('APIX: Reconnection failed', error);
        }
    }

    disconnect(): void {
        this.isConnected = false;
        this.stopHeartbeat();

        if (this.ws) {
            this.ws.close(1000, 'Client disconnect');
            this.ws = null;
        }

        this.connectionPromise = null;
        this.subscriptions.clear();
        this.messageQueue = [];
    }

    isConnectedStatus(): boolean {
        return this.isConnected;
    }

    getSessionId(): string | null {
        return this.sessionId;
    }
}

// Export singleton instance
export const apixSDK = new ApiXSDK();

// Export types
export type ApiXConnection = z.infer<typeof ApiXConnectionSchema>;
export type ApiXEvent = z.infer<typeof ApiXEventSchema>;
export type ApiXSubscription = z.infer<typeof ApiXSubscriptionSchema>; 