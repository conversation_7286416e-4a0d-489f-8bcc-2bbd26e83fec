import { Injectable, LoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';
import * as path from 'path';

@Injectable()
export class CustomLoggerService implements LoggerService {
    private logger!: winston.Logger;

    constructor(private configService: ConfigService) {
        this.initializeLogger();
    }

    private initializeLogger() {
        const logLevel = this.configService.get('app.logging.level') || 'info';
        const enableFileLogging = this.configService.get('app.logging.enableFileLogging');
        const logDirectory = this.configService.get('app.logging.logDirectory') || 'logs';

        const transports: winston.transport[] = [
            new winston.transports.Console({
                format: winston.format.combine(
                    winston.format.timestamp(),
                    winston.format.colorize(),
                    winston.format.printf(({ timestamp, level, message, context, trace, ...meta }) => {
                        let log = `${timestamp} [${level}] ${context ? `[${context}] ` : ''}${message}`;
                        if (Object.keys(meta).length > 0) {
                            log += ` ${JSON.stringify(meta)}`;
                        }
                        if (trace) {
                            log += `\n${trace}`;
                        }
                        return log;
                    }),
                ),
            }),
        ];

        if (enableFileLogging) {
            // Error logs
            transports.push(
                new DailyRotateFile({
                    filename: path.join(logDirectory, 'error-%DATE%.log'),
                    datePattern: 'YYYY-MM-DD',
                    level: 'error',
                    maxSize: '20m',
                    maxFiles: '14d',
                    format: winston.format.combine(
                        winston.format.timestamp(),
                        winston.format.errors({ stack: true }),
                        winston.format.json(),
                    ),
                }),
            );

            // Combined logs
            transports.push(
                new DailyRotateFile({
                    filename: path.join(logDirectory, 'combined-%DATE%.log'),
                    datePattern: 'YYYY-MM-DD',
                    maxSize: '20m',
                    maxFiles: '14d',
                    format: winston.format.combine(
                        winston.format.timestamp(),
                        winston.format.json(),
                    ),
                }),
            );
        }

        this.logger = winston.createLogger({
            level: logLevel,
            format: winston.format.combine(
                winston.format.timestamp(),
                winston.format.errors({ stack: true }),
                winston.format.json(),
            ),
            defaultMeta: { service: 'ai-platform' },
            transports,
        });
    }

    log(message: string, context?: string) {
        this.logger.info(message, { context });
    }

    error(message: string, trace?: string, context?: string) {
        this.logger.error(message, { context, trace });
    }

    warn(message: string, context?: string) {
        this.logger.warn(message, { context });
    }

    debug(message: string, context?: string) {
        this.logger.debug(message, { context });
    }

    verbose(message: string, context?: string) {
        this.logger.verbose(message, { context });
    }

    // Additional methods for structured logging
    logWithMeta(message: string, meta: Record<string, any>, context?: string) {
        this.logger.info(message, { context, ...meta });
    }

    logErrorWithMeta(message: string, error: Error, meta: Record<string, any> = {}, context?: string) {
        this.logger.error(message, {
            context,
            error: {
                name: error.name,
                message: error.message,
                stack: error.stack,
            },
            ...meta,
        });
    }

    logPerformance(operation: string, duration: number, meta: Record<string, any> = {}, context?: string) {
        this.logger.info(`Performance: ${operation}`, {
            context,
            operation,
            duration,
            ...meta,
        });
    }

    logSecurity(event: string, meta: Record<string, any> = {}, context?: string) {
        this.logger.warn(`Security Event: ${event}`, {
            context,
            event,
            ...meta,
        });
    }

    logDatabase(query: string, duration: number, meta: Record<string, any> = {}, context?: string) {
        this.logger.debug(`Database Query: ${query}`, {
            context,
            query,
            duration,
            ...meta,
        });
    }

    logApiRequest(method: string, url: string, statusCode: number, duration: number, meta: Record<string, any> = {}, context?: string) {
        this.logger.info(`API Request: ${method} ${url}`, {
            context,
            method,
            url,
            statusCode,
            duration,
            ...meta,
        });
    }
} 