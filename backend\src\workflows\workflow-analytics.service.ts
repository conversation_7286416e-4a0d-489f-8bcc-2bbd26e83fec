import { Injectable } from '@nestjs/common';
import { Workflow } from './workflow.entity';
import { CustomLoggerService } from '../logging/logger.service';
import { RedisService } from '../cache/redis.service';

export interface WorkflowAnalytics {
    executionMetrics: {
        totalExecutions: number;
        successfulExecutions: number;
        failedExecutions: number;
        successRate: number;
        averageExecutionTime: number;
        totalCost: number;
        averageCost: number;
    };
    performanceMetrics: {
        stepPerformance: Array<{
            stepId: string;
            stepName: string;
            averageDuration: number;
            successRate: number;
            failureRate: number;
            totalExecutions: number;
        }>;
        bottleneckSteps: Array<{
            stepId: string;
            stepName: string;
            averageDuration: number;
            impact: number;
        }>;
        throughput: {
            executionsPerHour: number;
            executionsPerDay: number;
            peakHour: string;
            peakDay: string;
        };
    };
    costAnalysis: {
        totalCost: number;
        costPerExecution: number;
        costByStep: Array<{
            stepId: string;
            stepName: string;
            totalCost: number;
            averageCost: number;
        }>;
        costTrend: Array<{
            date: string;
            cost: number;
            executions: number;
        }>;
    };
    errorAnalysis: {
        errorRate: number;
        commonErrors: Array<{
            error: string;
            count: number;
            percentage: number;
        }>;
        errorTrend: Array<{
            date: string;
            errors: number;
            total: number;
        }>;
    };
    optimization: {
        recommendations: Array<{
            type: 'performance' | 'cost' | 'reliability';
            title: string;
            description: string;
            impact: 'high' | 'medium' | 'low';
            estimatedSavings?: number;
        }>;
        bottlenecks: Array<{
            stepId: string;
            stepName: string;
            issue: string;
            recommendation: string;
        }>;
    };
}

@Injectable()
export class WorkflowAnalyticsService {
    constructor(
        private readonly logger: CustomLoggerService,
        private readonly redisService: RedisService,
    ) { }

    async getWorkflowAnalytics(workflow: Workflow): Promise<WorkflowAnalytics> {
        try {
            const executionHistory = workflow.executionHistory || [];
            const recentExecutions = await this.getRecentExecutions(workflow.id);

            const analytics: WorkflowAnalytics = {
                executionMetrics: this.calculateExecutionMetrics(workflow, executionHistory, recentExecutions),
                performanceMetrics: this.calculatePerformanceMetrics(workflow, executionHistory, recentExecutions),
                costAnalysis: this.calculateCostAnalysis(workflow, executionHistory, recentExecutions),
                errorAnalysis: this.calculateErrorAnalysis(workflow, executionHistory, recentExecutions),
                optimization: this.generateOptimizationRecommendations(workflow, executionHistory, recentExecutions),
            };

            return analytics;
        } catch (error) {
            this.logger.error(`Failed to calculate workflow analytics: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowAnalyticsService');
            throw error;
        }
    }

    async getOrganizationAnalytics(organizationId: string, timeRange: { start: Date; end: Date }): Promise<any> {
        try {
            // This would aggregate analytics across all workflows in the organization
            const workflows = await this.getWorkflowsByOrganization(organizationId);
            const analytics = {
                totalWorkflows: workflows.length,
                activeWorkflows: workflows.filter(w => w.status === 'active').length,
                totalExecutions: workflows.reduce((sum, w) => sum + w.executionCount, 0),
                totalCost: workflows.reduce((sum, w) => sum + Number(w.totalCost), 0),
                averageSuccessRate: this.calculateAverageSuccessRate(workflows),
                topPerformingWorkflows: this.getTopPerformingWorkflows(workflows),
                costTrend: await this.getOrganizationCostTrend(organizationId, timeRange),
            };

            return analytics;
        } catch (error) {
            this.logger.error(`Failed to calculate organization analytics: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowAnalyticsService');
            throw error;
        }
    }

    async getRealTimeMetrics(workflowId: string): Promise<any> {
        try {
            const activeExecutions = await this.getActiveExecutions(workflowId);
            const currentStep = this.getCurrentStep(activeExecutions);
            const queueLength = await this.getQueueLength(workflowId);

            return {
                activeExecutions: activeExecutions.length,
                currentStep,
                queueLength,
                lastExecution: await this.getLastExecution(workflowId),
                systemHealth: await this.getSystemHealth(),
            };
        } catch (error) {
            this.logger.error(`Failed to get real-time metrics: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowAnalyticsService');
            throw error;
        }
    }

    private calculateExecutionMetrics(workflow: Workflow, executionHistory: any[], recentExecutions: any[]) {
        const totalExecutions = workflow.executionCount;
        const successfulExecutions = workflow.successCount;
        const failedExecutions = workflow.failureCount;
        const successRate = totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0;
        const averageExecutionTime = Number(workflow.averageExecutionTime) || 0;
        const totalCost = Number(workflow.totalCost) || 0;
        const averageCost = totalExecutions > 0 ? totalCost / totalExecutions : 0;

        return {
            totalExecutions,
            successfulExecutions,
            failedExecutions,
            successRate,
            averageExecutionTime,
            totalCost,
            averageCost,
        };
    }

    private calculatePerformanceMetrics(workflow: Workflow, executionHistory: any[], recentExecutions: any[]) {
        const stepPerformance = this.analyzeStepPerformance(executionHistory);
        const bottleneckSteps = this.identifyBottlenecks(stepPerformance);
        const throughput = this.calculateThroughput(recentExecutions);

        return {
            stepPerformance,
            bottleneckSteps,
            throughput,
        };
    }

    private calculateCostAnalysis(workflow: Workflow, executionHistory: any[], recentExecutions: any[]) {
        const totalCost = Number(workflow.totalCost) || 0;
        const costPerExecution = workflow.executionCount > 0 ? totalCost / workflow.executionCount : 0;
        const costByStep = this.calculateCostByStep(executionHistory);
        const costTrend = this.calculateCostTrend(recentExecutions);

        return {
            totalCost,
            costPerExecution,
            costByStep,
            costTrend,
        };
    }

    private calculateErrorAnalysis(workflow: Workflow, executionHistory: any[], recentExecutions: any[]) {
        const errorRate = workflow.executionCount > 0 ? (workflow.failureCount / workflow.executionCount) * 100 : 0;
        const commonErrors = this.analyzeCommonErrors(executionHistory);
        const errorTrend = this.calculateErrorTrend(recentExecutions);

        return {
            errorRate,
            commonErrors,
            errorTrend,
        };
    }

    private generateOptimizationRecommendations(workflow: Workflow, executionHistory: any[], recentExecutions: any[]) {
        const recommendations = this.generateRecommendations(workflow, executionHistory);
        const bottlenecks = this.identifyOptimizationBottlenecks(executionHistory);

        return {
            recommendations,
            bottlenecks,
        };
    }

    private analyzeStepPerformance(executionHistory: any[]) {
        const stepStats = new Map<string, {
            stepId: string;
            stepName: string;
            totalExecutions: number;
            successfulExecutions: number;
            failedExecutions: number;
            totalDuration: number;
        }>();

        for (const execution of executionHistory) {
            for (const stepResult of execution.stepResults || []) {
                const { stepId, status, duration } = stepResult;
                const stats = stepStats.get(stepId) || {
                    stepId,
                    stepName: stepId, // In production, you'd get the actual step name
                    totalExecutions: 0,
                    successfulExecutions: 0,
                    failedExecutions: 0,
                    totalDuration: 0,
                };

                stats.totalExecutions++;
                if (status === 'completed') {
                    stats.successfulExecutions++;
                } else if (status === 'failed') {
                    stats.failedExecutions++;
                }
                stats.totalDuration += duration || 0;

                stepStats.set(stepId, stats);
            }
        }

        return Array.from(stepStats.values()).map(stats => ({
            stepId: stats.stepId,
            stepName: stats.stepName,
            averageDuration: stats.totalExecutions > 0 ? stats.totalDuration / stats.totalExecutions : 0,
            successRate: stats.totalExecutions > 0 ? (stats.successfulExecutions / stats.totalExecutions) * 100 : 0,
            failureRate: stats.totalExecutions > 0 ? (stats.failedExecutions / stats.totalExecutions) * 100 : 0,
            totalExecutions: stats.totalExecutions,
        }));
    }

    private identifyBottlenecks(stepPerformance: any[]) {
        const sortedSteps = stepPerformance
            .filter(step => step.totalExecutions > 0)
            .sort((a, b) => b.averageDuration - a.averageDuration);

        const totalAverageDuration = stepPerformance.reduce((sum, step) => sum + step.averageDuration, 0);
        const averageDuration = stepPerformance.length > 0 ? totalAverageDuration / stepPerformance.length : 0;

        return sortedSteps
            .filter(step => step.averageDuration > averageDuration * 1.5)
            .map(step => ({
                stepId: step.stepId,
                stepName: step.stepName,
                averageDuration: step.averageDuration,
                impact: ((step.averageDuration - averageDuration) / averageDuration) * 100,
            }));
    }

    private calculateThroughput(recentExecutions: any[]) {
        const now = new Date();
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

        const executionsPerHour = recentExecutions.filter(exec =>
            new Date(exec.startTime) > oneHourAgo
        ).length;

        const executionsPerDay = recentExecutions.filter(exec =>
            new Date(exec.startTime) > oneDayAgo
        ).length;

        // Calculate peak hours and days
        const hourlyStats = this.calculateHourlyStats(recentExecutions);
        const dailyStats = this.calculateDailyStats(recentExecutions);

        return {
            executionsPerHour,
            executionsPerDay,
            peakHour: hourlyStats.peakHour,
            peakDay: dailyStats.peakDay,
        };
    }

    private calculateCostByStep(executionHistory: any[]) {
        const stepCosts = new Map<string, { stepId: string; stepName: string; totalCost: number; executions: number }>();

        for (const execution of executionHistory) {
            for (const stepResult of execution.stepResults || []) {
                const { stepId, cost = 0 } = stepResult;
                const stats = stepCosts.get(stepId) || {
                    stepId,
                    stepName: stepId,
                    totalCost: 0,
                    executions: 0,
                };

                stats.totalCost += cost;
                stats.executions++;

                stepCosts.set(stepId, stats);
            }
        }

        return Array.from(stepCosts.values()).map(stats => ({
            stepId: stats.stepId,
            stepName: stats.stepName,
            totalCost: stats.totalCost,
            averageCost: stats.executions > 0 ? stats.totalCost / stats.executions : 0,
        }));
    }

    private calculateCostTrend(recentExecutions: any[]) {
        const dailyCosts = new Map<string, { cost: number; executions: number }>();

        for (const execution of recentExecutions) {
            const date = new Date(execution.startTime).toISOString().split('T')[0];
            const stats = dailyCosts.get(date) || { cost: 0, executions: 0 };

            stats.cost += execution.totalCost || 0;
            stats.executions++;

            dailyCosts.set(date, stats);
        }

        return Array.from(dailyCosts.entries())
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([date, stats]) => ({
                date,
                cost: stats.cost,
                executions: stats.executions,
            }));
    }

    private analyzeCommonErrors(executionHistory: any[]) {
        const errorCounts = new Map<string, number>();

        for (const execution of executionHistory) {
            for (const stepResult of execution.stepResults || []) {
                if (stepResult.error) {
                    const count = errorCounts.get(stepResult.error) || 0;
                    errorCounts.set(stepResult.error, count + 1);
                }
            }
        }

        const totalErrors = Array.from(errorCounts.values()).reduce((sum, count) => sum + count, 0);

        return Array.from(errorCounts.entries())
            .map(([error, count]) => ({
                error,
                count,
                percentage: totalErrors > 0 ? (count / totalErrors) * 100 : 0,
            }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);
    }

    private calculateErrorTrend(recentExecutions: any[]) {
        const dailyErrors = new Map<string, { errors: number; total: number }>();

        for (const execution of recentExecutions) {
            const date = new Date(execution.startTime).toISOString().split('T')[0];
            const stats = dailyErrors.get(date) || { errors: 0, total: 0 };

            stats.total++;
            if (execution.status === 'failed') {
                stats.errors++;
            }

            dailyErrors.set(date, stats);
        }

        return Array.from(dailyErrors.entries())
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([date, stats]) => ({
                date,
                errors: stats.errors,
                total: stats.total,
            }));
    }

    private generateRecommendations(workflow: Workflow, executionHistory: any[]) {
        const recommendations = [];

        // Performance recommendations
        const slowSteps = executionHistory
            .flatMap(exec => exec.stepResults || [])
            .filter(step => step.duration > 5000) // Steps taking more than 5 seconds
            .reduce((acc, step) => {
                acc[step.stepId] = (acc[step.stepId] || 0) + 1;
                return acc;
            }, {});

        for (const [stepId, count] of Object.entries(slowSteps) as [string, number][]) {
            if (count > 3) {
                recommendations.push({
                    type: 'performance' as const,
                    title: 'Optimize Slow Step',
                    description: `Step ${stepId} is consistently slow (${count} occurrences). Consider optimizing or caching.`,
                    impact: 'high' as const,
                });
            }
        }

        // Cost recommendations
        const expensiveSteps = executionHistory
            .flatMap(exec => exec.stepResults || [])
            .filter(step => step.cost > 1) // Steps costing more than $1
            .reduce((acc, step) => {
                acc[step.stepId] = (acc[step.stepId] || 0) + step.cost;
                return acc;
            }, {});

        for (const [stepId, totalCost] of Object.entries(expensiveSteps) as [string, number][]) {
            if (totalCost > 10) {
                recommendations.push({
                    type: 'cost' as const,
                    title: 'Reduce Step Cost',
                    description: `Step ${stepId} is expensive ($${totalCost.toFixed(2)} total). Consider alternatives or optimization.`,
                    impact: 'medium' as const,
                    estimatedSavings: totalCost * 0.3, // Estimate 30% savings
                });
            }
        }

        // Reliability recommendations
        const errorRate = workflow.failureCount / workflow.executionCount;
        if (errorRate > 0.1) { // More than 10% failure rate
            recommendations.push({
                type: 'reliability' as const,
                title: 'Improve Error Handling',
                description: `High failure rate (${(errorRate * 100).toFixed(1)}%). Review error handling and retry logic.`,
                impact: 'high' as const,
            });
        }

        return recommendations;
    }

    private identifyOptimizationBottlenecks(executionHistory: any[]) {
        const bottlenecks = [];

        // Find steps with high failure rates
        const stepFailures = new Map<string, { failures: number; total: number }>();
        for (const execution of executionHistory) {
            for (const stepResult of execution.stepResults || []) {
                const stats = stepFailures.get(stepResult.stepId) || { failures: 0, total: 0 };
                stats.total++;
                if (stepResult.status === 'failed') {
                    stats.failures++;
                }
                stepFailures.set(stepResult.stepId, stats);
            }
        }

        for (const [stepId, stats] of stepFailures) {
            const failureRate = stats.total > 0 ? stats.failures / stats.total : 0;
            if (failureRate > 0.2) { // More than 20% failure rate
                bottlenecks.push({
                    stepId,
                    stepName: stepId,
                    issue: `High failure rate (${(failureRate * 100).toFixed(1)}%)`,
                    recommendation: 'Review step configuration and error handling',
                });
            }
        }

        return bottlenecks;
    }

    private calculateHourlyStats(executions: any[]) {
        const hourlyCounts = new Array(24).fill(0);

        for (const execution of executions) {
            const hour = new Date(execution.startTime).getHours();
            hourlyCounts[hour]++;
        }

        const peakHour = hourlyCounts.indexOf(Math.max(...hourlyCounts));
        return { peakHour: `${peakHour}:00` };
    }

    private calculateDailyStats(executions: any[]) {
        const dailyCounts = new Array(7).fill(0);

        for (const execution of executions) {
            const day = new Date(execution.startTime).getDay();
            dailyCounts[day]++;
        }

        const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const peakDay = days[dailyCounts.indexOf(Math.max(...dailyCounts))];
        return { peakDay };
    }

    // Helper methods for data retrieval (these would be implemented based on your data storage)
    private async getRecentExecutions(workflowId: string): Promise<any[]> {
        // This would fetch recent executions from your database or cache
        return [];
    }

    private async getWorkflowsByOrganization(organizationId: string): Promise<Workflow[]> {
        // This would fetch workflows for the organization
        return [];
    }

    private calculateAverageSuccessRate(workflows: Workflow[]): number {
        const totalExecutions = workflows.reduce((sum, w) => sum + w.executionCount, 0);
        const totalSuccesses = workflows.reduce((sum, w) => sum + w.successCount, 0);
        return totalExecutions > 0 ? (totalSuccesses / totalExecutions) * 100 : 0;
    }

    private getTopPerformingWorkflows(workflows: Workflow[]): any[] {
        return workflows
            .filter(w => w.executionCount > 0)
            .map(w => ({
                id: w.id,
                name: w.name,
                successRate: (w.successCount / w.executionCount) * 100,
                averageExecutionTime: Number(w.averageExecutionTime),
            }))
            .sort((a, b) => b.successRate - a.successRate)
            .slice(0, 5);
    }

    private async getOrganizationCostTrend(organizationId: string, timeRange: { start: Date; end: Date }): Promise<any[]> {
        // This would calculate cost trends for the organization
        return [];
    }

    private async getActiveExecutions(workflowId: string): Promise<any[]> {
        // This would get currently running executions
        return [];
    }

    private getCurrentStep(activeExecutions: any[]): string | null {
        return activeExecutions.length > 0 ? activeExecutions[0].currentStep : null;
    }

    private async getQueueLength(workflowId: string): Promise<number> {
        // This would get the number of queued executions
        return 0;
    }

    private async getLastExecution(workflowId: string): Promise<any> {
        // This would get the most recent execution
        return null;
    }

    private async getSystemHealth(): Promise<any> {
        // This would get system health metrics
        return { status: 'healthy', uptime: 99.9 };
    }
} 