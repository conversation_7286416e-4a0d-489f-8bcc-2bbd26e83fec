import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, Index, BaseEntity } from 'typeorm';
import { User } from '../users/user.entity';
import { Agent } from '../agents/agent.entity';

export enum ToolType {
  HTTP_API = 'http_api',
  DATABASE = 'database',
  FILE_SYSTEM = 'file_system',
  EMAIL = 'email',
  SMS = 'sms',
  WEBHOOK = 'webhook',
  CALENDAR = 'calendar',
  CRM = 'crm',
  PAYMENT = 'payment',
  ANALYTICS = 'analytics',
  AI_MODEL = 'ai_model',
  CUSTOM = 'custom',
}

export enum ToolStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error',
  DEPRECATED = 'deprecated',
}

export enum ToolPermission {
  READ = 'read',
  WRITE = 'write',
  EXECUTE = 'execute',
  ADMIN = 'admin',
}

@Entity('tools')
@Index(['userId', 'status'])
@Index(['organizationId', 'type'])
@Index(['agentId', 'status'])
export class Tool extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  name!: string;

  @Column({ type: 'text', nullable: true })
  description!: string;

  @Column({
    type: 'enum',
    enum: ToolType,
    default: ToolType.CUSTOM,
  })
  type!: ToolType;

  @Column({
    type: 'enum',
    enum: ToolStatus,
    default: ToolStatus.DRAFT,
  })
  status!: ToolStatus;

  @Column({ type: 'jsonb' })
  configuration!: {
    endpoint?: string;
    method?: string;
    headers?: Record<string, string>;
    authentication: {
      type: 'none' | 'api_key' | 'oauth2' | 'bearer' | 'basic';
      credentials: Record<string, any>;
    };
    parameters: Array<{
      name: string;
      type: string;
      required: boolean;
      default?: any;
      description: string;
      validation?: Record<string, any>;
    }>;
    response: {
      format: 'json' | 'xml' | 'text' | 'binary';
      schema?: Record<string, any>;
      examples?: Array<{
        name: string;
        request: Record<string, any>;
        response: Record<string, any>;
      }>;
    };
    rateLimiting: {
      enabled: boolean;
      requestsPerMinute: number;
      burstLimit: number;
    };
    caching: {
      enabled: boolean;
      ttl: number;
      keyPattern: string;
    };
    errorHandling: {
      retryCount: number;
      retryDelay: number;
      timeout: number;
      fallbackResponse?: Record<string, any>;
    };
  };

  @Column({ type: 'jsonb', nullable: true })
  capabilities!: {
    operations: string[];
    dataTypes: string[];
    formats: string[];
    maxFileSize?: number;
    supportedLanguages?: string[];
    apiVersion?: string;
  };

  @Column({ type: 'jsonb', nullable: true })
  performance!: {
    totalCalls: number;
    successfulCalls: number;
    failedCalls: number;
    averageResponseTime: number;
    lastUsedAt: Date;
    uptime: number;
    errorRate: number;
    throughput: number;
  };

  @Column({ type: 'jsonb', nullable: true })
  security!: {
    encryption: boolean;
    sslVerification: boolean;
    apiKeyRotation: boolean;
    lastKeyRotation: Date;
    accessLogging: boolean;
    auditTrail: boolean;
    ipWhitelist?: string[];
    rateLimitByIp: boolean;
  };

  @Column({ type: 'jsonb', nullable: true })
  monitoring!: {
    healthCheck: {
      enabled: boolean;
      interval: number;
      timeout: number;
      threshold: number;
    };
    alerts: Array<{
      type: string;
      condition: string;
      action: string;
      recipients: string[];
    }>;
    metrics: {
      responseTime: number[];
      errorRate: number[];
      usageCount: number[];
      availability: number[];
    };
  };

  @Column({ type: 'jsonb', nullable: true })
  metadata!: {
    version: string;
    provider: string;
    documentation: string;
    changelog: Array<{
      version: string;
      date: Date;
      changes: string[];
    }>;
    tags: string[];
    category: string;
    pricing?: {
      model: 'free' | 'usage_based' | 'subscription';
      details: Record<string, any>;
    };
  };

  @Column({ type: 'jsonb', nullable: true })
  permissions!: {
    owners: string[];
    editors: string[];
    viewers: string[];
    public: boolean;
    allowedOrganizations: string[];
    allowedAgents: string[];
  };

  @Column({ type: 'jsonb', nullable: true })
  testing!: {
    testSuite: Array<{
      name: string;
      description: string;
      request: Record<string, any>;
      expectedResponse: Record<string, any>;
      timeout: number;
    }>;
    lastTestRun: Date;
    testResults: Array<{
      testName: string;
      status: 'passed' | 'failed' | 'skipped';
      duration: number;
      error?: string;
      timestamp: Date;
    }>;
  };

  @ManyToOne(() => User, user => user.tools, { onDelete: 'CASCADE' })
  user!: User;

  @Column()
  userId!: string;

  @Column()
  organizationId!: string;

  @ManyToOne(() => Agent, agent => agent.tools, { nullable: true })
  agent!: Agent;

  @Column({ nullable: true })
  agentId!: string;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  @Column({ nullable: true })
  deletedAt!: Date;
} 