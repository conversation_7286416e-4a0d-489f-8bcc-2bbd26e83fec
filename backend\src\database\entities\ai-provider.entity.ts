import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn, BaseEntity } from 'typeorm';
import { User } from '../../users/user.entity';
import { Organization } from './organization.entity';

export enum ProviderType {
  OPENAI = 'OPENAI',
  CLAUDE = 'CLAUDE',
  GEMINI = 'GEMINI',
  MISTRAL = 'MISTRAL',
  GROQ = 'GROQ',
  DEEPSEEK = 'DEEPSEEK',
  HUGGING_FACE = 'HUGGING_FACE',
  LOCAL_AI = 'LOCAL_AI',
  CUSTOM = 'CUSTOM',
  ANTHROPIC = 'ANTHROPIC',
  OLLAMA = 'OLLAMA',
  COHERE = 'COHERE',
  REPLICATE = 'REPLICATE',
  AZURE = 'AZURE',
  GOOGLE = 'GOOGLE',
  DEEPSEEK_R1 = 'DEEPSEEK_R1',
  OPENROUTER = 'OPENROUTER',
}

@Entity('ai_providers')
export class AIProvider extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  name!: string;

  @Column({
    type: 'enum',
    enum: ProviderType
  })
  type!: ProviderType;

  @Column('jsonb')
  config!: {
    apiKey?: string;
    baseUrl?: string;
    organizationId?: string;
    projectId?: string;
    region?: string;  
    customHeaders?: Record<string, string>;
    timeout?: number;
    maxRetries?: number;
    apiVersion?: string;
  
  };

  @Column('jsonb', { nullable: true })
  models!: Array<{
    id: string;
    name: string;
    description?: string;
    version: string;
    capabilities: Record<string, boolean>;
    fineTuned: boolean;
    contextLength: number;
    costPer1KTokens: {
      input: number;
      output: number;
    };
  }>;

  @Column({ default: true })
  isActive!: boolean;

  @Column('jsonb', { nullable: true })
  quotaLimits!: {
    dailyRequests?: number;
    monthlyRequests?: number;
    dailyCostCents?: number;
    monthlyCostCents?: number;
  };

  @Column('jsonb', { nullable: true })
  performanceMetrics!: {
    averageLatency: number;
    reliability: number;
    totalRequests: number;
    successfulRequests: number;
    lastHealthCheck: Date;
    healthStatus: 'healthy' | 'degraded' | 'unhealthy';
  };

  @Column('uuid')
  organizationId!: string;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'organizationId' })
  organization!: Organization;

  @Column('uuid')
  createdBy!: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'createdBy' })
  creator!: User;

  @OneToMany(() => AIModel, model => model.provider)
  aiModels!: AIModel[];

  @OneToMany(() => ProviderUsage, usage => usage.provider)
  usageRecords!: ProviderUsage[];

  @OneToMany(() => ProviderHealthCheck, healthCheck => healthCheck.provider)
  healthChecks!: ProviderHealthCheck[];

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}

@Entity('ai_models')
export class AIModel extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column('uuid')
  providerId!: string;

  @ManyToOne(() => AIProvider, provider => provider.aiModels)
  @JoinColumn({ name: 'providerId' })
  provider!: AIProvider;

  @Column()
  name!: string;

  @Column('text', { nullable: true })
  description!: string;

  @Column()
  version!: string;

  @Column('jsonb')
  capabilities!: {
    chat: boolean;
    completion: boolean;
    embedding: boolean;
    vision: boolean;
    functionCalling: boolean;
    codeGeneration: boolean;
    analysis: boolean;
    multimodal: boolean;
    streaming: boolean;
  };

  @Column({ default: false })
  fineTuned!: boolean;

  @Column('int', { default: 4096 })
  contextLength!: number;

  @Column('int', { default: 4096 })
  maxTokens!: number;

  @Column('jsonb')
  costPer1KTokens!: {
    input: number;
    output: number;
  };

  @Column('jsonb', { nullable: true })
  metadata!: {
    trainingData?: string;
    releaseDate?: Date;
    deprecated?: boolean;
    replacedBy?: string;
    tags?: string[];
  };

  @Column({ default: true })
  isActive!: boolean;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}

@Entity('provider_usage')
export class ProviderUsage extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column('uuid')
  providerId!: string;

  @ManyToOne(() => AIProvider, provider => provider.usageRecords)
  @JoinColumn({ name: 'providerId' })
  provider!: AIProvider;

  @Column('date')
  date!: Date;

  @Column('int', { default: 0 })
  requests!: number;

  @Column('int', { default: 0 })
  tokensUsed!: number;

  @Column('int', { default: 0 })
  costInCents!: number;

  @Column('int', { default: 0 })
  successfulRequests!: number;

  @Column('int', { default: 0 })
  failedRequests!: number;

  @Column('float', { default: 0 })
  averageLatency!: number;

  @Column('jsonb', { nullable: true })
  breakdown!: {
    byModel: Record<string, {
      requests: number;
      tokens: number;
      cost: number;
    }>;
    byCapability: Record<string, {
      requests: number;
      tokens: number;
      cost: number;
    }>;
  };

  @CreateDateColumn()
  createdAt!: Date;
}

@Entity('provider_health_checks')
export class ProviderHealthCheck extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column('uuid')
  providerId!: string;

  @ManyToOne(() => AIProvider)
  @JoinColumn({ name: 'providerId' })
  provider!: AIProvider;

  @Column('timestamp')
  checkedAt!: Date;

  @Column({
    type: 'enum',
    enum: ['healthy', 'degraded', 'unhealthy']
  })
  status!: 'healthy' | 'degraded' | 'unhealthy';

  @Column('int')
  responseTime!: number;

  @Column({ default: true })
  isAvailable!: boolean;

  @Column('text', { nullable: true })
  errorMessage!: string;

  @Column('jsonb', { nullable: true })
  metrics!: {
    uptime: number;
    errorRate: number;
    averageResponseTime: number;
    throughput: number;
  };

  @CreateDateColumn()
  createdAt!: Date;
}