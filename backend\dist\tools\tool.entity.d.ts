import { BaseEntity } from 'typeorm';
import { User } from '../users/user.entity';
import { Agent } from '../agents/agent.entity';
export declare enum ToolType {
    HTTP_API = "http_api",
    DATABASE = "database",
    FILE_SYSTEM = "file_system",
    EMAIL = "email",
    SMS = "sms",
    WEBHOOK = "webhook",
    CALENDAR = "calendar",
    CRM = "crm",
    PAYMENT = "payment",
    ANALYTICS = "analytics",
    AI_MODEL = "ai_model",
    CUSTOM = "custom"
}
export declare enum ToolStatus {
    DRAFT = "draft",
    ACTIVE = "active",
    INACTIVE = "inactive",
    ERROR = "error",
    DEPRECATED = "deprecated"
}
export declare enum ToolPermission {
    READ = "read",
    WRITE = "write",
    EXECUTE = "execute",
    ADMIN = "admin"
}
export declare class Tool extends BaseEntity {
    id: string;
    name: string;
    description: string;
    type: ToolType;
    status: ToolStatus;
    configuration: {
        endpoint?: string;
        method?: string;
        headers?: Record<string, string>;
        authentication: {
            type: 'none' | 'api_key' | 'oauth2' | 'bearer' | 'basic';
            credentials: Record<string, any>;
        };
        parameters: Array<{
            name: string;
            type: string;
            required: boolean;
            default?: any;
            description: string;
            validation?: Record<string, any>;
        }>;
        response: {
            format: 'json' | 'xml' | 'text' | 'binary';
            schema?: Record<string, any>;
            examples?: Array<{
                name: string;
                request: Record<string, any>;
                response: Record<string, any>;
            }>;
        };
        rateLimiting: {
            enabled: boolean;
            requestsPerMinute: number;
            burstLimit: number;
        };
        caching: {
            enabled: boolean;
            ttl: number;
            keyPattern: string;
        };
        errorHandling: {
            retryCount: number;
            retryDelay: number;
            timeout: number;
            fallbackResponse?: Record<string, any>;
        };
    };
    capabilities: {
        operations: string[];
        dataTypes: string[];
        formats: string[];
        maxFileSize?: number;
        supportedLanguages?: string[];
        apiVersion?: string;
    };
    performance: {
        totalCalls: number;
        successfulCalls: number;
        failedCalls: number;
        averageResponseTime: number;
        lastUsedAt: Date;
        uptime: number;
        errorRate: number;
        throughput: number;
    };
    security: {
        encryption: boolean;
        sslVerification: boolean;
        apiKeyRotation: boolean;
        lastKeyRotation: Date;
        accessLogging: boolean;
        auditTrail: boolean;
        ipWhitelist?: string[];
        rateLimitByIp: boolean;
    };
    monitoring: {
        healthCheck: {
            enabled: boolean;
            interval: number;
            timeout: number;
            threshold: number;
        };
        alerts: Array<{
            type: string;
            condition: string;
            action: string;
            recipients: string[];
        }>;
        metrics: {
            responseTime: number[];
            errorRate: number[];
            usageCount: number[];
            availability: number[];
        };
    };
    metadata: {
        version: string;
        provider: string;
        documentation: string;
        changelog: Array<{
            version: string;
            date: Date;
            changes: string[];
        }>;
        tags: string[];
        category: string;
        pricing?: {
            model: 'free' | 'usage_based' | 'subscription';
            details: Record<string, any>;
        };
    };
    permissions: {
        owners: string[];
        editors: string[];
        viewers: string[];
        public: boolean;
        allowedOrganizations: string[];
        allowedAgents: string[];
    };
    testing: {
        testSuite: Array<{
            name: string;
            description: string;
            request: Record<string, any>;
            expectedResponse: Record<string, any>;
            timeout: number;
        }>;
        lastTestRun: Date;
        testResults: Array<{
            testName: string;
            status: 'passed' | 'failed' | 'skipped';
            duration: number;
            error?: string;
            timestamp: Date;
        }>;
    };
    user: User;
    userId: string;
    organizationId: string;
    agent: Agent;
    agentId: string;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date;
}
