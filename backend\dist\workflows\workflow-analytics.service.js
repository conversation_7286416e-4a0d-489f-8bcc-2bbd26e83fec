"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowAnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const logger_service_1 = require("../logging/logger.service");
const redis_service_1 = require("../cache/redis.service");
let WorkflowAnalyticsService = class WorkflowAnalyticsService {
    constructor(logger, redisService) {
        this.logger = logger;
        this.redisService = redisService;
    }
    async getWorkflowAnalytics(workflow) {
        try {
            const executionHistory = workflow.executionHistory || [];
            const recentExecutions = await this.getRecentExecutions(workflow.id);
            const analytics = {
                executionMetrics: this.calculateExecutionMetrics(workflow, executionHistory, recentExecutions),
                performanceMetrics: this.calculatePerformanceMetrics(workflow, executionHistory, recentExecutions),
                costAnalysis: this.calculateCostAnalysis(workflow, executionHistory, recentExecutions),
                errorAnalysis: this.calculateErrorAnalysis(workflow, executionHistory, recentExecutions),
                optimization: this.generateOptimizationRecommendations(workflow, executionHistory, recentExecutions),
            };
            return analytics;
        }
        catch (error) {
            this.logger.error(`Failed to calculate workflow analytics: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowAnalyticsService');
            throw error;
        }
    }
    async getOrganizationAnalytics(organizationId, timeRange) {
        try {
            const workflows = await this.getWorkflowsByOrganization(organizationId);
            const analytics = {
                totalWorkflows: workflows.length,
                activeWorkflows: workflows.filter(w => w.status === 'active').length,
                totalExecutions: workflows.reduce((sum, w) => sum + w.executionCount, 0),
                totalCost: workflows.reduce((sum, w) => sum + Number(w.totalCost), 0),
                averageSuccessRate: this.calculateAverageSuccessRate(workflows),
                topPerformingWorkflows: this.getTopPerformingWorkflows(workflows),
                costTrend: await this.getOrganizationCostTrend(organizationId, timeRange),
            };
            return analytics;
        }
        catch (error) {
            this.logger.error(`Failed to calculate organization analytics: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowAnalyticsService');
            throw error;
        }
    }
    async getRealTimeMetrics(workflowId) {
        try {
            const activeExecutions = await this.getActiveExecutions(workflowId);
            const currentStep = this.getCurrentStep(activeExecutions);
            const queueLength = await this.getQueueLength(workflowId);
            return {
                activeExecutions: activeExecutions.length,
                currentStep,
                queueLength,
                lastExecution: await this.getLastExecution(workflowId),
                systemHealth: await this.getSystemHealth(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to get real-time metrics: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowAnalyticsService');
            throw error;
        }
    }
    calculateExecutionMetrics(workflow, executionHistory, recentExecutions) {
        const totalExecutions = workflow.executionCount;
        const successfulExecutions = workflow.successCount;
        const failedExecutions = workflow.failureCount;
        const successRate = totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0;
        const averageExecutionTime = Number(workflow.averageExecutionTime) || 0;
        const totalCost = Number(workflow.totalCost) || 0;
        const averageCost = totalExecutions > 0 ? totalCost / totalExecutions : 0;
        return {
            totalExecutions,
            successfulExecutions,
            failedExecutions,
            successRate,
            averageExecutionTime,
            totalCost,
            averageCost,
        };
    }
    calculatePerformanceMetrics(workflow, executionHistory, recentExecutions) {
        const stepPerformance = this.analyzeStepPerformance(executionHistory);
        const bottleneckSteps = this.identifyBottlenecks(stepPerformance);
        const throughput = this.calculateThroughput(recentExecutions);
        return {
            stepPerformance,
            bottleneckSteps,
            throughput,
        };
    }
    calculateCostAnalysis(workflow, executionHistory, recentExecutions) {
        const totalCost = Number(workflow.totalCost) || 0;
        const costPerExecution = workflow.executionCount > 0 ? totalCost / workflow.executionCount : 0;
        const costByStep = this.calculateCostByStep(executionHistory);
        const costTrend = this.calculateCostTrend(recentExecutions);
        return {
            totalCost,
            costPerExecution,
            costByStep,
            costTrend,
        };
    }
    calculateErrorAnalysis(workflow, executionHistory, recentExecutions) {
        const errorRate = workflow.executionCount > 0 ? (workflow.failureCount / workflow.executionCount) * 100 : 0;
        const commonErrors = this.analyzeCommonErrors(executionHistory);
        const errorTrend = this.calculateErrorTrend(recentExecutions);
        return {
            errorRate,
            commonErrors,
            errorTrend,
        };
    }
    generateOptimizationRecommendations(workflow, executionHistory, recentExecutions) {
        const recommendations = this.generateRecommendations(workflow, executionHistory);
        const bottlenecks = this.identifyOptimizationBottlenecks(executionHistory);
        return {
            recommendations,
            bottlenecks,
        };
    }
    analyzeStepPerformance(executionHistory) {
        const stepStats = new Map();
        for (const execution of executionHistory) {
            for (const stepResult of execution.stepResults || []) {
                const { stepId, status, duration } = stepResult;
                const stats = stepStats.get(stepId) || {
                    stepId,
                    stepName: stepId,
                    totalExecutions: 0,
                    successfulExecutions: 0,
                    failedExecutions: 0,
                    totalDuration: 0,
                };
                stats.totalExecutions++;
                if (status === 'completed') {
                    stats.successfulExecutions++;
                }
                else if (status === 'failed') {
                    stats.failedExecutions++;
                }
                stats.totalDuration += duration || 0;
                stepStats.set(stepId, stats);
            }
        }
        return Array.from(stepStats.values()).map(stats => ({
            stepId: stats.stepId,
            stepName: stats.stepName,
            averageDuration: stats.totalExecutions > 0 ? stats.totalDuration / stats.totalExecutions : 0,
            successRate: stats.totalExecutions > 0 ? (stats.successfulExecutions / stats.totalExecutions) * 100 : 0,
            failureRate: stats.totalExecutions > 0 ? (stats.failedExecutions / stats.totalExecutions) * 100 : 0,
            totalExecutions: stats.totalExecutions,
        }));
    }
    identifyBottlenecks(stepPerformance) {
        const sortedSteps = stepPerformance
            .filter(step => step.totalExecutions > 0)
            .sort((a, b) => b.averageDuration - a.averageDuration);
        const totalAverageDuration = stepPerformance.reduce((sum, step) => sum + step.averageDuration, 0);
        const averageDuration = stepPerformance.length > 0 ? totalAverageDuration / stepPerformance.length : 0;
        return sortedSteps
            .filter(step => step.averageDuration > averageDuration * 1.5)
            .map(step => ({
            stepId: step.stepId,
            stepName: step.stepName,
            averageDuration: step.averageDuration,
            impact: ((step.averageDuration - averageDuration) / averageDuration) * 100,
        }));
    }
    calculateThroughput(recentExecutions) {
        const now = new Date();
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        const executionsPerHour = recentExecutions.filter(exec => new Date(exec.startTime) > oneHourAgo).length;
        const executionsPerDay = recentExecutions.filter(exec => new Date(exec.startTime) > oneDayAgo).length;
        const hourlyStats = this.calculateHourlyStats(recentExecutions);
        const dailyStats = this.calculateDailyStats(recentExecutions);
        return {
            executionsPerHour,
            executionsPerDay,
            peakHour: hourlyStats.peakHour,
            peakDay: dailyStats.peakDay,
        };
    }
    calculateCostByStep(executionHistory) {
        const stepCosts = new Map();
        for (const execution of executionHistory) {
            for (const stepResult of execution.stepResults || []) {
                const { stepId, cost = 0 } = stepResult;
                const stats = stepCosts.get(stepId) || {
                    stepId,
                    stepName: stepId,
                    totalCost: 0,
                    executions: 0,
                };
                stats.totalCost += cost;
                stats.executions++;
                stepCosts.set(stepId, stats);
            }
        }
        return Array.from(stepCosts.values()).map(stats => ({
            stepId: stats.stepId,
            stepName: stats.stepName,
            totalCost: stats.totalCost,
            averageCost: stats.executions > 0 ? stats.totalCost / stats.executions : 0,
        }));
    }
    calculateCostTrend(recentExecutions) {
        const dailyCosts = new Map();
        for (const execution of recentExecutions) {
            const date = new Date(execution.startTime).toISOString().split('T')[0];
            const stats = dailyCosts.get(date) || { cost: 0, executions: 0 };
            stats.cost += execution.totalCost || 0;
            stats.executions++;
            dailyCosts.set(date, stats);
        }
        return Array.from(dailyCosts.entries())
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([date, stats]) => ({
            date,
            cost: stats.cost,
            executions: stats.executions,
        }));
    }
    analyzeCommonErrors(executionHistory) {
        const errorCounts = new Map();
        for (const execution of executionHistory) {
            for (const stepResult of execution.stepResults || []) {
                if (stepResult.error) {
                    const count = errorCounts.get(stepResult.error) || 0;
                    errorCounts.set(stepResult.error, count + 1);
                }
            }
        }
        const totalErrors = Array.from(errorCounts.values()).reduce((sum, count) => sum + count, 0);
        return Array.from(errorCounts.entries())
            .map(([error, count]) => ({
            error,
            count,
            percentage: totalErrors > 0 ? (count / totalErrors) * 100 : 0,
        }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);
    }
    calculateErrorTrend(recentExecutions) {
        const dailyErrors = new Map();
        for (const execution of recentExecutions) {
            const date = new Date(execution.startTime).toISOString().split('T')[0];
            const stats = dailyErrors.get(date) || { errors: 0, total: 0 };
            stats.total++;
            if (execution.status === 'failed') {
                stats.errors++;
            }
            dailyErrors.set(date, stats);
        }
        return Array.from(dailyErrors.entries())
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([date, stats]) => ({
            date,
            errors: stats.errors,
            total: stats.total,
        }));
    }
    generateRecommendations(workflow, executionHistory) {
        const recommendations = [];
        const slowSteps = executionHistory
            .flatMap(exec => exec.stepResults || [])
            .filter(step => step.duration > 5000)
            .reduce((acc, step) => {
            acc[step.stepId] = (acc[step.stepId] || 0) + 1;
            return acc;
        }, {});
        for (const [stepId, count] of Object.entries(slowSteps)) {
            if (count > 3) {
                recommendations.push({
                    type: 'performance',
                    title: 'Optimize Slow Step',
                    description: `Step ${stepId} is consistently slow (${count} occurrences). Consider optimizing or caching.`,
                    impact: 'high',
                });
            }
        }
        const expensiveSteps = executionHistory
            .flatMap(exec => exec.stepResults || [])
            .filter(step => step.cost > 1)
            .reduce((acc, step) => {
            acc[step.stepId] = (acc[step.stepId] || 0) + step.cost;
            return acc;
        }, {});
        for (const [stepId, totalCost] of Object.entries(expensiveSteps)) {
            if (totalCost > 10) {
                recommendations.push({
                    type: 'cost',
                    title: 'Reduce Step Cost',
                    description: `Step ${stepId} is expensive ($${totalCost.toFixed(2)} total). Consider alternatives or optimization.`,
                    impact: 'medium',
                    estimatedSavings: totalCost * 0.3,
                });
            }
        }
        const errorRate = workflow.failureCount / workflow.executionCount;
        if (errorRate > 0.1) {
            recommendations.push({
                type: 'reliability',
                title: 'Improve Error Handling',
                description: `High failure rate (${(errorRate * 100).toFixed(1)}%). Review error handling and retry logic.`,
                impact: 'high',
            });
        }
        return recommendations;
    }
    identifyOptimizationBottlenecks(executionHistory) {
        const bottlenecks = [];
        const stepFailures = new Map();
        for (const execution of executionHistory) {
            for (const stepResult of execution.stepResults || []) {
                const stats = stepFailures.get(stepResult.stepId) || { failures: 0, total: 0 };
                stats.total++;
                if (stepResult.status === 'failed') {
                    stats.failures++;
                }
                stepFailures.set(stepResult.stepId, stats);
            }
        }
        for (const [stepId, stats] of stepFailures) {
            const failureRate = stats.total > 0 ? stats.failures / stats.total : 0;
            if (failureRate > 0.2) {
                bottlenecks.push({
                    stepId,
                    stepName: stepId,
                    issue: `High failure rate (${(failureRate * 100).toFixed(1)}%)`,
                    recommendation: 'Review step configuration and error handling',
                });
            }
        }
        return bottlenecks;
    }
    calculateHourlyStats(executions) {
        const hourlyCounts = new Array(24).fill(0);
        for (const execution of executions) {
            const hour = new Date(execution.startTime).getHours();
            hourlyCounts[hour]++;
        }
        const peakHour = hourlyCounts.indexOf(Math.max(...hourlyCounts));
        return { peakHour: `${peakHour}:00` };
    }
    calculateDailyStats(executions) {
        const dailyCounts = new Array(7).fill(0);
        for (const execution of executions) {
            const day = new Date(execution.startTime).getDay();
            dailyCounts[day]++;
        }
        const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const peakDay = days[dailyCounts.indexOf(Math.max(...dailyCounts))];
        return { peakDay };
    }
    async getRecentExecutions(workflowId) {
        return [];
    }
    async getWorkflowsByOrganization(organizationId) {
        return [];
    }
    calculateAverageSuccessRate(workflows) {
        const totalExecutions = workflows.reduce((sum, w) => sum + w.executionCount, 0);
        const totalSuccesses = workflows.reduce((sum, w) => sum + w.successCount, 0);
        return totalExecutions > 0 ? (totalSuccesses / totalExecutions) * 100 : 0;
    }
    getTopPerformingWorkflows(workflows) {
        return workflows
            .filter(w => w.executionCount > 0)
            .map(w => ({
            id: w.id,
            name: w.name,
            successRate: (w.successCount / w.executionCount) * 100,
            averageExecutionTime: Number(w.averageExecutionTime),
        }))
            .sort((a, b) => b.successRate - a.successRate)
            .slice(0, 5);
    }
    async getOrganizationCostTrend(organizationId, timeRange) {
        return [];
    }
    async getActiveExecutions(workflowId) {
        return [];
    }
    getCurrentStep(activeExecutions) {
        return activeExecutions.length > 0 ? activeExecutions[0].currentStep : null;
    }
    async getQueueLength(workflowId) {
        return 0;
    }
    async getLastExecution(workflowId) {
        return null;
    }
    async getSystemHealth() {
        return { status: 'healthy', uptime: 99.9 };
    }
};
exports.WorkflowAnalyticsService = WorkflowAnalyticsService;
exports.WorkflowAnalyticsService = WorkflowAnalyticsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [logger_service_1.CustomLoggerService,
        redis_service_1.RedisService])
], WorkflowAnalyticsService);
//# sourceMappingURL=workflow-analytics.service.js.map