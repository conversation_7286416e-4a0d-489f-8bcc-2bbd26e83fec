"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowsController = void 0;
const common_1 = require("@nestjs/common");
const workflows_service_1 = require("./workflows.service");
const workflow_execution_service_1 = require("./workflow-execution.service");
const workflow_analytics_service_1 = require("./workflow-analytics.service");
const workflow_dto_1 = require("./dto/workflow.dto");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const permission_guard_1 = require("../auth/permission.guard");
const permissions_decorator_1 = require("../auth/permissions.decorator");
const swagger_1 = require("@nestjs/swagger");
const workflow_entity_1 = require("./workflow.entity");
let WorkflowsController = class WorkflowsController {
    constructor(workflowsService, executionService, analyticsService) {
        this.workflowsService = workflowsService;
        this.executionService = executionService;
        this.analyticsService = analyticsService;
    }
    async createWorkflow(createWorkflowDto, req) {
        return this.workflowsService.createWorkflow(createWorkflowDto, req.user.userId, req.user.organizationId);
    }
    async getWorkflows(filters, req) {
        return this.workflowsService.getWorkflows(filters, req.user.userId, req.user.organizationId);
    }
    async getWorkflowById(id, req) {
        return this.workflowsService.getWorkflowById(id, req.user.userId, req.user.organizationId);
    }
    async updateWorkflow(id, updateWorkflowDto, req) {
        return this.workflowsService.updateWorkflow(id, updateWorkflowDto, req.user.userId, req.user.organizationId);
    }
    async deleteWorkflow(id, req) {
        return this.workflowsService.deleteWorkflow(id, req.user.userId, req.user.organizationId);
    }
    async changeWorkflowStatus(id, body, req) {
        return this.workflowsService.changeWorkflowStatus(id, body.status, req.user.userId, req.user.organizationId);
    }
    async duplicateWorkflow(id, req) {
        return this.workflowsService.duplicateWorkflow(id, req.user.userId, req.user.organizationId);
    }
    async executeWorkflow(id, body, req) {
        const workflow = await this.workflowsService.getWorkflowById(id, req.user.userId, req.user.organizationId);
        const executionId = await this.executionService.executeWorkflow(workflow, req.user.userId, body.inputVariables);
        return {
            executionId,
            message: 'Workflow execution started',
            status: 'started',
        };
    }
    async getExecutionStatus(id, executionId, req) {
        await this.workflowsService.getWorkflowById(id, req.user.userId, req.user.organizationId);
        return this.executionService.getExecutionStatus(executionId);
    }
    async pauseExecution(id, executionId, req) {
        await this.workflowsService.getWorkflowById(id, req.user.userId, req.user.organizationId);
        await this.executionService.pauseExecution(executionId);
        return { message: 'Execution paused successfully' };
    }
    async resumeExecution(id, executionId, req) {
        await this.workflowsService.getWorkflowById(id, req.user.userId, req.user.organizationId);
        await this.executionService.resumeExecution(executionId);
        return { message: 'Execution resumed successfully' };
    }
    async cancelExecution(id, executionId, req) {
        await this.workflowsService.getWorkflowById(id, req.user.userId, req.user.organizationId);
        await this.executionService.cancelExecution(executionId);
        return { message: 'Execution cancelled successfully' };
    }
    async getWorkflowAnalytics(id, req) {
        return this.workflowsService.getWorkflowAnalytics(id, req.user.userId, req.user.organizationId);
    }
    async getRealTimeMetrics(id, req) {
        await this.workflowsService.getWorkflowById(id, req.user.userId, req.user.organizationId);
        return this.analyticsService.getRealTimeMetrics(id);
    }
    async getOrganizationAnalytics(startDate, endDate, req) {
        const timeRange = {
            start: startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            end: endDate ? new Date(endDate) : new Date(),
        };
        return this.analyticsService.getOrganizationAnalytics(req.user.organizationId, timeRange);
    }
    async validateWorkflow(id, req) {
        const workflow = await this.workflowsService.getWorkflowById(id, req.user.userId, req.user.organizationId);
        return {
            valid: true,
            message: 'Workflow definition is valid',
            workflow: {
                id: workflow.id,
                name: workflow.name,
                steps: workflow.definition.steps.length,
                triggers: workflow.definition.triggers.length,
            },
        };
    }
    async exportWorkflow(id, req) {
        const workflow = await this.workflowsService.getWorkflowById(id, req.user.userId, req.user.organizationId);
        return {
            id: workflow.id,
            name: workflow.name,
            description: workflow.description,
            definition: workflow.definition,
            metadata: workflow.metadata,
            version: '1.0',
            exportedAt: new Date().toISOString(),
        };
    }
    async importWorkflow(importData, req) {
        const createWorkflowDto = {
            name: importData.name,
            description: importData.description,
            definition: importData.definition,
            metadata: importData.metadata,
        };
        return this.workflowsService.createWorkflow(createWorkflowDto, req.user.userId, req.user.organizationId);
    }
};
exports.WorkflowsController = WorkflowsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('workflows.write'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new workflow' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Workflow created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid workflow definition' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [workflow_dto_1.CreateWorkflowDto, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "createWorkflow", null);
__decorate([
    (0, common_1.Get)(),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('workflows.read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get workflows with filtering and pagination' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Workflows retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: 'Search term' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: workflow_entity_1.WorkflowStatus, description: 'Filter by status' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: 'Page number' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: 'Items per page' }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [workflow_dto_1.WorkflowFiltersDto, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "getWorkflows", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('workflows.read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get workflow by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Workflow retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Workflow not found' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Workflow ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "getWorkflowById", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('workflows.write'),
    (0, swagger_1.ApiOperation)({ summary: 'Update workflow' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Workflow updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Workflow not found' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Workflow ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, workflow_dto_1.UpdateWorkflowDto, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "updateWorkflow", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('workflows.write'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete workflow' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Workflow deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Workflow not found' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Workflow ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "deleteWorkflow", null);
__decorate([
    (0, common_1.Put)(':id/status'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('workflows.write'),
    (0, swagger_1.ApiOperation)({ summary: 'Change workflow status' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Workflow status changed successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid status transition' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Workflow ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "changeWorkflowStatus", null);
__decorate([
    (0, common_1.Post)(':id/duplicate'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('workflows.write'),
    (0, swagger_1.ApiOperation)({ summary: 'Duplicate workflow' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Workflow duplicated successfully' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Workflow ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "duplicateWorkflow", null);
__decorate([
    (0, common_1.Post)(':id/execute'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('workflows.execute'),
    (0, swagger_1.ApiOperation)({ summary: 'Execute workflow' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Workflow execution started' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid workflow or execution parameters' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Workflow ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "executeWorkflow", null);
__decorate([
    (0, common_1.Get)(':id/executions/:executionId'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('workflows.read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get workflow execution status' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Execution status retrieved' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Workflow ID' }),
    (0, swagger_1.ApiParam)({ name: 'executionId', description: 'Execution ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('executionId')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "getExecutionStatus", null);
__decorate([
    (0, common_1.Put)(':id/executions/:executionId/pause'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('workflows.execute'),
    (0, swagger_1.ApiOperation)({ summary: 'Pause workflow execution' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Execution paused successfully' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Workflow ID' }),
    (0, swagger_1.ApiParam)({ name: 'executionId', description: 'Execution ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('executionId')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "pauseExecution", null);
__decorate([
    (0, common_1.Put)(':id/executions/:executionId/resume'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('workflows.execute'),
    (0, swagger_1.ApiOperation)({ summary: 'Resume workflow execution' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Execution resumed successfully' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Workflow ID' }),
    (0, swagger_1.ApiParam)({ name: 'executionId', description: 'Execution ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('executionId')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "resumeExecution", null);
__decorate([
    (0, common_1.Delete)(':id/executions/:executionId'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('workflows.execute'),
    (0, swagger_1.ApiOperation)({ summary: 'Cancel workflow execution' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Execution cancelled successfully' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Workflow ID' }),
    (0, swagger_1.ApiParam)({ name: 'executionId', description: 'Execution ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('executionId')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "cancelExecution", null);
__decorate([
    (0, common_1.Get)(':id/analytics'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('workflows.read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get workflow analytics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Analytics retrieved successfully' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Workflow ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "getWorkflowAnalytics", null);
__decorate([
    (0, common_1.Get)(':id/analytics/realtime'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('workflows.read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get real-time workflow metrics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Real-time metrics retrieved' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Workflow ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "getRealTimeMetrics", null);
__decorate([
    (0, common_1.Get)('organization/analytics'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('workflows.read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get organization workflow analytics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Organization analytics retrieved' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: false, description: 'Start date (ISO string)' }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: false, description: 'End date (ISO string)' }),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "getOrganizationAnalytics", null);
__decorate([
    (0, common_1.Post)(':id/validate'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('workflows.write'),
    (0, swagger_1.ApiOperation)({ summary: 'Validate workflow definition' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Workflow validation completed' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Workflow validation failed' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Workflow ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "validateWorkflow", null);
__decorate([
    (0, common_1.Get)(':id/export'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('workflows.read'),
    (0, swagger_1.ApiOperation)({ summary: 'Export workflow definition' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Workflow exported successfully' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Workflow ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "exportWorkflow", null);
__decorate([
    (0, common_1.Post)('import'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    (0, permissions_decorator_1.RequirePermissions)('workflows.write'),
    (0, swagger_1.ApiOperation)({ summary: 'Import workflow definition' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Workflow imported successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid workflow definition' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "importWorkflow", null);
exports.WorkflowsController = WorkflowsController = __decorate([
    (0, swagger_1.ApiTags)('workflows'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('api/workflows'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [workflows_service_1.WorkflowsService,
        workflow_execution_service_1.WorkflowExecutionService,
        workflow_analytics_service_1.WorkflowAnalyticsService])
], WorkflowsController);
//# sourceMappingURL=workflows.controller.js.map