import { apiClient } from './api-client';
import { AIProvider } from './api/ai-provider-api';

export interface User {
  id: string;
  email: string;
  name: string;
  systemRole: 'SUPER_ADMIN' | 'ORG_ADMIN' | 'DEVELOPER' | 'VIEWER';
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  organization: {
    id: string;
    name: string;
    slug: string;
  };
  userRoles: Array<{
    role: {
      id: string;
      name: string;
      description?: string;
    };
  }>;
  userPermissions: Array<{
    permission: {
      id: string;
      name: string;
      description?: string;
    };
  }>;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  isSystem: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  rolePermissions: Array<{
    permission: {
      id: string;
      name: string;
      description?: string;
      resource: string;
      action: string;
    };
  }>;
  userRoles: Array<{
    user: {
      id: string;
      name: string;
      email: string;
    };
  }>;
}

export interface Permission {
  id: string;
  name: string;
  description?: string;
  resource: string;
  action: string;
  isSystem: boolean;
  createdAt: string;
  updatedAt: string;
  rolePermissions: Array<{
    role: {
      id: string;
      name: string;
    };
  }>;
  userPermissions: Array<{
    user: {
      id: string;
      name: string;
      email: string;
    };
  }>;
}

export interface Organization {
  id: string;
  name: string;
  slug: string;
  domain?: string;
  status: 'ACTIVE' | 'SUSPENDED' | 'DELETED';
  isActive: boolean;
  settings: any;
  quotas: any;
  billing: any;
  branding: any;
  createdAt: string;
  updatedAt: string;
  users: Array<{
    id: string;
    name: string;
    email: string;
    systemRole: string;
    isActive: boolean;
  }>;
  _count: {
    users: number;
    agents: number;
    tools: number;
    workflows: number;
  };
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

class AdminApiClient {
  // User Management
  async getUsers(params?: {
    search?: string;
    role?: string;
    isActive?: boolean;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }

    return apiClient.get(`/api/users?${queryParams}`) as Promise<User[]>;
  }

  async getUserById(id: string) {
    return apiClient.get(`/api/users/${id}`) as Promise<User>;
  }

  async createUser(data: {
    email: string;
    name: string;
    password?: string;
    systemRole?: string;
    roleIds?: string[];
    permissionIds?: string[];
  }) {
    return apiClient.post('/api/users', data) as Promise<User>;
  }

  async updateUser(id: string, data: {
    name?: string;
    email?: string;
    systemRole?: string;
    isActive?: boolean;
    roleIds?: string[];
    permissionIds?: string[];
  }) {
    return apiClient.put(`/api/users/${id}`, data) as Promise<User>;
  }

  async deleteUser(id: string) {
    return apiClient.delete(`/api/users/${id}`) as Promise<void>;
  }

  async bulkUpdateUsers(userIds: string[], updateData: any) {
    return apiClient.put('/api/users/bulk-update', { userIds, updateData }) as Promise<User[]>;
  }

  async resetUserPassword(id: string, newPassword: string) {
    return apiClient.put(`/api/users/${id}/reset-password`, { newPassword }) as Promise<User>;
  }

  // Role Management
  async getRoles(params?: {
    search?: string;
    isActive?: boolean;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }

    return apiClient.get(`/api/roles?${queryParams}`) as Promise<Role[]>;
  }

  async getRoleById(id: string) {
    return apiClient.get(`/api/roles/${id}`) as Promise<Role>;
  }

  async createRole(data: {
    name: string;
    description?: string;
    permissionIds?: string[];
  }) {
    return apiClient.post('/api/roles', data) as Promise<Role>;
  }

  async updateRole(id: string, data: {
    name?: string;
    description?: string;
    isActive?: boolean;
    permissionIds?: string[];
  }) {
    return apiClient.put(`/api/roles/${id}`, data) as Promise<Role>;
  }

  async deleteRole(id: string) {
    return apiClient.delete(`/api/roles/${id}`) as Promise<void>;
  }

  async assignRoleToUser(roleId: string, userId: string) {
    return apiClient.post(`/api/roles/${roleId}/users/${userId}`, {}) as Promise<void>;
  }

  async removeRoleFromUser(roleId: string, userId: string) {
    return apiClient.delete(`/api/roles/${roleId}/users/${userId}`) as Promise<void>;
  }

  // Permission Management
  async getPermissions(params?: {
    search?: string;
    resource?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }

    return apiClient.get(`/api/permissions?${queryParams}`) as Promise<Permission[]>;
  }

  async getPermissionById(id: string) {
    return apiClient.get(`/api/permissions/${id}`) as Promise<Permission>;
  }

  async createPermission(data: {
    name: string;
    description?: string;
    resource: string;
    action: string;
  }) {
    return apiClient.post('/api/permissions', data) as Promise<Permission>;
  }

  async updatePermission(id: string, data: {
    name?: string;
    description?: string;
    resource?: string;
    action?: string;
  }) {
    return apiClient.put(`/api/permissions/${id}`, data) as Promise<Permission>;
  }

  async deletePermission(id: string) {
    return apiClient.delete(`/api/permissions/${id}`) as Promise<void>;
  }

  async getResources() {
    return apiClient.get('/api/permissions/resources') as Promise<string[]>;
  }

  async getActions(resource?: string) {
    const params = resource ? `?resource=${resource}` : '';
    return apiClient.get(`/api/permissions/actions${params}`) as Promise<string[]>;
  }

  async assignPermissionToUser(permissionId: string, userId: string) {
    return apiClient.post(`/api/permissions/${permissionId}/users/${userId}`, {}) as Promise<void>;
  }

  async removePermissionFromUser(permissionId: string, userId: string) {
    return apiClient.delete(`/api/permissions/${permissionId}/users/${userId}`) as Promise<void>;
  }

  // Organization Management
  async getOrganizations(params?: {
    search?: string;
    status?: string;
    isActive?: boolean;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }

    return apiClient.get(`/api/organizations?${queryParams}`) as Promise<Organization[]>;
  }

  async getCurrentOrganization() {
    return apiClient.get('/api/organizations/current') as Promise<Organization>;
  }

  async getCurrentOrganizationStats() {
    return apiClient.get('/api/organizations/current/stats') as Promise<Organization>;
  }

  async getOrganizationById(id: string) {
    return apiClient.get(`/api/organizations/${id}`) as Promise<Organization>;
  }

  async createOrganization(data: {
    name: string;
    slug: string;
    domain?: string;
    settings?: any;
    quotas?: any;
    billing?: any;
    branding?: any;
  }) {
    return apiClient.post('/api/organizations', data) as Promise<Organization>;
  }

  async updateOrganization(id: string, data: {
    name?: string;
    slug?: string;
    domain?: string;
    settings?: any;
    quotas?: any;
    billing?: any;
    branding?: any;
    status?: string;
    isActive?: boolean;
  }) {
    return apiClient.put(`/api/organizations/${id}`, data) as Promise<Organization>;
  }

  async deleteOrganization(id: string) {
    return apiClient.delete(`/api/organizations/${id}`) as Promise<void>;
  }

  async suspendOrganization(id: string, reason?: string) {
    return apiClient.put(`/api/organizations/${id}/suspend`, { reason }) as Promise<void>;
  }

  async activateOrganization(id: string) {
    return apiClient.put(`/api/organizations/${id}/activate`, {}) as Promise<void>;
  }

  // Provider Management
  async getProviders(params?: {
    search?: string;
    isActive?: boolean;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }

    return apiClient.get(`/api/providers?${queryParams}`) as Promise<AIProvider[]>;
  }

  async getProviderById(id: string) { 
    return apiClient.get(`/api/providers/${id}`) as Promise<AIProvider>;
  }

  async createProvider(data: {
    name: string;
    description?: string;
    isActive?: boolean;
  }) {
    return apiClient.post('/api/providers', data) as Promise<AIProvider>;
  }

  async updateProvider(id: string, data: {
    name?: string;
    description?: string;
    isActive?: boolean;
  }) {
    return apiClient.put(`/api/providers/${id}`, data) as Promise<AIProvider>;
  }
}

export const adminApiClient = new AdminApiClient();