"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WorkflowRuntimeService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowRuntimeService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const prisma_service_1 = require("../../prisma/prisma.service");
const logger_service_1 = require("../../common/services/logger.service");
const agent_runtime_service_1 = require("../../agents/execution/agent-runtime.service");
const tool_runtime_service_1 = require("../../tools/execution/tool-runtime.service");
let WorkflowRuntimeService = WorkflowRuntimeService_1 = class WorkflowRuntimeService {
    constructor(prisma, configService, loggerService, agentRuntime, toolRuntime) {
        this.prisma = prisma;
        this.configService = configService;
        this.loggerService = loggerService;
        this.agentRuntime = agentRuntime;
        this.toolRuntime = toolRuntime;
        this.logger = new common_1.Logger(WorkflowRuntimeService_1.name);
    }
    async executeWorkflow(context) {
        const startTime = Date.now();
        const steps = [];
        try {
            const workflow = await this.loadWorkflow(context.workflowId);
            if (!workflow) {
                throw new Error(`Workflow ${context.workflowId} not found`);
            }
            const executionContext = Object.assign(Object.assign({}, context), { variables: Object.assign(Object.assign(Object.assign({}, workflow.variables), context.variables), context.input) });
            const execution = await this.createExecutionRecord(context);
            const result = await this.executeWorkflowNodes(workflow, executionContext, steps);
            await this.updateExecutionRecord(execution.id, {
                status: result.success ? 'COMPLETED' : 'FAILED',
                output: result.output,
                steps,
                duration: Date.now() - startTime,
                error: result.error,
            });
            return Object.assign(Object.assign({}, result), { duration: Date.now() - startTime, steps });
        }
        catch (error) {
            const duration = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.loggerService.error(`Workflow execution failed: ${errorMessage}`, {
                workflowId: context.workflowId,
                error: errorMessage,
                duration,
            });
            return {
                success: false,
                output: {},
                duration,
                steps,
                error: errorMessage,
            };
        }
    }
    async loadWorkflow(workflowId) {
        const workflow = await this.prisma.workflow.findUnique({
            where: { id: workflowId },
        });
        if (!workflow)
            return null;
        return {
            id: workflow.id,
            name: workflow.name,
            nodes: workflow.nodes,
            edges: workflow.edges,
            variables: workflow.variables,
            settings: workflow.settings,
        };
    }
    async executeWorkflowNodes(workflow, context, steps) {
        const startNode = this.findStartNode(workflow);
        if (!startNode) {
            throw new Error('No start node found in workflow');
        }
        const executionState = {
            variables: Object.assign({}, context.variables),
            completedNodes: new Set(),
            nodeOutputs: new Map(),
        };
        const success = await this.executeNode(startNode, workflow, context, executionState, steps);
        return {
            success,
            output: executionState.variables,
            error: success ? undefined : 'Workflow execution failed',
        };
    }
    findStartNode(workflow) {
        const nodesWithIncomingEdges = new Set(workflow.edges.map(edge => edge.target));
        return workflow.nodes.find(node => !nodesWithIncomingEdges.has(node.id)) || null;
    }
    async executeNode(node, workflow, context, state, steps) {
        if (state.completedNodes.has(node.id)) {
            return true;
        }
        const stepStartTime = Date.now();
        try {
            let stepResult;
            let success = true;
            switch (node.type) {
                case 'agent':
                    stepResult = await this.executeAgentNode(node, context, state);
                    break;
                case 'tool':
                    stepResult = await this.executeToolNode(node, context, state);
                    break;
                case 'condition':
                    stepResult = await this.executeConditionNode(node, context, state);
                    break;
                case 'parallel':
                    stepResult = await this.executeParallelNode(node, workflow, context, state, steps);
                    break;
                case 'delay':
                    stepResult = await this.executeDelayNode(node, context, state);
                    break;
                default:
                    throw new Error(`Unsupported node type: ${node.type}`);
            }
            steps.push({
                nodeId: node.id,
                type: node.type,
                success,
                output: stepResult,
                duration: Date.now() - stepStartTime,
            });
            state.completedNodes.add(node.id);
            state.nodeOutputs.set(node.id, stepResult);
            if (typeof stepResult === 'object' && stepResult !== null) {
                state.variables = Object.assign(Object.assign({}, state.variables), stepResult);
            }
            const nextNodes = this.getNextNodes(node.id, workflow, state);
            for (const nextNode of nextNodes) {
                const nextSuccess = await this.executeNode(nextNode, workflow, context, state, steps);
                if (!nextSuccess) {
                    success = false;
                }
            }
            return success;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            steps.push({
                nodeId: node.id,
                type: node.type,
                success: false,
                output: null,
                duration: Date.now() - stepStartTime,
                error: errorMessage,
            });
            return false;
        }
    }
    async executeAgentNode(node, context, state) {
        const agentId = node.config.agentId;
        const input = this.resolveVariables(node.config.input || '{{input}}', state.variables);
        const result = await this.agentRuntime.executeAgent({
            agentId,
            sessionId: context.sessionId,
            input,
            variables: state.variables,
        });
        if (!result.success) {
            throw new Error(result.error || 'Agent execution failed');
        }
        return { [node.config.outputVariable || 'output']: result.output };
    }
    async executeToolNode(node, context, state) {
        const toolId = node.config.toolId;
        const parameters = this.resolveVariables(node.config.parameters || {}, state.variables);
        const result = await this.toolRuntime.executeTool({
            toolId,
            agentId: context.workflowId,
            sessionId: context.sessionId,
            parameters,
        });
        if (!result.success) {
            throw new Error(result.error || 'Tool execution failed');
        }
        return { [node.config.outputVariable || 'output']: result.output };
    }
    async executeConditionNode(node, context, state) {
        const condition = node.config.condition;
        const result = this.evaluateCondition(condition, state.variables);
        return { condition_result: result };
    }
    async executeParallelNode(node, workflow, context, state, steps) {
        const parallelBranches = node.config.branches || [];
        const promises = parallelBranches.map(async (branchId) => {
            const branchNode = workflow.nodes.find(n => n.id === branchId);
            if (branchNode) {
                return await this.executeNode(branchNode, workflow, context, state, steps);
            }
            return false;
        });
        const results = await Promise.all(promises);
        return { parallel_results: results };
    }
    async executeDelayNode(node, context, state) {
        const delay = node.config.delay || 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        return { delayed: true };
    }
    getNextNodes(nodeId, workflow, state) {
        const outgoingEdges = workflow.edges.filter(edge => edge.source === nodeId);
        const nextNodes = [];
        for (const edge of outgoingEdges) {
            if (edge.condition) {
                const conditionResult = this.evaluateCondition(edge.condition, state.variables);
                if (!conditionResult) {
                    continue;
                }
            }
            const nextNode = workflow.nodes.find(node => node.id === edge.target);
            if (nextNode) {
                nextNodes.push(nextNode);
            }
        }
        return nextNodes;
    }
    resolveVariables(template, variables) {
        if (typeof template === 'string') {
            return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
                return variables[key] !== undefined ? variables[key] : match;
            });
        }
        if (typeof template === 'object' && template !== null) {
            const resolved = {};
            for (const [key, value] of Object.entries(template)) {
                resolved[key] = this.resolveVariables(value, variables);
            }
            return resolved;
        }
        return template;
    }
    evaluateCondition(condition, variables) {
        try {
            const resolvedCondition = condition.replace(/\{\{(\w+)\}\}/g, (match, key) => {
                const value = variables[key];
                return typeof value === 'string' ? `"${value}"` : String(value);
            });
            return Function(`"use strict"; return (${resolvedCondition})`)();
        }
        catch (error) {
            this.logger.warn(`Failed to evaluate condition: ${condition}`, error);
            return false;
        }
    }
    async createExecutionRecord(context) {
        return await this.prisma.workflowExecution.create({
            data: {
                workflowId: context.workflowId,
                input: context.input,
                status: 'RUNNING',
                userId: context.userId,
                organizationId: context.organizationId,
            },
        });
    }
    async updateExecutionRecord(executionId, data) {
        await this.prisma.workflowExecution.update({
            where: { id: executionId },
            data,
        });
    }
};
exports.WorkflowRuntimeService = WorkflowRuntimeService;
exports.WorkflowRuntimeService = WorkflowRuntimeService = WorkflowRuntimeService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        config_1.ConfigService,
        logger_service_1.LoggerService,
        agent_runtime_service_1.AgentRuntimeService,
        tool_runtime_service_1.ToolRuntimeService])
], WorkflowRuntimeService);
//# sourceMappingURL=workflow-runtime.service.js.map