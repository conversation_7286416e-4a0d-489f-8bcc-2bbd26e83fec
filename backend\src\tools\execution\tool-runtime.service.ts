import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { LoggerService } from '../../common/services/logger.service';
import axios from 'axios';
import { Tool } from '../../database/entities/tool.entity';

export interface ToolExecutionContext {
  toolId: string;
  agentId?: string;
  sessionId?: string;
  parameters: Record<string, any>;
  timeout?: number;
  retryCount?: number;
}

export interface ToolExecutionResult {
  success: boolean;
  output: any;
  duration: number;
  error?: string;
  metadata?: Record<string, any>;
}

export interface ToolDefinition {
  id: string;
  name: string;
  type: 'API' | 'WEBHOOK' | 'FUNCTION' | 'DATABASE';
  config: {
    endpoint?: string;
    method?: string;
    headers?: Record<string, string>;
    authentication?: {
      type: 'bearer' | 'api_key' | 'basic';
      credentials: Record<string, string>;
    };
    parameters?: Array<{
      name: string;
      type: string;
      required: boolean;
      description: string;
    }>;
    timeout?: number;
    retries?: number;
  };
  schema: {
    input: Record<string, any>;
    output: Record<string, any>;
  };
}

@Injectable()
export class ToolRuntimeService {
  private readonly logger = new Logger(ToolRuntimeService.name);

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private loggerService: LoggerService,
  ) {}

  async executeTool(context: ToolExecutionContext): Promise<ToolExecutionResult> {
    const startTime = Date.now();

    try {
      // 1. Load tool definition
      const tool = await this.loadTool(context.toolId);
      if (!tool) {
        throw new Error(`Tool ${context.toolId} not found`);
      }

      // 2. Validate parameters
      this.validateParameters(tool, context.parameters);

      // 3. Execute based on tool type
      const result = await this.executeByType(tool, context);

      // 4. Log execution
      const duration = Date.now() - startTime;
      await this.logExecution(context, { ...result, duration } as ToolExecutionResult, duration);

      return {
        ...result,
        duration,
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      this.loggerService.error(`Tool execution failed: ${errorMessage}`, error instanceof Error ? error.stack : undefined);

      return {
        success: false,
        output: null,
        duration,
        error: errorMessage,
      };
    }
  }

  private async loadTool(toolId: string): Promise<ToolDefinition | null> {
    const tool = await this.prisma.tool.findUnique({
      where: { id: toolId },
    });

    if (!tool) return null;

    return {
      id: tool.id,
      name: tool.name,
      type: tool.type,
      config: tool.config,
      schema: tool.schema,
    };
  }

  private validateParameters(tool: ToolDefinition, parameters: Record<string, any>) {
    const requiredParams = tool.config.parameters?.filter(p => p.required) || [];
    
    for (const param of requiredParams) {
      if (!(param.name in parameters)) {
        throw new Error(`Missing required parameter: ${param.name}`);
      }
    }

    // Type validation could be added here
    for (const [key, value] of Object.entries(parameters)) {
      const paramDef = tool.config.parameters?.find(p => p.name === key);
      if (paramDef) {
        this.validateParameterType(key, value, paramDef.type);
      }
    }
  }

  private validateParameterType(name: string, value: any, expectedType: string) {
    switch (expectedType) {
      case 'string':
        if (typeof value !== 'string') {
          throw new Error(`Parameter ${name} must be a string`);
        }
        break;
      case 'number':
        if (typeof value !== 'number') {
          throw new Error(`Parameter ${name} must be a number`);
        }
        break;
      case 'boolean':
        if (typeof value !== 'boolean') {
          throw new Error(`Parameter ${name} must be a boolean`);
        }
        break;
      case 'array':
        if (!Array.isArray(value)) {
          throw new Error(`Parameter ${name} must be an array`);
        }
        break;
      case 'object':
        if (typeof value !== 'object' || value === null) {
          throw new Error(`Parameter ${name} must be an object`);
        }
        break;
    }
  }

  private async executeByType(tool: ToolDefinition, context: ToolExecutionContext): Promise<Omit<ToolExecutionResult, 'duration'>> {
    switch (tool.type) {
      case 'API':
        return await this.executeApiTool(tool, context);
      case 'WEBHOOK':
        return await this.executeWebhookTool(tool, context);
      case 'FUNCTION':
        return await this.executeFunctionTool(tool, context);
      case 'DATABASE':
        return await this.executeDatabaseTool(tool, context);
      default:
        throw new Error(`Unsupported tool type: ${tool.type}`);
    }
  }

  private async executeApiTool(tool: ToolDefinition, context: ToolExecutionContext): Promise<Omit<ToolExecutionResult, 'duration'>> {
    const { config } = tool;
    const timeout = context.timeout || config.timeout || 30000;

    try {
      // Prepare headers
      const headers = { ...config.headers };
      
      // Add authentication
      if (config.authentication) {
        switch (config.authentication.type) {
          case 'bearer':
            headers['Authorization'] = `Bearer ${config.authentication.credentials.token}`;
            break;
          case 'api_key':
            headers[config.authentication.credentials.header || 'X-API-Key'] = config.authentication.credentials.key;
            break;
          case 'basic':
            const auth = Buffer.from(`${config.authentication.credentials.username}:${config.authentication.credentials.password}`).toString('base64');
            headers['Authorization'] = `Basic ${auth}`;
            break;
        }
      }

      // Make the API call
      const response = await axios({
        method: config.method || 'POST',
        url: config.endpoint,
        data: context.parameters,
        headers,
        timeout,
      });

      return {
        success: true,
        output: response.data,
        metadata: {
          status: response.status,
          headers: response.headers,
        },
      };

    } catch (error: any) {
      throw new Error(`API call failed: ${error.message}`);
    }
  }

  private async executeWebhookTool(tool: ToolDefinition, context: ToolExecutionContext): Promise<Omit<ToolExecutionResult, 'duration'>> {
    // Similar to API tool but with webhook-specific logic
    return await this.executeApiTool(tool, context);
  }

  private async executeFunctionTool(tool: ToolDefinition, context: ToolExecutionContext): Promise<Omit<ToolExecutionResult, 'duration'>> {
    // Execute custom function tools
    const functionName = tool.config.endpoint; // Function name stored in endpoint
    
    try {
      const result = await this.executeCustomFunction(functionName || '', context.parameters);
      
      return {
        success: true,
        output: result,
      };

    } catch (error: any) {
      throw new Error(`Function execution failed: ${error.message}`);
    }
  }

  private async executeDatabaseTool(tool: ToolDefinition, context: ToolExecutionContext): Promise<Omit<ToolExecutionResult, 'duration'>> {
    const query = tool.config.endpoint;
    
    try {
      const result = await this.executeDatabaseQuery(context, query || '', context.parameters);
      
      return {
        success: true,
        output: result,
      };

    } catch (error: any) {
      throw new Error(`Database query failed: ${error.message}`);
    }
  }

  private async executeCustomFunction(functionName: string, parameters: Record<string, any>): Promise<any> {
    const functions: Record<string, Function> = {
      'math.add': (params: any) => params.a + params.b,
      'math.multiply': (params: any) => params.a * params.b,
      'string.uppercase': (params: any) => params.text.toUpperCase(),
      'string.lowercase': (params: any) => params.text.toLowerCase(),
      'date.now': () => new Date().toISOString(),
      'uuid.generate': () => require('crypto').randomUUID(),
    };

    const func = functions[functionName];
    if (!func) {
      throw new Error(`Function ${functionName} not found`);
    }

    return func(parameters);
  }

  private async executeDatabaseQuery(context: ToolExecutionContext, query: string, parameters: Record<string, any>): Promise<any> {
    try {
      await this.prisma.toolExecution.create({
        data: {
          toolId: context.toolId,
          agentId: context.agentId,
          sessionId: context.sessionId,
          parameters: parameters,
          output: 'result',
          success: true,
          duration: 1000,
          errorMessage: null,
          metadata: {},
        },
      });
    } catch (error) {
      throw new Error(`Database query failed: ${error instanceof Error ? error.message : String(error)}`);
    }

    return {
      rows: [
        { id: 1, name: 'Sample Data', value: parameters.value || 'default' },
      ],
      count: 1,
    };
  }

  private async logExecution(context: ToolExecutionContext, result: ToolExecutionResult, duration: number) {
    try {
      await this.prisma.toolExecution.create({
        data: {
          toolId: context.toolId,
          agentId: context.agentId,
          sessionId: context.sessionId,
          parameters: context.parameters,
          output: result.output,
          success: result.success,
          duration,
          errorMessage: result.error,
          metadata: result.metadata || {},
        },
      });

      this.loggerService.business('tool_execution', {
        toolId: context.toolId,
        agentId: context.agentId,
        success: result.success,
        duration,
      });

    } catch (error) {
      this.logger.warn(`Failed to log tool execution: ${error}`);
    }
  }

  // Utility method to get available tools for an agent
  async getAvailableTools(agentId: string): Promise<ToolDefinition[]> {
    const tools = await this.prisma.tool.findMany({
      where: {
        OR: [
          { isPublic: true },
          { 
            agentTools: {
              some: { agentId }
            }
          }
        ]
      },
    });

    return tools.map((tool: Tool) => ({
      id: tool.id,
      name: tool.name,
      type: tool.type,
      config: tool.config,
      schema: tool.schema,
    }));
  }
}
