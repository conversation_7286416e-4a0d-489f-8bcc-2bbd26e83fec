import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, Index } from 'typeorm';
import { User } from '../users/user.entity';
import { Workflow } from '../workflows/workflow.entity';
import { Tool } from '../tools/tool.entity';

export enum AgentType {
  CONDUCTOR = 'conductor',
  SPECIALIZED = 'specialized',
  REVIEW = 'review',
  COORDINATOR = 'coordinator',
  EXECUTOR = 'executor',
}

export enum AgentStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  TRAINING = 'training',
  ERROR = 'error',
  DEPRECATED = 'deprecated',
}

export enum AgentCapability {
  TEXT_GENERATION = 'text_generation',
  CODE_GENERATION = 'code_generation',
  DATA_ANALYSIS = 'data_analysis',
  API_INTEGRATION = 'api_integration',
  FILE_PROCESSING = 'file_processing',
  WEB_SCRAPING = 'web_scraping',
  EMAIL_PROCESSING = 'email_processing',
  DOCUMENT_PROCESSING = 'document_processing',
  IMAGE_PROCESSING = 'image_processing',
  AUDIO_PROCESSING = 'audio_processing',
  VIDEO_PROCESSING = 'video_processing',
  DATABASE_OPERATIONS = 'database_operations',
  WORKFLOW_ORCHESTRATION = 'workflow_orchestration',
  DECISION_MAKING = 'decision_making',
  LEARNING = 'learning',
}

@Entity('agents')
@Index(['userId', 'status'])
@Index(['organizationId', 'type'])
@Index(['capabilities', 'status'])
export class Agent {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  name!: string;

  @Column({ type: 'text', nullable: true })
  description!: string;

  @Column({
    type: 'enum',
    enum: AgentType,
    default: AgentType.SPECIALIZED,
  })
  type!: AgentType;

  @Column({
    type: 'enum',
    enum: AgentStatus,
    default: AgentStatus.DRAFT,
  })
  status!: AgentStatus;

  @Column({ type: 'jsonb' })
  capabilities!: AgentCapability[];

  @Column({ type: 'jsonb' })
  configuration!: {
    model: {
      provider: string;
      model: string;
      version: string;
      parameters: Record<string, any>;
    };
    memory: {
      type: 'none' | 'conversation' | 'vector' | 'hybrid';
      maxTokens: number;
      retentionDays: number;
    };
    tools: Array<{
      id: string;
      name: string;
      type: string;
      config: Record<string, any>;
      permissions: string[];
    }>;
    prompts: {
      system: string;
      user: string;
      assistant: string;
      examples: Array<{ input: string; output: string }>;
    };
    constraints: {
      maxTokens: number;
      maxRequests: number;
      rateLimit: number;
      costLimit: number;
    };
    security: {
      dataRetention: number;
      encryption: boolean;
      auditLogging: boolean;
      accessControl: string[];
    };
  };

  @Column({ type: 'jsonb', nullable: true })
  performance!: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    averageTokensUsed: number;
    averageCost: number;
    lastUsedAt: Date;
    uptime: number;
    errorRate: number;
  };

  @Column({ type: 'jsonb', nullable: true })
  learning!: {
    enabled: boolean;
    adaptationRate: number;
    feedbackLoop: boolean;
    trainingData: Array<{
      input: string;
      expectedOutput: string;
      actualOutput: string;
      feedback: number;
      timestamp: Date;
    }>;
    modelVersion: string;
    lastTrainedAt: Date;
  };

  @Column({ type: 'jsonb', nullable: true })
  metadata!: {
    version: string;
    author: string;
    tags: string[];
    category: string;
    documentation: string;
    examples: Array<{
      title: string;
      description: string;
      input: string;
      output: string;
    }>;
    dependencies: string[];
    compatibility: string[];
  };

  @Column({ type: 'jsonb', nullable: true })
  permissions!: {
    owners: string[];
    editors: string[];
    viewers: string[];
    public: boolean;
    allowedOrganizations: string[];
  };

  @Column({ type: 'jsonb', nullable: true })
  monitoring!: {
    healthCheck: {
      enabled: boolean;
      interval: number;
      timeout: number;
      threshold: number;
    };
    alerts: Array<{
      type: string;
      condition: string;
      action: string;
      recipients: string[];
    }>;
    metrics: {
      responseTime: number[];
      errorRate: number[];
      usageCount: number[];
      costHistory: number[];
    };
  };

  @ManyToOne(() => User, user => user.agents, { onDelete: 'CASCADE' })
  user!: User;

  @Column()
  userId!: string;

  @Column()
  organizationId!: string;

  @ManyToOne(() => Workflow, workflow => workflow.agents, { nullable: true })
  workflow!: Workflow;

  @Column({ nullable: true })
  workflowId!: string;

  @OneToMany(() => Tool, tool => tool.agent)
  tools!: Tool[];

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  @Column({ nullable: true })
  deletedAt!: Date;
} 