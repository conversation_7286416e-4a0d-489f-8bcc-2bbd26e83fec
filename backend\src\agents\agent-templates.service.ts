import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AgentTemplate, AgentType, ProviderType } from '../database/entities/agent.entity';
import { ApixGateway } from '../websocket/apix.gateway';

export interface CreateTemplateDto {
  name: string;
  category: string;
  description: string;
  config: Record<string, any>;
  skills: string[];
  isPublic?: boolean;
  promptTemplate: string;
  type: AgentType;
  supportedProviders: ProviderType[];
  metadata?: Record<string, any>;
}

export interface TemplateCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  templateCount: number;
}

@Injectable()
export class AgentTemplatesService {
  private readonly logger = new Logger(AgentTemplatesService.name);

  constructor(
    @InjectRepository(AgentTemplate)
    private templateRepository: Repository<AgentTemplate>,
    private apixGateway: ApixGateway,
  ) { }

  async createTemplate(
    createTemplateDto: CreateTemplateDto,
    organizationId: string,
    userId: string,
  ): Promise<AgentTemplate> {
    try {
      const template = this.templateRepository.create({
        ...createTemplateDto,
        createdBy: userId,
        organizationId,
      });

      const savedTemplate = await this.templateRepository.save(template);

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'template_created', {
        templateId: savedTemplate.id,
        name: savedTemplate.name,
        category: savedTemplate.category,
        timestamp: new Date(),
      });

      this.logger.log(`Template created: ${savedTemplate.id} for organization: ${organizationId}`);
      return savedTemplate;
    } catch (error) {
      this.logger.error(`Failed to create template: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async getTemplatesByOrganization(organizationId: string): Promise<AgentTemplate[]> {
    try {
      return await this.templateRepository.find({
        where: [
          { organizationId },
          { isPublic: true }
        ],
        order: { createdAt: 'DESC' },
      });
    } catch (error) {
      this.logger.error(`Failed to get templates: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async getTemplateById(templateId: string, organizationId: string): Promise<AgentTemplate> {
    try {
      const template = await this.templateRepository.findOne({
        where: [
          { id: templateId, organizationId },
          { id: templateId, isPublic: true }
        ],
      });

      if (!template) {
        throw new NotFoundException(`Template with ID ${templateId} not found`);
      }

      return template;
    } catch (error) {
      this.logger.error(`Failed to get template ${templateId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async updateTemplate(
    templateId: string,
    updateData: Partial<CreateTemplateDto>,
    organizationId: string,
  ): Promise<AgentTemplate> {
    try {
      const template = await this.getTemplateById(templateId, organizationId);

      Object.assign(template, updateData);
      const updatedTemplate = await this.templateRepository.save(template);

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'template_updated', {
        templateId: updatedTemplate.id,
        changes: updateData,
        timestamp: new Date(),
      });

      return updatedTemplate;
    } catch (error) {
      this.logger.error(`Failed to update template ${templateId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async deleteTemplate(templateId: string, organizationId: string): Promise<void> {
    try {
      const template = await this.getTemplateById(templateId, organizationId);
      await this.templateRepository.remove(template);

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'template_deleted', {
        templateId,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`Failed to delete template ${templateId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async getTemplateCategories(organizationId: string): Promise<TemplateCategory[]> {
    try {
      const categories = await this.templateRepository
        .createQueryBuilder('template')
        .select('template.category', 'category')
        .addSelect('COUNT(*)', 'count')
        .where('template.organizationId = :organizationId OR template.isPublic = true', { organizationId })
        .groupBy('template.category')
        .getRawMany();

      const categoryData: Record<string, { name: string; description: string; icon: string }> = {
        'customer-support': {
          name: 'Customer Support',
          description: 'Agents for handling customer inquiries and support',
          icon: 'headphones',
        },
        'sales': {
          name: 'Sales',
          description: 'Agents for lead generation and sales processes',
          icon: 'trending-up',
        },
        'marketing': {
          name: 'Marketing',
          description: 'Agents for content creation and marketing tasks',
          icon: 'megaphone',
        },
        'hr': {
          name: 'Human Resources',
          description: 'Agents for HR processes and employee support',
          icon: 'users',
        },
        'finance': {
          name: 'Finance',
          description: 'Agents for financial analysis and reporting',
          icon: 'dollar-sign',
        },
        'operations': {
          name: 'Operations',
          description: 'Agents for operational tasks and workflows',
          icon: 'settings',
        },
        'development': {
          name: 'Development',
          description: 'Agents for coding and technical tasks',
          icon: 'code',
        },
        'general': {
          name: 'General',
          description: 'General-purpose agents for various tasks',
          icon: 'bot',
        },
      };

      return categories.map(cat => ({
        id: cat.category,
        name: categoryData[cat.category]?.name || cat.category,
        description: categoryData[cat.category]?.description || 'Custom category',
        icon: categoryData[cat.category]?.icon || 'bot',
        templateCount: parseInt(cat.count),
      }));
    } catch (error) {
      this.logger.error(`Failed to get template categories: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async getTemplatesByCategory(category: string, organizationId: string): Promise<AgentTemplate[]> {
    try {
      return await this.templateRepository.find({
        where: [
          { category, organizationId },
          { category, isPublic: true }
        ],
        order: { createdAt: 'DESC' },
      });
    } catch (error) {
      this.logger.error(`Failed to get templates by category: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async searchTemplates(query: string, organizationId: string): Promise<AgentTemplate[]> {
    try {
      return await this.templateRepository
        .createQueryBuilder('template')
        .where('(template.organizationId = :organizationId OR template.isPublic = true)', { organizationId })
        .andWhere('(template.name ILIKE :query OR template.description ILIKE :query OR template.category ILIKE :query)', {
          query: `%${query}%`,
        })
        .orderBy('template.createdAt', 'DESC')
        .getMany();
    } catch (error) {
      this.logger.error(`Failed to search templates: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async getPopularTemplates(organizationId: string, limit: number = 10): Promise<AgentTemplate[]> {
    try {
      // This would typically be based on usage metrics
      // For now, we'll return the most recently created public templates
      return await this.templateRepository.find({
        where: { isPublic: true },
        order: { createdAt: 'DESC' },
        take: limit,
      });
    } catch (error) {
      this.logger.error(`Failed to get popular templates: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async duplicateTemplate(templateId: string, organizationId: string, userId: string): Promise<AgentTemplate> {
    try {
      const originalTemplate = await this.getTemplateById(templateId, organizationId);

      const duplicatedTemplate = this.templateRepository.create({
        name: `${originalTemplate.name} (Copy)`,
        category: originalTemplate.category,
        description: originalTemplate.description,
        config: originalTemplate.config,
        skills: originalTemplate.skills,
        isPublic: false, // Duplicates are always private
        promptTemplate: originalTemplate.promptTemplate,
        type: originalTemplate.type,
        supportedProviders: originalTemplate.supportedProviders,
        metadata: originalTemplate.metadata,
        createdBy: userId,
        organizationId,
      });

      const savedTemplate = await this.templateRepository.save(duplicatedTemplate);

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'template_duplicated', {
        originalTemplateId: templateId,
        newTemplateId: savedTemplate.id,
        timestamp: new Date(),
      });

      return savedTemplate;
    } catch (error) {
      this.logger.error(`Failed to duplicate template ${templateId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async getTemplateUsageStats(templateId: string, organizationId: string): Promise<any> {
    try {
      const template = await this.getTemplateById(templateId, organizationId);

      // Get usage statistics for this template
      const usageStats = await this.templateRepository
        .createQueryBuilder('template')
        .leftJoin('template.instances', 'agent')
        .leftJoin('agent.executions', 'execution')
        .select('COUNT(DISTINCT agent.id)', 'totalInstances')
        .addSelect('COUNT(execution.id)', 'totalExecutions')
        .addSelect('AVG(execution.duration)', 'averageExecutionTime')
        .addSelect('SUM(execution.cost)', 'totalCost')
        .where('template.id = :templateId', { templateId })
        .getRawOne();

      return {
        templateId,
        templateName: template.name,
        totalInstances: parseInt(usageStats.totalInstances) || 0,
        totalExecutions: parseInt(usageStats.totalExecutions) || 0,
        averageExecutionTime: parseFloat(usageStats.averageExecutionTime) || 0,
        totalCost: parseFloat(usageStats.totalCost) || 0,
      };
    } catch (error) {
      this.logger.error(`Failed to get template usage stats ${templateId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined   );
      throw error;
    }
  }

  async seedDefaultTemplates(organizationId: string, userId: string): Promise<void> {
    try {
      const defaultTemplates = [
        {
          name: 'Customer Support Agent',
          category: 'customer-support',
          description: 'A helpful customer support agent that can handle common inquiries and provide solutions',
          type: AgentType.BASIC,
          promptTemplate: `You are a helpful customer support agent. Your role is to:
- Listen carefully to customer concerns
- Provide clear, accurate, and helpful responses
- Escalate complex issues when necessary
- Maintain a professional and empathetic tone
- Follow up to ensure customer satisfaction

Always be polite, patient, and solution-oriented.`,
          skills: ['customer-service', 'problem-solving', 'communication'],
          supportedProviders: [ProviderType.OPENAI, ProviderType.CLAUDE, ProviderType.GEMINI],
          config: {
            temperature: 0.7,
            maxTokens: 1000,
            systemPrompt: 'You are a helpful customer support agent.',
          },
        },
        {
          name: 'Sales Assistant',
          category: 'sales',
          description: 'A sales assistant that can help with lead qualification and sales processes',
          type: AgentType.BASIC,
          promptTemplate: `You are a sales assistant. Your role is to:
- Qualify leads based on their needs and budget
- Provide product information and pricing
- Handle objections professionally
- Schedule follow-up meetings
- Track sales opportunities

Be consultative, professional, and focused on value creation.`,
          skills: ['sales', 'lead-qualification', 'product-knowledge'],
          supportedProviders: [ProviderType.OPENAI, ProviderType.CLAUDE, ProviderType.GEMINI],
          config: {
            temperature: 0.6,
            maxTokens: 800,
            systemPrompt: 'You are a sales assistant.',
          },
        },
        {
          name: 'Content Writer',
          category: 'marketing',
          description: 'A content writer that can create engaging marketing materials and blog posts',
          type: AgentType.BASIC,
          promptTemplate: `You are a content writer. Your role is to:
- Create engaging and informative content
- Write in a clear, professional style
- Adapt tone and style to target audience
- Include relevant keywords naturally
- Ensure content is original and valuable

Write content that is informative, engaging, and optimized for your audience.`,
          skills: ['content-writing', 'seo', 'marketing'],
          supportedProviders: [ProviderType.OPENAI, ProviderType.CLAUDE, ProviderType.GEMINI],
          config: {
            temperature: 0.8,
            maxTokens: 1500,
            systemPrompt: 'You are a content writer.',
          },
        },
        {
          name: 'Code Assistant',
          category: 'development',
          description: 'A coding assistant that can help with programming tasks and code reviews',
          type: AgentType.BASIC,
          promptTemplate: `You are a code assistant. Your role is to:
- Write clean, efficient, and well-documented code
- Review code for best practices and security
- Debug issues and provide solutions
- Explain code concepts clearly
- Suggest improvements and optimizations

Write code that is readable, maintainable, and follows best practices.`,
          skills: ['programming', 'code-review', 'debugging'],
          supportedProviders: [ProviderType.OPENAI, ProviderType.CLAUDE, ProviderType.GEMINI],
          config: {
            temperature: 0.3,
            maxTokens: 2000,
            systemPrompt: 'You are a code assistant.',
          },
        },
        {
          name: 'Data Analyst',
          category: 'finance',
          description: 'A data analyst that can help with data interpretation and reporting',
          type: AgentType.BASIC,
          promptTemplate: `You are a data analyst. Your role is to:
- Analyze data and identify patterns
- Create clear and insightful reports
- Provide actionable recommendations
- Explain complex data in simple terms
- Help with data visualization decisions

Provide analysis that is accurate, insightful, and actionable.`,
          skills: ['data-analysis', 'reporting', 'statistics'],
          supportedProviders: [ProviderType.OPENAI, ProviderType.CLAUDE, ProviderType.GEMINI],
          config: {
            temperature: 0.5,
            maxTokens: 1200,
            systemPrompt: 'You are a data analyst.',
          },
        },
      ];

      for (const templateData of defaultTemplates) {
        const existingTemplate = await this.templateRepository.findOne({
          where: { name: templateData.name, organizationId },
        });

        if (!existingTemplate) {
          await this.createTemplate({
            ...templateData,
            isPublic: false,
          }, organizationId, userId);
        }
      }

      this.logger.log(`Default templates seeded for organization: ${organizationId}`);
    } catch (error) {
      this.logger.error(`Failed to seed default templates: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }
}