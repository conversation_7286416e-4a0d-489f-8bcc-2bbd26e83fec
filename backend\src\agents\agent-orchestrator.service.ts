import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Agent, AgentTemplate, AgentCollaboration, AgentType, AgentStatus } from '../database/entities/agent.entity';
import { AgentExecution, ExecutionStatus } from '../database/entities/agent-execution.entity';
import { SessionMemoryService } from './session-memory.service';
import { AgentAnalyticsService } from './agent-analytics.service';
import { ApixGateway } from '../websocket/apix.gateway';
import { AIProviderIntegrationService } from '../providers/ai-provider-integration.service';
import { AIProviderSelectorService } from '../providers/ai-provider-selector.service';
import { CreateAgentInstanceDto, UpdateAgentInstanceDto, ExecuteAgentDto } from './dto/agent.dto';

export interface AgentWizardStep {
  step: number;
  title: string;
  description: string;
  fields: string[];
  isCompleted: boolean;
  validation?: Record<string, any>;
}

export interface AgentWizardData {
  step: number;
  name: string;
  description: string;
  templateId: string;
  type: AgentType;
  primaryProvider: string;
  fallbackProviders: string[];
  config: {
    temperature: number;
    maxTokens: number;
    systemPrompt: string;
    skills: string[];
    capabilities: string[];
    personality: string;
    responseStyle: string;
  };
  memoryConfig: {
    maxTokens: number;
    retentionDays: number;
    enableLongTerm: boolean;
  };
  testMessages: string[];
  isPublic: boolean;
}

export interface AgentTestResult {
  success: boolean;
  output: string;
  metadata: {
    provider: string;
    model: string;
    tokens: { input: number; output: number; total: number };
    duration: number;
    cost: number;
  };
  error?: string;
}

@Injectable()
export class AgentOrchestratorService {
  private readonly logger = new Logger(AgentOrchestratorService.name);

  constructor(
    @InjectRepository(Agent)
    private agentRepository: Repository<Agent>,
    @InjectRepository(AgentExecution)
    private executionRepository: Repository<AgentExecution>,
    @InjectRepository(AgentCollaboration)
    private collaborationRepository: Repository<AgentCollaboration>,
    private sessionMemoryService: SessionMemoryService,
    private analyticsService: AgentAnalyticsService,
    private apixGateway: ApixGateway,
    private aiProviderIntegration: AIProviderIntegrationService,
    private aiProviderSelector: AIProviderSelectorService,
  ) { }

  async createAgent(
    createAgentDto: CreateAgentInstanceDto,
    organizationId: string,
    userId: string,
  ): Promise<Agent> {
    try {
      // Validate agent configuration
      await this.validateAgentConfiguration(createAgentDto);

      const agent = this.agentRepository.create({
        ...createAgentDto,
        organizationId,
        createdBy: userId,
        performanceMetrics: {
          totalExecutions: 0,
          successRate: 0,
          averageResponseTime: 0,
          lastExecuted: new Date(),
        },
      });

      const savedAgent = await this.agentRepository.save(agent);

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'agent_created', {
        agentId: savedAgent.id,
        name: savedAgent.name,
        type: savedAgent.type,
        timestamp: new Date(),
      });

      this.logger.log(`Agent created: ${savedAgent.id} for organization: ${organizationId}`);
      return savedAgent;
    } catch (error) {
      this.logger.error(`Failed to create agent: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async createAgentFromWizard(
    wizardData: AgentWizardData,
    organizationId: string,
    userId: string,
  ): Promise<Agent> {
    try {
      // Validate wizard data
      await this.validateWizardData(wizardData);

      const createAgentDto: CreateAgentInstanceDto = {
        templateId: wizardData.templateId,
        name: wizardData.name,
        type: wizardData.type,
        primaryProvider: wizardData.primaryProvider as any,
        fallbackProviders: wizardData.fallbackProviders as any,
        config: wizardData.config,
        memoryConfig: wizardData.memoryConfig,
        skills: wizardData.config.skills,
      };

      return await this.createAgent(createAgentDto, organizationId, userId);
    } catch (error) {
      this.logger.error(`Failed to create agent from wizard: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async getAgentWizardSteps(): Promise<AgentWizardStep[]> {
    return [
      {
        step: 1,
        title: 'Basic Information',
        description: 'Set your agent\'s name and basic details',
        fields: ['name', 'description'],
        isCompleted: false,
        validation: {
          name: { required: true, minLength: 2, maxLength: 100 },
          description: { required: true, maxLength: 500 },
        },
      },
      {
        step: 2,
        title: 'Template Selection',
        description: 'Choose a template or start from scratch',
        fields: ['templateId', 'type'],
        isCompleted: false,
        validation: {
          templateId: { required: false },
          type: { required: true },
        },
      },
      {
        step: 3,
        title: 'Personality & Style',
        description: 'Define your agent\'s personality and response style',
        fields: ['personality', 'responseStyle', 'systemPrompt'],
        isCompleted: false,
        validation: {
          personality: { required: true },
          responseStyle: { required: true },
          systemPrompt: { required: true, minLength: 10 },
        },
      },
      {
        step: 4,
        title: 'AI Provider',
        description: 'Select your preferred AI provider and models',
        fields: ['primaryProvider', 'fallbackProviders'],
        isCompleted: false,
        validation: {
          primaryProvider: { required: true },
        },
      },
      {
        step: 5,
        title: 'Capabilities',
        description: 'Choose what your agent can do',
        fields: ['capabilities', 'skills'],
        isCompleted: false,
        validation: {
          capabilities: { required: true, minLength: 1 },
        },
      },
      {
        step: 6,
        title: 'Memory Settings',
        description: 'Configure how your agent remembers conversations',
        fields: ['memoryConfig'],
        isCompleted: false,
        validation: {
          memoryConfig: { required: true },
        },
      },
      {
        step: 7,
        title: 'Test & Validate',
        description: 'Test your agent with sample conversations',
        fields: ['testMessages'],
        isCompleted: false,
        validation: {
          testMessages: { required: false },
        },
      },
    ];
  }

  async validateWizardData(wizardData: AgentWizardData): Promise<void> {
    const errors: string[] = [];

    if (!wizardData.name?.trim()) {
      errors.push('Agent name is required');
    }

    if (!wizardData.type) {
      errors.push('Agent type is required');
    }

    if (!wizardData.primaryProvider) {
      errors.push('Primary provider is required');
    }

    if (!wizardData.config.systemPrompt?.trim()) {
      errors.push('System prompt is required');
    }

    if (wizardData.config.capabilities.length === 0) {
      errors.push('At least one capability must be selected');
    }

    if (errors.length > 0) {
      throw new BadRequestException(`Validation failed: ${errors.join(', ')}`);
    }
  }

  async validateAgentConfiguration(createAgentDto: CreateAgentInstanceDto): Promise<void> {
    const errors: string[] = [];

    if (!createAgentDto.name?.trim()) {
      errors.push('Agent name is required');
    }

    if (!createAgentDto.type) {
      errors.push('Agent type is required');
    }

    if (!createAgentDto.primaryProvider) {
      errors.push('Primary provider is required');
    }

    if (!createAgentDto.config?.systemPrompt?.trim()) {
      errors.push('System prompt is required');
    }

    if (errors.length > 0) {
      throw new BadRequestException(`Validation failed: ${errors.join(', ')}`);
    }
  }

  async updateAgent(
    agentId: string,
    updateAgentDto: UpdateAgentInstanceDto,
    organizationId: string,
  ): Promise<Agent> {
    try {
      const agent = await this.getAgentById(agentId, organizationId);

      // Validate updates
      if (updateAgentDto.config) {
        await this.validateAgentConfiguration({
          ...agent,
          config: updateAgentDto.config,
        } as CreateAgentInstanceDto);
      }

      Object.assign(agent, updateAgentDto);
      const updatedAgent = await this.agentRepository.save(agent);

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'agent_updated', {
        agentId: updatedAgent.id,
        changes: updateAgentDto,
        timestamp: new Date(),
      });

      return updatedAgent;
    } catch (error) {
      this.logger.error(`Failed to update agent ${agentId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async deleteAgent(agentId: string, organizationId: string): Promise<void> {
    try {
      const agent = await this.getAgentById(agentId, organizationId);

      // Soft delete
      agent.status = AgentStatus.INACTIVE;
      await this.agentRepository.save(agent);

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'agent_deleted', {
        agentId,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`Failed to delete agent ${agentId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async executeAgent(
    agentId: string,
    executeDto: ExecuteAgentDto,
    organizationId: string,
  ): Promise<AgentExecution> {
    try {
      const agent = await this.getAgentById(agentId, organizationId);

      if (agent.status !== AgentStatus.ACTIVE) {
        throw new BadRequestException(`Agent is not active. Current status: ${agent.status}`);
      }

      const startTime = Date.now();
      let execution: AgentExecution;

      try {
        // Get or create session memory
        const sessionId = executeDto.sessionId || `session_${Date.now()}`;
        const sessionMemory = await this.sessionMemoryService.createSession(sessionId, organizationId);

        // Execute agent logic
        const result = await this.executeAgentLogic(agent, executeDto.message, sessionMemory, executeDto.context);

        // Create execution record
        execution = this.executionRepository.create({
          agentId: agent.id,
          sessionId,
          input: executeDto.message,
          output: result.output,
          status: ExecutionStatus.COMPLETED,
          duration: Date.now() - startTime,
          cost: result.cost,
          metadata: {
            provider: result.provider,
            model: result.model,
            tokens: result.tokens,
            retryCount: result.retryCount || 0,
          },
        });

        await this.executionRepository.save(execution);

        // Update session memory
        await this.sessionMemoryService.addMessage(sessionId, {
          role: 'user',
          content: executeDto.message,
          timestamp: new Date(),
        });

        await this.sessionMemoryService.addMessage(sessionId, {
          role: 'assistant',
          content: result.output,
          timestamp: new Date(),
        });

        // Update agent metrics
        await this.updateAgentMetrics(agentId, execution.duration, true);

        // Emit APIX event
        this.apixGateway.emitToOrganization(organizationId, 'agent_executed', {
          agentId: agent.id,
          executionId: execution.id,
          duration: execution.duration,
          cost: execution.cost,
          timestamp: new Date(),
        });

        return execution;
      } catch (error) {
        // Create failed execution record
        execution = this.executionRepository.create({
          agentId: agent.id,
          sessionId: executeDto.sessionId || `session_${Date.now()}`,
          input: executeDto.message,
          output: '',
          status: ExecutionStatus.FAILED,
          duration: Date.now() - startTime,
          cost: 0,
          metadata: {
            provider: '',
            model: '',
            tokens: { input: 0, output: 0, total: 0 },
            duration: 0,
            cost: 0,
          },
        });

        await this.executionRepository.save(execution);

        // Update agent metrics
        await this.updateAgentMetrics(agentId, execution.duration, false);

        throw error;
      }
    } catch (error) {
      this.logger.error(`Failed to execute agent ${agentId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async testAgent(
    agentId: string,
    testMessage: string,
    organizationId: string,
  ): Promise<AgentTestResult> {
    try {
      const agent = await this.getAgentById(agentId, organizationId);
      const startTime = Date.now();

      // Execute agent logic without saving to database
      const result = await this.executeAgentLogic(agent, testMessage, {}, { testing: true });

      return {
        success: true,
        output: result.output,
        metadata: {
          provider: result.provider,
          model: result.model,
          tokens: result.tokens,
          duration: Date.now() - startTime,
          cost: result.cost,
        },
      };
    } catch (error) {
      this.logger.error(`Agent test failed for ${agentId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      return {
        success: false,
        output: '',
        metadata: {
          provider: '',
          model: '',
          tokens: { input: 0, output: 0, total: 0 },
          duration: 0,
          cost: 0,
        },
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async getAgentById(agentId: string, organizationId: string): Promise<Agent> {
    const agent = await this.agentRepository.findOne({
      where: { id: agentId, organizationId },
      relations: ['template', 'creator', 'organization'],
    });

    if (!agent) {
      throw new NotFoundException(`Agent with ID ${agentId} not found`);
    }

    return agent;
  }

  async getAgentsByOrganization(organizationId: string): Promise<Agent[]> {
    return await this.agentRepository.find({
      where: { organizationId },
      relations: ['template', 'creator'],
      order: { createdAt: 'DESC' },
    });
  }

  async getAgentExecutions(agentId: string, organizationId: string): Promise<AgentExecution[]> {
    return await this.executionRepository.find({
      where: { agentId },
      order: { createdAt: 'DESC' },
      take: 100,
    });
  }

  async getAgentAnalytics(agentId: string, organizationId: string): Promise<any> {
    const agent = await this.getAgentById(agentId, organizationId);
    return await this.analyticsService.getAgentAnalytics(agent, organizationId);
  }

  async duplicateAgent(agentId: string, organizationId: string, userId: string): Promise<Agent> {
    try {
      const originalAgent = await this.getAgentById(agentId, organizationId);

      const duplicatedAgent = this.agentRepository.create({
        name: `${originalAgent.name} (Copy)`,
        templateId: originalAgent.templateId,
        config: originalAgent.config,
        type: originalAgent.type,
        status: AgentStatus.ACTIVE,
        primaryProvider: originalAgent.primaryProvider,
        fallbackProviders: originalAgent.fallbackProviders,
        memoryConfig: originalAgent.memoryConfig,
        skills: originalAgent.skills,
        performanceMetrics: {
          totalExecutions: 0,
          successRate: 0,
          averageResponseTime: 0,
          lastExecuted: new Date(),
        },
        organizationId,
        createdBy: userId,
      });

      const savedAgent = await this.agentRepository.save(duplicatedAgent);

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'agent_duplicated', {
        originalAgentId: agentId,
        newAgentId: savedAgent.id,
        timestamp: new Date(),
      });

      return savedAgent;
    } catch (error) {
      this.logger.error(`Failed to duplicate agent ${agentId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  private async executeAgentLogic(
    agent: Agent,
    message: string,
    sessionMemory: any,
    context: Record<string, any> = {},
  ): Promise<{
    output: string;
    provider: string;
    model: string;
    tokens: { input: number; output: number; total: number };
    cost: number;
    retryCount?: number;
  }> {
    try {
      // Select AI provider
      const selectedProvider = await this.aiProviderSelector.selectOptimalProvider(
        {
          organizationId: agent.organizationId,
          capabilities: agent.config.capabilities,
          maxCost: agent.config.maxCost,
          maxLatency: agent.config.maxLatency,
          preferredProviders: agent.config.preferredProviders,
          excludeProviders: agent.config.excludeProviders,
        }
      );

      // Build system prompt
      const systemPrompt = this.buildSystemPrompt(agent.template?.promptTemplate || '', agent, context);

      // Prepare conversation history
      const conversationHistory = sessionMemory?.messages || [];

      // Execute with selected provider
      const result = await this.aiProviderIntegration.processRequest(
        {
          requestId: `exec_${Date.now()}_${agent.id}`,
          messages: [
            { role: 'system', content: systemPrompt },
            ...conversationHistory,
            { role: 'user', content: message }, 
          ],
          modelId: agent.config.model || 'gpt-3.5-turbo',
          temperature: agent.config.temperature || 0.7,
          maxTokens: agent.config.maxTokens || 1000,
          providerId: selectedProvider.providerId,
          organizationId: agent.organizationId,
        },
      );

      return {
        output: result.content,
        provider: selectedProvider.providerId,
        model: result.modelId,
        tokens: {
          input: result.usage.promptTokens,
          output: result.usage.completionTokens,
          total: result.usage.totalTokens,
        },
        cost: result.cost,
      };
    } catch (error) {
      this.logger.error(`Agent execution failed: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  private buildSystemPrompt(template: string, agent: Agent, context: Record<string, any>): string {
    let systemPrompt = template || agent.config.systemPrompt || '';

    // Add agent-specific context
    if (agent.config.personality) {
      systemPrompt += `\n\nPersonality: ${agent.config.personality}`;
    }

    if (agent.config.responseStyle) {
      systemPrompt += `\n\nResponse Style: ${agent.config.responseStyle}`;
    }

    // Add context information
    if (Object.keys(context).length > 0) {
      systemPrompt += `\n\nContext: ${JSON.stringify(context)}`;
    }

    return systemPrompt;
  }

  private async updateAgentMetrics(agentId: string, duration: number, success: boolean): Promise<void> {
    try {
      const agent = await this.agentRepository.findOne({ where: { id: agentId } });
      if (!agent) return;

      const metrics = agent.performanceMetrics || {
        totalExecutions: 0,
        successRate: 0,
        averageResponseTime: 0,
        lastExecuted: new Date(),
      };

      metrics.totalExecutions += 1;
      metrics.lastExecuted = new Date();

      // Update success rate
      const successfulExecutions = await this.executionRepository.count({
        where: { agentId, status: ExecutionStatus.COMPLETED },
      });

      metrics.successRate = successfulExecutions / metrics.totalExecutions;

      // Update average response time
      const avgTime = await this.executionRepository
        .createQueryBuilder('execution')
        .select('AVG(execution.duration)', 'avgDuration')
        .where('execution.agentId = :agentId', { agentId })
        .andWhere('execution.status = :status', { status: ExecutionStatus.COMPLETED })
        .getRawOne();

      metrics.averageResponseTime = parseFloat(avgTime.avgDuration) || 0;

      agent.performanceMetrics = metrics;
      await this.agentRepository.save(agent);
    } catch (error) {
      this.logger.error(`Failed to update agent metrics: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
    }
  }

  // Collaboration methods remain the same...
  async createCollaboration(
    name: string,
    agentIds: string[],
    coordinatorId: string,
    workflow: Record<string, any>,
    organizationId: string,
    userId: string,
  ): Promise<AgentCollaboration> {
    try {
      const collaboration = this.collaborationRepository.create({
        name,
        agentIds,
        coordinatorId,
        workflow,
        organizationId,
        createdBy: userId,
      });

      const savedCollaboration = await this.collaborationRepository.save(collaboration);

      // Emit APIX event
      this.apixGateway.emitToOrganization(organizationId, 'collaboration_created', {
        collaborationId: savedCollaboration.id,
        name: savedCollaboration.name,
        timestamp: new Date(),
      });

      return savedCollaboration;
    } catch (error) {
      this.logger.error(`Failed to create collaboration: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async executeCollaboration(
    collaborationId: string,
    input: string,
    organizationId: string,
  ): Promise<{
    collaborationId: string;
    results: Array<{
      agentId: string;
      output: string;
      executionId: string;
    }>;
    finalOutput: string;
  }> {
    try {
      const collaboration = await this.collaborationRepository.findOne({
        where: { id: collaborationId, organizationId },
      });

      if (!collaboration) {
        throw new NotFoundException(`Collaboration with ID ${collaborationId} not found`);
      }

      const results = await this.executeCollaborationWorkflow(collaboration, input, organizationId);

      // Generate final output using coordinator agent
      const coordinatorAgent = await this.getAgentById(collaboration.coordinatorId, organizationId);
      const finalResult = await this.executeAgentLogic(coordinatorAgent, input, {}, {
        collaborationResults: results,
      });

      return {
        collaborationId,
        results,
        finalOutput: finalResult.output,
      };
    } catch (error) {
      this.logger.error(`Failed to execute collaboration ${collaborationId}: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  private async executeCollaborationWorkflow(
    collaboration: AgentCollaboration,
    input: string,
    organizationId: string,
  ): Promise<Array<{
    agentId: string;
    output: string;
    executionId: string;
  }>> {
    const results = [];

    for (const agentId of collaboration.agentIds) {
      try {
        const agent = await this.getAgentById(agentId, organizationId);
        const result = await this.executeAgentLogic(agent, input, {}, {
          collaboration: true,
          workflow: collaboration.workflow,
        });

        results.push({
          agentId,
          output: result.output,
          executionId: `exec_${Date.now()}_${agentId}`,
        });
      } catch (error) {
        this.logger.error(`Failed to execute agent ${agentId} in collaboration: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
        results.push({
          agentId,
          output: `Error: ${error instanceof Error ? error.message : String(error)}`,
          executionId: `exec_${Date.now()}_${agentId}`,
        });
      }
    }

    return results;
  }

  async getCollaborationsByOrganization(organizationId: string): Promise<AgentCollaboration[]> {
    return await this.collaborationRepository.find({
      where: { organizationId },
      order: { createdAt: 'DESC' },
    });
  }
}