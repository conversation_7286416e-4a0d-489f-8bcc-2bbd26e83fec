import { Controller, Get, Post, Body, Param, Query, UseGuards, Request, Sse } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { UniversalSDKService, UniversalAIRequest } from './universal-sdk.service';
import { SmartRoutingService } from './smart-routing.service';
import { ProviderAnalyticsService } from './provider-analytics.service';
import { LoggerService } from '../common/services/logger.service';
import { Observable, interval, map } from 'rxjs';

@ApiTags('providers')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('providers')
export class ProviderManagementController {
  constructor(
    private universalSDK: UniversalSDKService,
    private smartRouting: SmartRoutingService,
    private analytics: ProviderAnalyticsService,
    private logger: LoggerService,
  ) {}

  @Post('ai/complete')
  @ApiOperation({ summary: 'Universal AI completion with smart routing' })
  @ApiResponse({ status: 200, description: 'AI completion successful' })
  async universalComplete(
    @Body() body: {
      prompt: string;
      provider?: string;
      model?: string;
      temperature?: number;
      maxTokens?: number;
      priority?: 'cost' | 'speed' | 'quality' | 'balanced';
      systemPrompt?: string;
      context?: Array<{ role: 'user' | 'assistant' | 'system'; content: string }>;
      maxLatency?: number;
      maxCost?: number;
      tags?: string[];
    },
    @Request() req: any,
  ) {
    try {
      const request: UniversalAIRequest = {
        ...body,
        organizationId: req.user.organizationId,
        userId: req.user.id,
      };

      const result = await this.universalSDK.complete(request);

      return {
        success: true,
        data: result,
      };

    } catch (error) {
      this.logger.error('Universal AI completion failed', {
        userId: req.user.id,
        organizationId: req.user.organizationId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  @Post('ai/stream')
  @ApiOperation({ summary: 'Universal AI streaming with smart routing' })
  @ApiResponse({ status: 200, description: 'AI streaming started' })
  async universalStream(
    @Body() body: {
      prompt: string;
      provider?: string;
      model?: string;
      temperature?: number;
      maxTokens?: number;
      priority?: 'cost' | 'speed' | 'quality' | 'balanced';
      systemPrompt?: string;
      context?: Array<{ role: 'user' | 'assistant' | 'system'; content: string }>;
    },
    @Request() req: any,
  ) {
    try {
      const request: UniversalAIRequest = {
        ...body,
        organizationId: req.user.organizationId,
        userId: req.user.id,
        requiresStreaming: true,
      };

      const result = await this.universalSDK.stream(request);

      return {
        success: true,
        data: {
          requestId: result.requestId,
          provider: result.provider,
          model: result.model,
          streamUrl: `/providers/ai/stream/${result.requestId}`,
        },
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  @Get('routing/optimal')
  @ApiOperation({ summary: 'Get optimal provider for request' })
  @ApiResponse({ status: 200, description: 'Optimal provider selected' })
  async getOptimalProvider(
    @Query('priority') priority: 'cost' | 'speed' | 'quality' | 'balanced' = 'balanced',
    @Query('maxLatency') maxLatency?: number,
    @Query('maxCost') maxCost?: number,
    @Query('requiresStreaming') requiresStreaming?: boolean,
    @Request() req: any,
  ) {
    try {
      const routing = await this.smartRouting.selectOptimalProvider({
        organizationId: req.user.organizationId,
        priority,
        maxLatency: maxLatency ? Number(maxLatency) : undefined,
        maxCost: maxCost ? Number(maxCost) : undefined,
        requiresStreaming: requiresStreaming === 'true',
      });

      return {
        success: true,
        data: routing,
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  @Get('metrics')
  @ApiOperation({ summary: 'Get provider metrics for organization' })
  @ApiResponse({ status: 200, description: 'Provider metrics retrieved' })
  async getProviderMetrics(@Request() req: any) {
    try {
      const metrics = await this.smartRouting.getProviderMetricsForOrganization(
        req.user.organizationId
      );

      return {
        success: true,
        data: metrics,
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  @Get('analytics/organization')
  @ApiOperation({ summary: 'Get organization usage analytics' })
  @ApiResponse({ status: 200, description: 'Analytics retrieved' })
  async getOrganizationAnalytics(
    @Query('timeframe') timeframe: 'hour' | 'day' | 'week' | 'month' = 'day',
    @Request() req: any,
  ) {
    try {
      const report = await this.analytics.generateOrganizationReport(
        req.user.organizationId,
        timeframe
      );

      return {
        success: true,
        data: report,
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  @Get('analytics/provider/:providerId')
  @ApiOperation({ summary: 'Get provider performance analytics' })
  @ApiResponse({ status: 200, description: 'Provider analytics retrieved' })
  async getProviderAnalytics(
    @Param('providerId') providerId: string,
    @Query('timeframe') timeframe: 'hour' | 'day' | 'week' | 'month' = 'day',
    @Request() req: any,
  ) {
    try {
      const report = await this.analytics.generateProviderReport(providerId, timeframe);

      return {
        success: true,
        data: report,
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  @Get('models')
  @ApiOperation({ summary: 'Get available models for organization' })
  @ApiResponse({ status: 200, description: 'Available models retrieved' })
  async getAvailableModels(@Request() req: any) {
    try {
      const models = await this.universalSDK.getAvailableModels(req.user.organizationId);

      return {
        success: true,
        data: models,
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  @Get('usage/stats')
  @ApiOperation({ summary: 'Get usage statistics' })
  @ApiResponse({ status: 200, description: 'Usage statistics retrieved' })
  async getUsageStats(
    @Query('timeframe') timeframe: 'hour' | 'day' | 'week' | 'month' = 'day',
    @Request() req: any,
  ) {
    try {
      const stats = await this.universalSDK.getUsageStats(req.user.organizationId, timeframe);

      return {
        success: true,
        data: stats,
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  @Get('health/:providerId')
  @ApiOperation({ summary: 'Check provider health' })
  @ApiResponse({ status: 200, description: 'Provider health checked' })
  async checkProviderHealth(
    @Param('providerId') providerId: string,
    @Request() req: any,
  ) {
    try {
      const health = await this.smartRouting.checkProviderHealth(providerId);

      return {
        success: true,
        data: health,
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  @Sse('metrics/live')
  @ApiOperation({ summary: 'Live provider metrics stream' })
  liveMetrics(@Request() req: any): Observable<any> {
    return interval(5000).pipe(
      map(async () => {
        try {
          const metrics = await this.smartRouting.getProviderMetricsForOrganization(
            req.user.organizationId
          );
          
          return {
            data: JSON.stringify({
              timestamp: new Date().toISOString(),
              metrics,
            }),
          };
        } catch (error) {
          return {
            data: JSON.stringify({
              timestamp: new Date().toISOString(),
              error: 'Failed to fetch metrics',
            }),
          };
        }
      })
    );
  }

  @Get('dashboard')
  @ApiOperation({ summary: 'Get provider dashboard data' })
  @ApiResponse({ status: 200, description: 'Dashboard data retrieved' })
  async getDashboardData(@Request() req: any) {
    try {
      const [metrics, analytics, models, usage] = await Promise.all([
        this.smartRouting.getProviderMetricsForOrganization(req.user.organizationId),
        this.analytics.generateOrganizationReport(req.user.organizationId, 'day'),
        this.universalSDK.getAvailableModels(req.user.organizationId),
        this.universalSDK.getUsageStats(req.user.organizationId, 'day'),
      ]);

      return {
        success: true,
        data: {
          metrics,
          analytics,
          models,
          usage,
          lastUpdated: new Date().toISOString(),
        },
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}
