"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowExecutionService = void 0;
const common_1 = require("@nestjs/common");
const logger_service_1 = require("../logging/logger.service");
const redis_service_1 = require("../cache/redis.service");
const event_emitter_1 = require("@nestjs/event-emitter");
let WorkflowExecutionService = class WorkflowExecutionService {
    constructor(logger, redisService, eventEmitter) {
        this.logger = logger;
        this.redisService = redisService;
        this.eventEmitter = eventEmitter;
        this.executionCache = new Map();
        this.stepExecutors = new Map();
        this.initializeStepExecutors();
    }
    async executeWorkflow(workflow, userId, inputVariables) {
        try {
            const executionId = this.generateExecutionId();
            const context = {
                workflowId: workflow.id,
                executionId,
                userId,
                organizationId: workflow.organizationId,
                variables: Object.assign(Object.assign({}, workflow.definition.variables), inputVariables),
                stepResults: new Map(),
                currentStep: null,
                status: 'running',
                startTime: new Date(),
                lastActivity: new Date(),
                metadata: {},
            };
            this.executionCache.set(executionId, context);
            await this.redisService.set(`workflow:execution:${executionId}`, JSON.stringify(context), 3600);
            this.eventEmitter.emit('workflow.execution.started', {
                workflowId: workflow.id,
                executionId,
                userId,
                organizationId: workflow.organizationId,
            });
            this.executeWorkflowSteps(workflow, context).catch((error) => {
                this.logger.error(`Workflow execution failed: ${error.message}`, error.stack, 'WorkflowExecutionService');
                this.handleExecutionError(context, error);
            });
            return executionId;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorStack = error instanceof Error ? error.stack : '';
            this.logger.error(`Failed to start workflow execution: ${errorMessage}`, errorStack, 'WorkflowExecutionService');
            throw new common_1.InternalServerErrorException('Failed to start workflow execution');
        }
    }
    async executeWorkflowSteps(workflow, context) {
        try {
            const { steps } = workflow.definition;
            const executionOrder = this.calculateExecutionOrder(steps);
            for (const stepId of executionOrder) {
                if (context.status === 'paused') {
                    await this.waitForResume(context.executionId);
                }
                if (context.status === 'failed') {
                    break;
                }
                const step = steps.find(s => s.id === stepId);
                if (!step) {
                    throw new Error(`Step ${stepId} not found in workflow`);
                }
                await this.executeStep(workflow, step, context);
            }
            if (context.status === 'running') {
                context.status = 'completed';
                await this.finalizeExecution(context);
            }
        }
        catch (error) {
            const errorObj = error instanceof Error ? error : new Error('Unknown error');
            this.handleExecutionError(context, errorObj);
        }
    }
    async executeStep(workflow, step, context) {
        const stepResult = {
            stepId: step.id,
            status: 'running',
            startTime: new Date(),
            metadata: {},
        };
        try {
            context.currentStep = step.id;
            context.lastActivity = new Date();
            if (step.conditions && !this.evaluateConditions(step.conditions, context)) {
                stepResult.status = 'completed';
                stepResult.result = { skipped: true, reason: 'Conditions not met' };
                context.stepResults.set(step.id, stepResult);
                return;
            }
            const executor = this.stepExecutors.get(step.type);
            if (!executor) {
                throw new Error(`No executor found for step type: ${step.type}`);
            }
            const result = await executor(context, step);
            stepResult.status = 'completed';
            stepResult.result = result;
            stepResult.endTime = new Date();
            stepResult.duration = stepResult.endTime.getTime() - stepResult.startTime.getTime();
            if (result && typeof result === 'object') {
                context.variables = Object.assign(Object.assign({}, context.variables), result);
            }
            context.stepResults.set(step.id, stepResult);
            this.eventEmitter.emit('workflow.step.completed', {
                workflowId: workflow.id,
                executionId: context.executionId,
                stepId: step.id,
                result,
                duration: stepResult.duration,
            });
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            stepResult.status = 'failed';
            stepResult.error = errorMessage;
            stepResult.endTime = new Date();
            stepResult.duration = stepResult.endTime.getTime() - stepResult.startTime.getTime();
            context.stepResults.set(step.id, stepResult);
            const errorObj = error instanceof Error ? error : new Error(errorMessage);
            await this.handleStepFailure(workflow, step, context, errorObj);
        }
    }
    async handleStepFailure(workflow, step, context, error) {
        const { errorHandling } = workflow.definition;
        let retryCount = 0;
        while (retryCount < errorHandling.retryCount && context.status === 'running') {
            retryCount++;
            this.logger.warn(`Retrying step ${step.id} (attempt ${retryCount}/${errorHandling.retryCount})`, 'WorkflowExecutionService');
            await this.delay(errorHandling.retryDelay);
            try {
                await this.executeStep(workflow, step, context);
                this.logger.log(`Step ${step.id} succeeded on retry ${retryCount}`, 'WorkflowExecutionService');
                return;
            }
            catch (retryError) {
                const errorMessage = retryError instanceof Error ? retryError.message : 'Unknown error';
                this.logger.error(`Step ${step.id} failed on retry ${retryCount}: ${errorMessage}`, 'WorkflowExecutionService');
            }
        }
        if (errorHandling.fallbackSteps && errorHandling.fallbackSteps.length > 0) {
            this.logger.log(`Executing fallback steps for ${step.id}`, 'WorkflowExecutionService');
            for (const fallbackStepId of errorHandling.fallbackSteps) {
                const fallbackStep = workflow.definition.steps.find(s => s.id === fallbackStepId);
                if (fallbackStep) {
                    try {
                        await this.executeStep(workflow, fallbackStep, context);
                    }
                    catch (fallbackError) {
                        const errorMessage = fallbackError instanceof Error ? fallbackError.message : 'Unknown error';
                        this.logger.error(`Fallback step ${fallbackStepId} failed: ${errorMessage}`, 'WorkflowExecutionService');
                    }
                }
            }
        }
        else {
            context.status = 'failed';
            context.error = `Step ${step.id} failed after ${retryCount} retries: ${error.message}`;
        }
    }
    calculateExecutionOrder(steps) {
        const graph = new Map();
        const inDegree = new Map();
        const executionOrder = [];
        for (const step of steps) {
            graph.set(step.id, step.dependencies || []);
            inDegree.set(step.id, (step.dependencies || []).length);
        }
        const queue = [];
        for (const [stepId, degree] of inDegree) {
            if (degree === 0) {
                queue.push(stepId);
            }
        }
        while (queue.length > 0) {
            const stepId = queue.shift();
            if (stepId) {
                executionOrder.push(stepId);
                for (const [id, dependencies] of graph) {
                    if (dependencies.includes(stepId)) {
                        const currentDegree = inDegree.get(id) || 0;
                        inDegree.set(id, currentDegree - 1);
                        if (currentDegree - 1 === 0) {
                            queue.push(id);
                        }
                    }
                }
            }
        }
        if (executionOrder.length !== steps.length) {
            throw new Error('Circular dependency detected in workflow steps');
        }
        return executionOrder;
    }
    evaluateConditions(conditions, context) {
        for (const [key, value] of Object.entries(conditions)) {
            const contextValue = this.getNestedValue(context.variables, key);
            if (contextValue !== value) {
                return false;
            }
        }
        return true;
    }
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current === null || current === void 0 ? void 0 : current[key], obj);
    }
    async handleExecutionError(context, error) {
        context.status = 'failed';
        context.error = error.message;
        context.lastActivity = new Date();
        await this.finalizeExecution(context);
        this.eventEmitter.emit('workflow.execution.failed', {
            workflowId: context.workflowId,
            executionId: context.executionId,
            error: error.message,
        });
    }
    async finalizeExecution(context) {
        const endTime = new Date();
        const duration = endTime.getTime() - context.startTime.getTime();
        const executionSummary = {
            executionId: context.executionId,
            status: context.status,
            startTime: context.startTime,
            endTime,
            duration,
            stepResults: Array.from(context.stepResults.entries()),
            variables: context.variables,
            error: context.error,
        };
        await this.redisService.set(`workflow:execution:${context.executionId}:summary`, JSON.stringify(executionSummary), 86400);
        this.executionCache.delete(context.executionId);
        await this.redisService.del(`workflow:execution:${context.executionId}`);
        this.eventEmitter.emit('workflow.execution.completed', executionSummary);
    }
    async pauseExecution(executionId) {
        const context = this.executionCache.get(executionId);
        if (context && context.status === 'running') {
            context.status = 'paused';
            context.lastActivity = new Date();
            await this.redisService.set(`workflow:execution:${executionId}`, JSON.stringify(context), 3600);
        }
    }
    async resumeExecution(executionId) {
        const context = this.executionCache.get(executionId);
        if (context && context.status === 'paused') {
            context.status = 'running';
            context.lastActivity = new Date();
            await this.redisService.set(`workflow:execution:${executionId}`, JSON.stringify(context), 3600);
        }
    }
    async cancelExecution(executionId) {
        const context = this.executionCache.get(executionId);
        if (context) {
            context.status = 'failed';
            context.error = 'Execution cancelled by user';
            await this.finalizeExecution(context);
        }
    }
    async getExecutionStatus(executionId) {
        const context = this.executionCache.get(executionId);
        if (!context) {
            const cached = await this.redisService.get(`workflow:execution:${executionId}`);
            if (cached) {
                return JSON.parse(cached);
            }
            return null;
        }
        return context;
    }
    async waitForResume(executionId) {
        return new Promise((resolve) => {
            const checkResume = async () => {
                const context = this.executionCache.get(executionId);
                if (context && context.status === 'running') {
                    resolve();
                }
                else {
                    setTimeout(checkResume, 1000);
                }
            };
            checkResume();
        });
    }
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    generateExecutionId() {
        return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    initializeStepExecutors() {
        this.stepExecutors.set('http_request', async (context, step) => {
            const { url, method = 'GET', headers = {}, body } = step.config;
            const response = await fetch(url, { method, headers, body });
            return { status: response.status, data: await response.json() };
        });
        this.stepExecutors.set('data_transformation', async (context, step) => {
            const { transformation } = step.config;
            return this.applyTransformation(context.variables, transformation);
        });
        this.stepExecutors.set('condition', async (context, step) => {
            const { condition } = step.config;
            return { result: this.evaluateConditions(condition, context) };
        });
        this.stepExecutors.set('loop', async (context, step) => {
            const { iterator, steps: loopSteps } = step.config;
            const items = this.getNestedValue(context.variables, iterator);
            const results = [];
            for (const item of items) {
                const loopContext = Object.assign(Object.assign({}, context), { variables: Object.assign(Object.assign({}, context.variables), { item }) });
                for (const loopStep of loopSteps) {
                    const mockWorkflow = {
                        id: context.workflowId,
                        definition: { steps: [loopStep] }
                    };
                    const result = await this.executeStep(mockWorkflow, loopStep, loopContext);
                    results.push(result);
                }
            }
            return { results };
        });
        this.stepExecutors.set('agent', async (context, step) => {
            const { agentId, input } = step.config;
            return { agentResponse: `Agent ${agentId} processed: ${input}` };
        });
        this.stepExecutors.set('tool', async (context, step) => {
            const { toolId, parameters } = step.config;
            return { toolResult: `Tool ${toolId} executed with parameters: ${JSON.stringify(parameters)}` };
        });
        this.stepExecutors.set('human_approval', async (context, step) => {
            const { message, approvers } = step.config;
            return { approvalStatus: 'pending', message, approvers };
        });
    }
    applyTransformation(data, transformation) {
        return data;
    }
};
exports.WorkflowExecutionService = WorkflowExecutionService;
exports.WorkflowExecutionService = WorkflowExecutionService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [logger_service_1.CustomLoggerService,
        redis_service_1.RedisService,
        event_emitter_1.EventEmitter2])
], WorkflowExecutionService);
//# sourceMappingURL=workflow-execution.service.js.map