import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';

export interface ErrorResponse {
  statusCode: number;
  timestamp: string;
  path: string;
  method: string;
  message: string | string[];
  error?: string;
  details?: any;
  requestId?: string;
}

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  constructor(private configService: ConfigService) {}

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const errorResponse = this.buildErrorResponse(exception, request);
    
    // Log the error
    this.logError(exception, request, errorResponse);

    response.status(errorResponse.statusCode).json(errorResponse);
  }

  private buildErrorResponse(exception: unknown, request: Request): ErrorResponse {
    const timestamp = new Date().toISOString();
    const path = request.url;
    const method = request.method;
    const requestId = request.headers['x-request-id'] as string;

    let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    let message: string | string[] = 'Internal server error';
    let error = 'Internal Server Error';
    let details: any = undefined;

    if (exception instanceof HttpException) {
      statusCode = exception.getStatus();
      const exceptionResponse = exception.getResponse();
      
      if (typeof exceptionResponse === 'string') {
        message = exceptionResponse;
        error = exception.name;
      } else if (typeof exceptionResponse === 'object') {
        message = (exceptionResponse as any).message || exception.message;
        error = (exceptionResponse as any).error || exception.name;
        details = (exceptionResponse as any).details;
      }
    } else if (exception instanceof PrismaClientKnownRequestError) {
      statusCode = this.getPrismaErrorStatus(exception.code);
      message = this.getPrismaErrorMessage(exception);
      error = 'Database Error';
      
      // Don't expose internal database details in production
      if (this.configService.get('app.server.nodeEnv') !== 'production') {
        details = {
          code: exception.code,
          meta: exception.meta,
        };
      }
    } else if (exception instanceof Error) {
      message = exception.message;
      error = exception.name;
      
      // Include stack trace in development
      if (this.configService.get('app.server.nodeEnv') === 'development') {
        details = { stack: exception.stack };
      }
    }

    return {
      statusCode,
      timestamp,
      path,
      method,
      message,
      error,
      ...(details && { details }),
      ...(requestId && { requestId }),
    };
  }

  private logError(exception: unknown, request: Request, errorResponse: ErrorResponse): void {
    const { statusCode, message, path, method } = errorResponse;
    const userAgent = request.get('User-Agent') || '';
    const ip = request.ip;

    const logMessage = `${method} ${path} ${statusCode} - ${userAgent} ${ip}`;

    if (statusCode >= 500) {
      this.logger.error(logMessage, exception instanceof Error ? exception.stack : exception);
    } else if (statusCode >= 400) {
      this.logger.warn(logMessage);
    }

    // Log additional context for debugging
    if (this.configService.get('app.server.nodeEnv') === 'development') {
      this.logger.debug('Request details:', {
        headers: request.headers,
        body: request.body,
        params: request.params,
        query: request.query,
      });
    }
  }

  private getPrismaErrorStatus(code: string): number {
    switch (code) {
      case 'P2002': // Unique constraint violation
        return HttpStatus.CONFLICT;
      case 'P2025': // Record not found
        return HttpStatus.NOT_FOUND;
      case 'P2003': // Foreign key constraint violation
        return HttpStatus.BAD_REQUEST;
      case 'P2004': // Constraint violation
        return HttpStatus.BAD_REQUEST;
      default:
        return HttpStatus.INTERNAL_SERVER_ERROR;
    }
  }

  private getPrismaErrorMessage(exception: PrismaClientKnownRequestError): string {
    switch (exception.code) {
      case 'P2002':
        const target = exception.meta?.target as string[];
        return `A record with this ${target?.join(', ')} already exists`;
      case 'P2025':
        return 'Record not found';
      case 'P2003':
        return 'Invalid reference to related record';
      case 'P2004':
        return 'Constraint violation';
      default:
        return 'Database operation failed';
    }
  }
}
