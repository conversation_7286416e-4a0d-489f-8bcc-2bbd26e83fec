import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export const TenantId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    
    // Extract tenant from subdomain
    const host = request.headers.host;
    if (host) {
      const subdomain = host.split('.')[0];
      if (subdomain && subdomain !== 'www' && subdomain !== 'api') {
        return subdomain;
      }
    }

    // Extract tenant from custom header
    const tenantHeader = request.headers['x-tenant-id'] as string;
    if (tenantHeader) {
      return tenantHeader;
    }

    return undefined;
  },
); 