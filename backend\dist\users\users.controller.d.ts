import { Request as ExpressRequest } from 'express';
import { UserService, CreateUserDto, UpdateUserDto, UserFilters } from './users.service';
interface RequestWithUser extends ExpressRequest {
    user: {
        userId: string;
        organizationId: string;
    };
}
export declare class UserController {
    private readonly userService;
    constructor(userService: UserService);
    createUser(createUserDto: CreateUserDto, req: RequestWithUser): Promise<any>;
    getUsers(filters: UserFilters, req: RequestWithUser): Promise<{
        users: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            pages: number;
        };
    }>;
    getUserById(id: string): Promise<any>;
    updateUser(id: string, updateUserDto: UpdateUserDto, req: RequestWithUser): Promise<any>;
    deleteUser(id: string, req: RequestWithUser): Promise<{
        message: string;
    }>;
    bulkUpdateUsers(body: {
        userIds: string[];
        updateData: Partial<UpdateUserDto>;
    }, req: RequestWithUser): Promise<{
        message: string;
    }>;
    resetPassword(id: string, body: {
        newPassword: string;
    }, req: RequestWithUser): Promise<{
        message: string;
    }>;
}
export {};
