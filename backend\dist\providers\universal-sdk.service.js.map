{"version": 3, "file": "universal-sdk.service.js", "sourceRoot": "", "sources": ["../../src/providers/universal-sdk.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,uFAAiF;AACjF,mEAA8E;AAC9E,sEAAkE;AAClE,6DAAyD;AAgFlD,IAAM,mBAAmB,2BAAzB,MAAM,mBAAmB;IAG9B,YACU,UAAwC,EACxC,YAAiC,EACjC,aAA4B,EAC5B,MAAqB;QAHrB,eAAU,GAAV,UAAU,CAA8B;QACxC,iBAAY,GAAZ,YAAY,CAAqB;QACjC,kBAAa,GAAb,aAAa,CAAe;QAC5B,WAAM,GAAN,MAAM,CAAe;QANd,WAAM,GAAG,IAAI,eAAM,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;IAO3D,CAAC;IAEL,KAAK,CAAC,QAAQ,CAAC,OAA2B;;QACxC,MAAM,SAAS,GAAG,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QACjF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YAEH,IAAI,gBAAgB,GAAG,OAAO,CAAC,QAAQ,CAAC;YACxC,IAAI,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC;YAClC,IAAI,WAAW,GAAQ,IAAI,CAAC;YAE5B,IAAI,CAAC,gBAAgB,IAAI,CAAC,aAAa,EAAE,CAAC;gBACxC,MAAM,cAAc,GAAmB;oBACrC,cAAc,EAAE,OAAO,CAAC,cAAc;oBACtC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,UAAU;oBACxC,UAAU,EAAE,OAAO,CAAC,UAAU;oBAC9B,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;iBAC7C,CAAC;gBAEF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC;gBAC9E,gBAAgB,GAAG,OAAO,CAAC,UAAU,CAAC;gBACtC,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC;gBAChC,WAAW,GAAG,OAAO,CAAC;YACxB,CAAC;YAGD,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,gBAAgB,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC;YAG3F,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,SAAS,KAAI,EAAE,CAAC,CAAC;YAGvF,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAGvC,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;YAG3D,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,YAAY,EAAE,MAAM,CAAC,YAAY;gBACjC,QAAQ,EAAE,MAAM,CAAC,UAAU;gBAC3B,KAAK,EAAE,MAAM,CAAC,OAAO;gBACrB,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,OAAO;gBACP,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,OAAO,EAAE;oBACP,QAAQ,EAAE,gBAAiB;oBAC3B,UAAU,EAAE,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,UAAU,KAAI,GAAG;oBAC1C,SAAS,EAAE,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,SAAS,KAAI,oBAAoB;oBACzD,kBAAkB,EAAE,CAAA,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,SAAS,0CAAE,MAAM,KAAI,CAAC;iBACxD;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAEvC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBACtD,SAAS;gBACT,cAAc,EAAE,OAAO,CAAC,cAAc;gBACtC,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;gBAC/D,OAAO;aACR,CAAC,CAAC;YAEH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,OAA2B;QACtC,MAAM,SAAS,GAAG,UAAU,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAEpF,IAAI,CAAC;YAEH,IAAI,gBAAgB,GAAG,OAAO,CAAC,QAAQ,CAAC;YACxC,IAAI,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC;YAElC,IAAI,CAAC,gBAAgB,IAAI,CAAC,aAAa,EAAE,CAAC;gBACxC,MAAM,cAAc,GAAmB;oBACrC,cAAc,EAAE,OAAO,CAAC,cAAc;oBACtC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,OAAO;oBACrC,iBAAiB,EAAE,IAAI;iBACxB,CAAC;gBAEF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC;gBAC9E,gBAAgB,GAAG,OAAO,CAAC,UAAU,CAAC;gBACtC,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC;YAClC,CAAC;YAGD,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,gBAAgB,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC;YAC3F,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC;YAGxB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YAE9D,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,mBAAmB,EAAE;gBAC/C,SAAS;gBACT,QAAQ,EAAE,gBAAgB;gBAC1B,KAAK,EAAE,aAAa;gBACpB,cAAc,EAAE,OAAO,CAAC,cAAc;gBACtC,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC,CAAC;YAEH,OAAO;gBACL,SAAS;gBACT,QAAQ,EAAE,gBAAiB;gBAC3B,KAAK,EAAE,aAAc;gBACrB,MAAM;aACP,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBACnD,SAAS;gBACT,cAAc,EAAE,OAAO,CAAC,cAAc;gBACtC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAChE,CAAC,CAAC;YAEH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,OAA2B,EAAE,QAAgB,EAAE,KAAa,EAAE,SAAiB;QAEpG,MAAM,QAAQ,GAAsE,EAAE,CAAC;QAGvF,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACzB,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;QACnE,CAAC;QAGD,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,QAAQ,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC;QAGD,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAEzD,OAAO;YACL,SAAS;YACT,UAAU,EAAE,QAAQ;YACpB,OAAO,EAAE,KAAK;YACd,QAAQ;YACR,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;YAC1C,eAAe,EAAE,OAAO,CAAC,eAAe;YACxC,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,MAAM,EAAE,KAAK;SACd,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,SAAc,EAAE,SAAgB;QAChE,MAAM,QAAQ,GAAG;YACf,EAAE,UAAU,EAAE,SAAS,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,CAAC,OAAO,EAAE;YAChE,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;SAC1E,CAAC;QAEF,IAAI,SAAS,GAAiB,IAAI,CAAC;QAEnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACzC,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE5B,IAAI,CAAC;gBACH,MAAM,mBAAmB,mCACpB,SAAS,KACZ,UAAU,EAAE,OAAO,CAAC,UAAU,EAC9B,OAAO,EAAE,OAAO,CAAC,OAAO,GACzB,CAAC;gBAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;gBAGzE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;oBACV,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,kBAAkB,EAAE;wBAC9C,SAAS,EAAE,SAAS,CAAC,SAAS;wBAC9B,gBAAgB,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU;wBACxC,kBAAkB,EAAE,OAAO,CAAC,UAAU;wBACtC,aAAa,EAAE,CAAC,GAAG,CAAC;qBACrB,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO,MAAM,CAAC;YAEhB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;gBAExE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,yBAAyB,EAAE;oBACjD,SAAS,EAAE,SAAS,CAAC,SAAS;oBAC9B,QAAQ,EAAE,OAAO,CAAC,UAAU;oBAC5B,KAAK,EAAE,OAAO,CAAC,OAAO;oBACtB,aAAa,EAAE,CAAC,GAAG,CAAC;oBACpB,KAAK,EAAE,SAAS,CAAC,OAAO;iBACzB,CAAC,CAAC;gBAGH,SAAS;YACX,CAAC;QACH,CAAC;QAGD,MAAM,IAAI,KAAK,CAAC,qCAAqC,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,OAAO,EAAE,CAAC,CAAC;IAC7E,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,OAA2B,EAAE,MAAW,EAAE,OAAe,EAAE,SAAiB;QACnG,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;gBACjC,IAAI,EAAE;oBACJ,EAAE,EAAE,SAAS;oBACb,cAAc,EAAE,OAAO,CAAC,cAAc;oBACtC,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,QAAQ,EAAE,MAAM,CAAC,UAAU;oBAC3B,KAAK,EAAE,MAAM,CAAC,OAAO;oBACrB,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,QAAQ,EAAE,MAAM,CAAC,OAAO;oBACxB,MAAM,EAAE,MAAM,CAAC,KAAK;oBACpB,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,OAAO;oBACP,MAAM,EAAE,WAAW;oBACnB,QAAQ,EAAE;wBACR,QAAQ,EAAE,OAAO,CAAC,QAAQ;wBAC1B,WAAW,EAAE,OAAO,CAAC,WAAW;wBAChC,SAAS,EAAE,OAAO,CAAC,SAAS;wBAC5B,IAAI,EAAE,OAAO,CAAC,IAAI;qBACnB;iBACF;aACF,CAAC,CAAC;YAGH,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,sBAAsB,EAAE;gBAClD,SAAS;gBACT,cAAc,EAAE,OAAO,CAAC,cAAc;gBACtC,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,QAAQ,EAAE,MAAM,CAAC,UAAU;gBAC3B,KAAK,EAAE,MAAM,CAAC,OAAO;gBACrB,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW;gBAChC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,OAAO;aACR,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAID,KAAK,CAAC,kBAAkB,CAAC,cAAsB;QAS7C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YACtD,KAAK,EAAE;gBACL,cAAc;gBACd,QAAQ,EAAE,IAAI;aACf;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE;oBACR,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;iBAC1B;aACF;SACF,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAChC,QAAQ,EAAE,QAAQ,CAAC,IAAI;YACvB,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACtC,EAAE,EAAE,KAAK,CAAC,EAAE;gBACZ,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,YAAY,EAAE,KAAK,CAAC,YAAwB,IAAI,EAAE;gBAClD,YAAY,EAAE,KAAK,CAAC,YAAY,IAAI,CAAC;aACtC,CAAC,CAAC;SACJ,CAAC,CAAC,CAAC;IACN,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,cAAsB,EAAE,YAA+C,KAAK;QAC9F,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,CAAC;YACP,GAAG,EAAE,EAAE;YACP,IAAI,EAAE,EAAE,GAAG,CAAC;YACZ,KAAK,EAAE,EAAE,GAAG,EAAE;SACf,CAAC;QAEF,MAAM,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAE/D,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC;YAClD,KAAK,EAAE;gBACL,cAAc;gBACd,SAAS,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE;gBACzB,MAAM,EAAE,WAAW;aACpB;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,IAAI;aACX;YACD,IAAI,EAAE;gBACJ,OAAO,EAAE,IAAI;aACd;YACD,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;aACT;SACF,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YACtD,KAAK,EAAE;gBACL,cAAc;gBACd,SAAS,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE;gBACzB,MAAM,EAAE,WAAW;aACpB;YACD,MAAM,EAAE;gBACN,MAAM,EAAE,IAAI;aACb;SACF,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACjD,MAAM,MAAM,GAAG,GAAG,CAAC,MAAa,CAAC;YACjC,OAAO,GAAG,GAAG,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,KAAI,CAAC,CAAC,CAAC;QAC1C,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,OAAO;YACL,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC;YAC9B,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;YAC/B,WAAW;YACX,cAAc,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC;YACvC,SAAS;SACV,CAAC;IACJ,CAAC;CACF,CAAA;AA/VY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAKW,8DAA4B;QAC1B,2CAAmB;QAClB,8BAAa;QACpB,8BAAa;GAPpB,mBAAmB,CA+V/B"}