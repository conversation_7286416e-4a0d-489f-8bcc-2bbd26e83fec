import { IsString, IsOptional, IsEnum, IsObject, IsArray, IsUUID, IsDateString, IsNumber, IsBoolean, ValidateNested, Is<PERSON>nt, <PERSON>, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { WorkflowStatus, WorkflowTriggerType } from '../workflow.entity';

export class WorkflowStepDto {
  @ApiProperty({ description: 'Unique identifier for the step' })
  @IsString()
  id!: string;

  @ApiProperty({ description: 'Type of the step' })
  @IsString()
  type!: string;

  @ApiProperty({ description: 'Name of the step' })
  @IsString()
  name!: string;

  @ApiProperty({ description: 'Configuration for the step' })
  @IsObject()
  config!: Record<string, any>;

  @ApiPropertyOptional({ description: 'Dependencies for this step' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  dependencies?: string[];

  @ApiPropertyOptional({ description: 'Conditions for step execution' })
  @IsOptional()
  @IsObject()
  conditions?: Record<string, any>;
}

export class WorkflowTriggerDto {
  @ApiProperty({ description: 'Type of trigger', enum: WorkflowTriggerType })
  @IsEnum(WorkflowTriggerType)
  type!: WorkflowTriggerType;

  @ApiProperty({ description: 'Configuration for the trigger' })
  @IsObject()
  config!: Record<string, any>;
}

export class WorkflowErrorHandlingDto {
  @ApiProperty({ description: 'Number of retry attempts' })
  @IsInt()
  @Min(0)
  @Max(10)
  retryCount!: number;

  @ApiProperty({ description: 'Delay between retries in milliseconds' })
  @IsInt()
  @Min(1000)
  retryDelay!: number;

  @ApiPropertyOptional({ description: 'Fallback steps to execute on failure' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  fallbackSteps?: string[];
}

export class WorkflowDefinitionDto {
  @ApiProperty({ description: 'Version of the workflow definition' })
  @IsString()
  version!: string;

  @ApiProperty({ description: 'Steps in the workflow', type: [WorkflowStepDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkflowStepDto)
  steps!: WorkflowStepDto[];

  @ApiProperty({ description: 'Triggers for the workflow', type: [WorkflowTriggerDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkflowTriggerDto)
  triggers!: WorkflowTriggerDto[];

  @ApiProperty({ description: 'Variables available in the workflow' })
  @IsObject()
  variables!: Record<string, any>;

  @ApiProperty({ description: 'Error handling configuration' })
  @ValidateNested()
  @Type(() => WorkflowErrorHandlingDto)
  errorHandling!: WorkflowErrorHandlingDto;
}

export class WorkflowMetadataDto {
  @ApiProperty({ description: 'Tags for the workflow' })
  @IsArray()
  @IsString({ each: true })
  tags!: string[];

  @ApiProperty({ description: 'Category of the workflow' })
  @IsString()
  category!: string;

  @ApiProperty({ description: 'Priority level', enum: ['low', 'medium', 'high', 'critical'] })
  @IsString()
  priority!: 'low' | 'medium' | 'high' | 'critical';

  @ApiProperty({ description: 'Estimated duration in seconds' })
  @IsNumber()
  estimatedDuration!: number;

  @ApiProperty({ description: 'Estimated cost' })
  @IsNumber()
  costEstimate!: number;
}

export class WorkflowPermissionsDto {
  @ApiProperty({ description: 'User IDs with owner permissions' })
  @IsArray()
  @IsString({ each: true })
  owners!: string[];

  @ApiProperty({ description: 'User IDs with editor permissions' })
  @IsArray()
  @IsString({ each: true })
  editors!: string[];

  @ApiProperty({ description: 'User IDs with viewer permissions' })
  @IsArray()
  @IsString({ each: true })
  viewers!: string[];

  @ApiProperty({ description: 'Whether the workflow is public' })
  @IsBoolean()
  public!: boolean;
}

export class CreateWorkflowDto {
  @ApiProperty({ description: 'Name of the workflow' })
  @IsString()
  name!: string;

  @ApiPropertyOptional({ description: 'Description of the workflow' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Workflow definition' })
  @ValidateNested()
  @Type(() => WorkflowDefinitionDto)
  definition!: WorkflowDefinitionDto;

  @ApiPropertyOptional({ description: 'Metadata for the workflow' })
  @IsOptional()
  @ValidateNested()
  @Type(() => WorkflowMetadataDto)
  metadata?: WorkflowMetadataDto;

  @ApiPropertyOptional({ description: 'Permissions for the workflow' })
  @IsOptional()
  @ValidateNested()
  @Type(() => WorkflowPermissionsDto)
  permissions?: WorkflowPermissionsDto;
}

export class UpdateWorkflowDto {
  @ApiPropertyOptional({ description: 'Name of the workflow' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Description of the workflow' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Workflow definition' })
  @IsOptional()
  @ValidateNested()
  @Type(() => WorkflowDefinitionDto)
  definition?: WorkflowDefinitionDto;

  @ApiPropertyOptional({ description: 'Metadata for the workflow' })
  @IsOptional()
  @ValidateNested()
  @Type(() => WorkflowMetadataDto)
  metadata?: WorkflowMetadataDto;

  @ApiPropertyOptional({ description: 'Permissions for the workflow' })
  @IsOptional()
  @ValidateNested()
  @Type(() => WorkflowPermissionsDto)
  permissions?: WorkflowPermissionsDto;

  @ApiPropertyOptional({ description: 'Force update even if workflow is active' })
  @IsOptional()
  @IsBoolean()
  forceUpdate?: boolean;
}

export class WorkflowFiltersDto {
  @ApiPropertyOptional({ description: 'Search term for workflow name or description' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: 'Filter by workflow status', enum: WorkflowStatus })
  @IsOptional()
  @IsEnum(WorkflowStatus)
  status?: WorkflowStatus;

  @ApiPropertyOptional({ description: 'Filter by user ID' })
  @IsOptional()
  @IsUUID()
  userId?: string;

  @ApiPropertyOptional({ description: 'Filter by creation date after' })
  @IsOptional()
  @IsDateString()
  createdAfter?: string;

  @ApiPropertyOptional({ description: 'Filter by creation date before' })
  @IsOptional()
  @IsDateString()
  createdBefore?: string;

  @ApiPropertyOptional({ description: 'Page number for pagination' })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({ description: 'Number of items per page' })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiPropertyOptional({ description: 'Field to sort by' })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiPropertyOptional({ description: 'Sort order', enum: ['ASC', 'DESC'] })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC';
} 