import { Workflow } from './workflow.entity';
import { CustomLoggerService } from '../logging/logger.service';
import { RedisService } from '../cache/redis.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
export interface WorkflowExecutionContext {
    workflowId: string;
    executionId: string;
    userId: string;
    organizationId: string;
    variables: Record<string, any>;
    stepResults: Map<string, any>;
    currentStep: string | null;
    status: 'running' | 'paused' | 'completed' | 'failed';
    startTime: Date;
    lastActivity: Date;
    error?: string;
    metadata: Record<string, any>;
}
export interface StepExecutionResult {
    stepId: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    startTime: Date;
    endTime?: Date;
    result?: any;
    error?: string;
    duration?: number;
    cost?: number;
    metadata?: Record<string, any>;
}
export declare class WorkflowExecutionService {
    private readonly logger;
    private readonly redisService;
    private readonly eventEmitter;
    private readonly executionCache;
    private readonly stepExecutors;
    constructor(logger: CustomLoggerService, redisService: RedisService, eventEmitter: EventEmitter2);
    executeWorkflow(workflow: Workflow, userId: string, inputVariables?: Record<string, any>): Promise<string>;
    executeWorkflowSteps(workflow: Workflow, context: WorkflowExecutionContext): Promise<void>;
    private executeStep;
    private handleStepFailure;
    private calculateExecutionOrder;
    private evaluateConditions;
    private getNestedValue;
    private handleExecutionError;
    private finalizeExecution;
    pauseExecution(executionId: string): Promise<void>;
    resumeExecution(executionId: string): Promise<void>;
    cancelExecution(executionId: string): Promise<void>;
    getExecutionStatus(executionId: string): Promise<any>;
    private waitForResume;
    private delay;
    private generateExecutionId;
    private initializeStepExecutors;
    private applyTransformation;
}
