import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { Workflow, WorkflowStatus } from './workflow.entity';
import { CreateWorkflowDto, UpdateWorkflowDto, WorkflowFiltersDto } from './dto/workflow.dto';
import { WorkflowValidationService } from './workflow-validation.service';
import { WorkflowAnalyticsService } from './workflow-analytics.service';
import { CustomLoggerService } from '../logging/logger.service';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class WorkflowsService {
    constructor(
        @InjectRepository(Workflow)
        private readonly workflowRepository: Repository<Workflow>,
        private readonly validationService: WorkflowValidationService,
        private readonly analyticsService: WorkflowAnalyticsService,
        private readonly logger: CustomLoggerService,
    ) { }

    async createWorkflow(createWorkflowDto: CreateWorkflowDto, userId: string, organizationId: string): Promise<Workflow> {
        try {
            // Validate workflow definition
            await this.validationService.validateWorkflowDefinition(createWorkflowDto.definition);

            const workflow = this.workflowRepository.create({
                ...createWorkflowDto,
                userId,
                organizationId,
                status: WorkflowStatus.DRAFT,
            });

            const savedWorkflow = await this.workflowRepository.save(workflow);

            this.logger.log(`Workflow created: ${savedWorkflow.id} by user: ${userId}`, 'WorkflowsService');

            return savedWorkflow;
        } catch (error) {
            this.logger.error(`Failed to create workflow: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowsService');
            throw error;
        }
    }

    async getWorkflows(filters: WorkflowFiltersDto, userId: string, organizationId: string): Promise<{ workflows: Workflow[]; total: number }> {
        try {
            const queryBuilder = this.buildWorkflowQuery(filters, userId, organizationId);

            const [workflows, total] = await queryBuilder.getManyAndCount();

            return { workflows, total };
        } catch (error) {
            this.logger.error(`Failed to get workflows: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowsService');
            throw error;
        }
    }

    async getWorkflowById(id: string, userId: string, organizationId: string): Promise<Workflow> {
        try {
            const workflow = await this.workflowRepository.findOne({
                where: { id, organizationId },
                relations: ['user', 'agents'],
            });

            if (!workflow) {
                throw new NotFoundException(`Workflow with ID ${id} not found`);
            }

            // Check permissions
            if (workflow.userId !== userId && !this.hasViewPermission(workflow, userId)) {
                throw new ForbiddenException('Access denied to this workflow');
            }

            return workflow;
        } catch (error) {
            this.logger.error(`Failed to get workflow ${id}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowsService');
            throw error;
        }
    }

    async updateWorkflow(id: string, updateWorkflowDto: UpdateWorkflowDto, userId: string, organizationId: string): Promise<Workflow> {
        try {
            const workflow = await this.getWorkflowById(id, userId, organizationId);

            // Check edit permissions
            if (workflow.userId !== userId && !this.hasEditPermission(workflow, userId)) {
                throw new ForbiddenException('Edit access denied to this workflow');
            }

            // Validate workflow definition if provided
            if (updateWorkflowDto.definition) {
                await this.validationService.validateWorkflowDefinition(updateWorkflowDto.definition);
            }

            // Prevent updates to active workflows unless explicitly allowed
            if (workflow.status === WorkflowStatus.ACTIVE && !updateWorkflowDto.forceUpdate) {
                throw new BadRequestException('Cannot update active workflow without force flag');
            }

            Object.assign(workflow, updateWorkflowDto);
            const updatedWorkflow = await this.workflowRepository.save(workflow);

            this.logger.log(`Workflow updated: ${id} by user: ${userId}`, 'WorkflowsService');

            return updatedWorkflow;
        } catch (error) {
            this.logger.error(`Failed to update workflow ${id}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowsService');
            throw error;
        }
    }

    async deleteWorkflow(id: string, userId: string, organizationId: string): Promise<void> {
        try {
            const workflow = await this.getWorkflowById(id, userId, organizationId);

            // Check delete permissions
            if (workflow.userId !== userId && !this.hasAdminPermission(workflow, userId)) {
                throw new ForbiddenException('Delete access denied to this workflow');
            }

            // Soft delete
            workflow.deletedAt = new Date();
            await this.workflowRepository.save(workflow);

            this.logger.log(`Workflow deleted: ${id} by user: ${userId}`, 'WorkflowsService');
        } catch (error) {
            this.logger.error(`Failed to delete workflow ${id}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowsService');
            throw error;
        }
    }

    async changeWorkflowStatus(id: string, status: WorkflowStatus, userId: string, organizationId: string): Promise<Workflow> {
        try {
            const workflow = await this.getWorkflowById(id, userId, organizationId);

            // Check permissions
            if (workflow.userId !== userId && !this.hasEditPermission(workflow, userId)) {
                throw new ForbiddenException('Status change access denied to this workflow');
            }

            // Validate status transition
            if (!this.isValidStatusTransition(workflow.status, status)) {
                throw new BadRequestException(`Invalid status transition from ${workflow.status} to ${status}`);
            }

            workflow.status = status;

            if (status === WorkflowStatus.ACTIVE) {
                workflow.lastExecutedAt = new Date();
            }

            const updatedWorkflow = await this.workflowRepository.save(workflow);

            this.logger.log(`Workflow status changed: ${id} to ${status} by user: ${userId}`, 'WorkflowsService');

            return updatedWorkflow;
        } catch (error) {
            this.logger.error(`Failed to change workflow status ${id}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowsService');
            throw error;
        }
    }

    async duplicateWorkflow(id: string, userId: string, organizationId: string): Promise<Workflow> {
        try {
            const originalWorkflow = await this.getWorkflowById(id, userId, organizationId);

            // Create a new workflow instance
            const duplicatedWorkflow = new Workflow();
            duplicatedWorkflow.name = `${originalWorkflow.name} (Copy)`;
            duplicatedWorkflow.description = originalWorkflow.description;
            duplicatedWorkflow.definition = originalWorkflow.definition;
            duplicatedWorkflow.metadata = originalWorkflow.metadata;
            duplicatedWorkflow.permissions = originalWorkflow.permissions;
            duplicatedWorkflow.status = WorkflowStatus.DRAFT;
            duplicatedWorkflow.executionCount = 0;
            duplicatedWorkflow.successCount = 0;
            duplicatedWorkflow.failureCount = 0;
            duplicatedWorkflow.averageExecutionTime = 0;
            duplicatedWorkflow.totalCost = 0;
            duplicatedWorkflow.lastExecutedAt = new Date();
            duplicatedWorkflow.nextExecutionAt = new Date();
            duplicatedWorkflow.executionHistory = [];
            duplicatedWorkflow.userId = userId;
            duplicatedWorkflow.organizationId = organizationId;

            const savedWorkflow = await this.workflowRepository.save(duplicatedWorkflow);

            this.logger.log(`Workflow duplicated: ${id} to ${savedWorkflow.id} by user: ${userId}`, 'WorkflowsService');

            return savedWorkflow;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorStack = error instanceof Error ? error.stack : '';
            this.logger.error(`Failed to duplicate workflow ${id}: ${errorMessage}`, errorStack, 'WorkflowsService');
            throw error;
        }
    }

    async getWorkflowAnalytics(id: string, userId: string, organizationId: string): Promise<any> {
        try {
            const workflow = await this.getWorkflowById(id, userId, organizationId);
            return await this.analyticsService.getWorkflowAnalytics(workflow);
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorStack = error instanceof Error ? error.stack : '';
            this.logger.error(`Failed to get workflow analytics ${id}: ${errorMessage}`, errorStack, 'WorkflowsService');
            throw error;
        }
    }

    private buildWorkflowQuery(filters: WorkflowFiltersDto, userId: string, organizationId: string): SelectQueryBuilder<Workflow> {
        const queryBuilder = this.workflowRepository.createQueryBuilder('workflow')
            .leftJoinAndSelect('workflow.user', 'user')
            .where('workflow.organizationId = :organizationId', { organizationId })
            .andWhere('workflow.deletedAt IS NULL');

        // Apply filters
        if (filters.status) {
            queryBuilder.andWhere('workflow.status = :status', { status: filters.status });
        }

        if (filters.search) {
            queryBuilder.andWhere(
                '(workflow.name ILIKE :search OR workflow.description ILIKE :search)',
                { search: `%${filters.search}%` }
            );
        }

        if (filters.userId) {
            queryBuilder.andWhere('workflow.userId = :userId', { userId: filters.userId });
        }

        if (filters.createdAfter) {
            queryBuilder.andWhere('workflow.createdAt >= :createdAfter', { createdAfter: filters.createdAfter });
        }

        if (filters.createdBefore) {
            queryBuilder.andWhere('workflow.createdAt <= :createdBefore', { createdBefore: filters.createdBefore });
        }

        // Apply pagination
        if (filters.page && filters.limit) {
            const offset = (filters.page - 1) * filters.limit;
            queryBuilder.skip(offset).take(filters.limit);
        }

        // Apply sorting
        const sortField = filters.sortBy || 'createdAt';
        const sortOrder = filters.sortOrder || 'DESC';
        queryBuilder.orderBy(`workflow.${sortField}`, sortOrder as 'ASC' | 'DESC');

        return queryBuilder;
    }

    private hasViewPermission(workflow: Workflow, userId: string): boolean {
        return workflow.permissions?.viewers?.includes(userId) || workflow.permissions?.public;
    }

    private hasEditPermission(workflow: Workflow, userId: string): boolean {
        return workflow.permissions?.editors?.includes(userId) || workflow.userId === userId;
    }

    private hasAdminPermission(workflow: Workflow, userId: string): boolean {
        return workflow.permissions?.owners?.includes(userId) || workflow.userId === userId;
    }

    private isValidStatusTransition(currentStatus: WorkflowStatus, newStatus: WorkflowStatus): boolean {
        const validTransitions = {
            [WorkflowStatus.DRAFT]: [WorkflowStatus.ACTIVE, WorkflowStatus.COMPLETED, WorkflowStatus.FAILED, WorkflowStatus.CANCELLED, WorkflowStatus.PAUSED],
            [WorkflowStatus.ACTIVE]: [WorkflowStatus.PAUSED, WorkflowStatus.COMPLETED, WorkflowStatus.CANCELLED, WorkflowStatus.FAILED],
            [WorkflowStatus.PAUSED]: [WorkflowStatus.ACTIVE, WorkflowStatus.CANCELLED, WorkflowStatus.FAILED],
            [WorkflowStatus.COMPLETED]: [WorkflowStatus.ACTIVE, WorkflowStatus.FAILED],
            [WorkflowStatus.FAILED]: [WorkflowStatus.ACTIVE, WorkflowStatus.CANCELLED],
            [WorkflowStatus.CANCELLED]: [WorkflowStatus.ACTIVE, WorkflowStatus.FAILED],
        };

        return validTransitions[currentStatus]?.includes(newStatus) || false;
    }
    async getRealTimeMetrics(id: string, userId: string, organizationId: string): Promise<any> {
        try {
            const workflow = await this.getWorkflowById(id, userId, organizationId) as any;
            return await this.analyticsService.getRealTimeMetrics(workflow.id);
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorStack = error instanceof Error ? error.stack : '';
            this.logger.error(`Failed to get real time metrics ${id}: ${errorMessage}`, errorStack, 'WorkflowsService');
            throw error;
        }
    }
}