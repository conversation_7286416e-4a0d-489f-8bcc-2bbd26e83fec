"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowExecutionModule = void 0;
const common_1 = require("@nestjs/common");
const workflow_runtime_service_1 = require("./workflow-runtime.service");
const prisma_module_1 = require("../../prisma/prisma.module");
const common_module_1 = require("../../common/common.module");
const execution_module_1 = require("../../agents/execution/execution.module");
const execution_module_2 = require("../../tools/execution/execution.module");
let WorkflowExecutionModule = class WorkflowExecutionModule {
};
exports.WorkflowExecutionModule = WorkflowExecutionModule;
exports.WorkflowExecutionModule = WorkflowExecutionModule = __decorate([
    (0, common_1.Module)({
        imports: [
            prisma_module_1.PrismaModule,
            common_module_1.CommonModule,
            execution_module_1.AgentExecutionModule,
            execution_module_2.ToolExecutionModule
        ],
        providers: [workflow_runtime_service_1.WorkflowRuntimeService],
        exports: [workflow_runtime_service_1.WorkflowRuntimeService],
    })
], WorkflowExecutionModule);
//# sourceMappingURL=execution.module.js.map