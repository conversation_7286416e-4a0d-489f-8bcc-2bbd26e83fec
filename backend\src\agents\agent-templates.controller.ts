import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AgentTemplatesService, CreateTemplateDto, TemplateCategory } from './agent-templates.service';
import { AgentTemplate } from '../database/entities/agent.entity';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { PermissionGuard } from '../auth/permission.guard';
import { RequirePermissions } from '../auth/permissions.decorator';

@ApiTags('Agent Templates')
@Controller('agent-templates')
@UseGuards(JwtAuthGuard, PermissionGuard)
@ApiBearerAuth()
export class AgentTemplatesController {
  constructor(private readonly templatesService: AgentTemplatesService) { }

  @Post()
  @RequirePermissions('agents:create', 'agents:read')
  @ApiOperation({ summary: 'Create a new agent template' })
  @ApiResponse({ status: 201, description: 'Template created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async createTemplate(
    @Body() createTemplateDto: CreateTemplateDto,
    @Request() req: any,
  ): Promise<AgentTemplate> {
    return this.templatesService.createTemplate(
      createTemplateDto,
      req.user.organizationId,
      req.user.id,
    );
  }

  @Get()
  @RequirePermissions('agents:read', 'agents:create')
  @ApiOperation({ summary: 'Get all templates for the organization' })
  @ApiResponse({ status: 200, description: 'Templates retrieved successfully' })
  async getTemplates(@Request() req: any): Promise<AgentTemplate[]> {
    return this.templatesService.getTemplatesByOrganization(req.user.organizationId);
  }

  @Get('categories')
  @RequirePermissions('agents:read')
  @ApiOperation({ summary: 'Get template categories with counts' })
  @ApiResponse({ status: 200, description: 'Categories retrieved successfully' })
  async getTemplateCategories(@Request() req: any): Promise<TemplateCategory[]> {
    return this.templatesService.getTemplateCategories(req.user.organizationId);
  }

  @Get('category/:category')
  @RequirePermissions('agents:read')
  @ApiOperation({ summary: 'Get templates by category' })
  @ApiResponse({ status: 200, description: 'Templates retrieved successfully' })
  async getTemplatesByCategory(
    @Param('category') category: string,
    @Request() req: any,
  ): Promise<AgentTemplate[]> {
    return this.templatesService.getTemplatesByCategory(category, req.user.organizationId);
  }

  @Get('search')
  @RequirePermissions('agents:read')
  @ApiOperation({ summary: 'Search templates by query' })
  @ApiResponse({ status: 200, description: 'Search results retrieved successfully' })
  async searchTemplates(
    @Query('q') query: string,
    @Request() req: any,
  ): Promise<AgentTemplate[]> {
    return this.templatesService.searchTemplates(query, req.user.organizationId);
  }

  @Get('popular')
  @RequirePermissions('agents:read')
  @ApiOperation({ summary: 'Get popular templates' })
  @ApiResponse({ status: 200, description: 'Popular templates retrieved successfully' })
  async getPopularTemplates(
    @Query('limit') limit: number = 10,
    @Request() req: any,
  ): Promise<AgentTemplate[]> {
    return this.templatesService.getPopularTemplates(req.user.organizationId, limit);
  }

  @Get(':id')
  @RequirePermissions('agents:read')
  @ApiOperation({ summary: 'Get template by ID' })
  @ApiResponse({ status: 200, description: 'Template retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Template not found' })
  async getTemplate(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<AgentTemplate> {
    return this.templatesService.getTemplateById(id, req.user.organizationId);
  }

  @Put(':id')
  @RequirePermissions('agents:update')
  @ApiOperation({ summary: 'Update template' })
  @ApiResponse({ status: 200, description: 'Template updated successfully' })
  @ApiResponse({ status: 404, description: 'Template not found' })
  async updateTemplate(
    @Param('id') id: string,
    @Body() updateData: Partial<CreateTemplateDto>,
    @Request() req: any,
  ): Promise<AgentTemplate> {
    return this.templatesService.updateTemplate(id, updateData, req.user.organizationId);
  }

  @Delete(':id')
  @RequirePermissions('agents:delete')
  @ApiOperation({ summary: 'Delete template' })
  @ApiResponse({ status: 200, description: 'Template deleted successfully' })
  @ApiResponse({ status: 404, description: 'Template not found' })
  async deleteTemplate(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<void> {
    return this.templatesService.deleteTemplate(id, req.user.organizationId);
  }

  @Post(':id/duplicate')
  @RequirePermissions('agents:create')
  @ApiOperation({ summary: 'Duplicate template' })
  @ApiResponse({ status: 201, description: 'Template duplicated successfully' })
  @ApiResponse({ status: 404, description: 'Template not found' })
  async duplicateTemplate(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<AgentTemplate> {
    return this.templatesService.duplicateTemplate(id, req.user.organizationId, req.user.id);
  }

  @Get(':id/stats')
  @RequirePermissions('agents:read')
  @ApiOperation({ summary: 'Get template usage statistics' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Template not found' })
  async getTemplateStats(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<any> {
    return this.templatesService.getTemplateUsageStats(id, req.user.organizationId);
  }

  @Post('seed-defaults')
  @RequirePermissions('agents:create')
  @ApiOperation({ summary: 'Seed default templates for organization' })
  @ApiResponse({ status: 201, description: 'Default templates seeded successfully' })
  async seedDefaultTemplates(@Request() req: any): Promise<void> {
    return this.templatesService.seedDefaultTemplates(req.user.organizationId, req.user.id);
  }
}