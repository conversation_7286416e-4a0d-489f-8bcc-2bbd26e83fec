import { BaseEntity } from 'typeorm';
import { Tenant } from '../../tenants/entities/tenant.entity';
import { Role } from '../../roles/entities/role.entity';
import { RefreshToken } from '../../auth/entities/refresh-token.entity';
export declare class User extends BaseEntity {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    password: string;
    isActive: boolean;
    lastLoginAt: Date | null;
    tenantId: string | null;
    createdAt: Date;
    updatedAt: Date;
    tenant: Tenant | null;
    roles: Role[];
    refreshTokens: RefreshToken[];
}
