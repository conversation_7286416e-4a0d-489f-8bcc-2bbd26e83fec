import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, ManyToMany, JoinTable, OneToMany, Index, BaseEntity } from 'typeorm';
import { Tenant } from '../../tenants/entities/tenant.entity';
import { Role } from '../../roles/entities/role.entity';
import { RefreshToken } from '../../auth/entities/refresh-token.entity';

@Entity('users')
@Index(['email'], { unique: true })
@Index(['tenantId'])
@Index(['isActive'])
export class User extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id!: string;

    @Column({ unique: true, length: 255 })
    email!: string;

    @Column({ length: 100 })
    firstName!: string;

    @Column({ length: 100 })
    lastName!: string;

    @Column({ length: 255, nullable: true })
    password!: string;

    @Column({ default: true })
    isActive!: boolean;

    @Column({ type: 'timestamp', nullable: true })
    lastLoginAt!: Date | null;

    @Column({ nullable: true })
    tenantId!: string | null;

    @CreateDateColumn()
    createdAt!: Date;

    @UpdateDateColumn()
    updatedAt!: Date;

    @ManyToOne(() => Tenant, tenant => tenant.users, { nullable: true })
    tenant!: Tenant | null;

    @ManyToMany(() => Role, role => role.users)
    @JoinTable({
        name: 'user_roles',
        joinColumn: { name: 'userId', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'roleId', referencedColumnName: 'id' },
    })
    roles!: Role[];

    @OneToMany(() => RefreshToken, refreshToken => refreshToken.user, { cascade: true })
    refreshTokens!: RefreshToken[];
} 