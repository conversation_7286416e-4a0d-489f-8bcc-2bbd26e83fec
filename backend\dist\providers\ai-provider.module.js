"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIProviderModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const schedule_1 = require("@nestjs/schedule");
const ai_provider_entity_1 = require("../database/entities/ai-provider.entity");
const ai_provider_controller_1 = require("./ai-provider.controller");
const ai_provider_manager_service_1 = require("./ai-provider-manager.service");
const ai_provider_selector_service_1 = require("./ai-provider-selector.service");
const ai_provider_integration_service_1 = require("./ai-provider-integration.service");
const ai_provider_stream_service_1 = require("./ai-provider-stream.service");
const universal_sdk_service_1 = require("./universal-sdk.service");
const smart_routing_service_1 = require("./smart-routing.service");
const provider_analytics_service_1 = require("./provider-analytics.service");
const provider_management_controller_1 = require("./provider-management.controller");
const apix_module_1 = require("../websocket/apix.module");
const prisma_module_1 = require("../prisma/prisma.module");
const common_module_1 = require("../common/common.module");
let AIProviderModule = class AIProviderModule {
};
exports.AIProviderModule = AIProviderModule;
exports.AIProviderModule = AIProviderModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                ai_provider_entity_1.AIProvider,
                ai_provider_entity_1.AIModel,
                ai_provider_entity_1.ProviderUsage,
                ai_provider_entity_1.ProviderHealthCheck,
            ]),
            schedule_1.ScheduleModule.forRoot(),
            apix_module_1.ApixModule,
            prisma_module_1.PrismaModule,
            common_module_1.CommonModule,
        ],
        controllers: [ai_provider_controller_1.AIProviderController, provider_management_controller_1.ProviderManagementController],
        providers: [
            ai_provider_manager_service_1.AIProviderManagerService,
            ai_provider_selector_service_1.AIProviderSelectorService,
            ai_provider_integration_service_1.AIProviderIntegrationService,
            ai_provider_stream_service_1.AIProviderStreamService,
            universal_sdk_service_1.UniversalSDKService,
            smart_routing_service_1.SmartRoutingService,
            provider_analytics_service_1.ProviderAnalyticsService,
        ],
        exports: [
            ai_provider_manager_service_1.AIProviderManagerService,
            ai_provider_selector_service_1.AIProviderSelectorService,
            ai_provider_integration_service_1.AIProviderIntegrationService,
            ai_provider_stream_service_1.AIProviderStreamService,
            universal_sdk_service_1.UniversalSDKService,
            smart_routing_service_1.SmartRoutingService,
            provider_analytics_service_1.ProviderAnalyticsService,
        ],
    })
], AIProviderModule);
//# sourceMappingURL=ai-provider.module.js.map