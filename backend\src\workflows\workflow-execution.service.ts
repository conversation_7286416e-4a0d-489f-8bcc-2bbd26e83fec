import { Injectable, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { Workflow, WorkflowStatus } from './workflow.entity';
import { CustomLoggerService } from '../logging/logger.service';
import { RedisService } from '../cache/redis.service';
import { EventEmitter2 } from '@nestjs/event-emitter';

export interface WorkflowExecutionContext {
    workflowId: string;
    executionId: string;
    userId: string;
    organizationId: string;
    variables: Record<string, any>;
    stepResults: Map<string, any>;
    currentStep: string | null;
    status: 'running' | 'paused' | 'completed' | 'failed';
    startTime: Date;
    lastActivity: Date;
    error?: string;
    metadata: Record<string, any>;
}

export interface StepExecutionResult {
    stepId: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    startTime: Date;
    endTime?: Date;
    result?: any;
    error?: string;
    duration?: number;
    cost?: number;
    metadata?: Record<string, any>;
}

@Injectable()
export class WorkflowExecutionService {
    private readonly executionCache = new Map<string, WorkflowExecutionContext>();
    private readonly stepExecutors = new Map<string, (context: WorkflowExecutionContext, step: any) => Promise<any>>();

    constructor(
        private readonly logger: CustomLoggerService,
        private readonly redisService: RedisService,
        private readonly eventEmitter: EventEmitter2,
    ) {
        this.initializeStepExecutors();
    }

    async executeWorkflow(workflow: Workflow, userId: string, inputVariables?: Record<string, any>): Promise<string> {
        try {
            const executionId = this.generateExecutionId();
            const context: WorkflowExecutionContext = {
                workflowId: workflow.id,
                executionId,
                userId,
                organizationId: workflow.organizationId,
                variables: { ...workflow.definition.variables, ...inputVariables },
                stepResults: new Map(),
                currentStep: null,
                status: 'running',
                startTime: new Date(),
                lastActivity: new Date(),
                metadata: {},
            };

            // Store execution context
            this.executionCache.set(executionId, context);
            await this.redisService.set(`workflow:execution:${executionId}`, JSON.stringify(context), 3600);

            // Emit execution started event
            this.eventEmitter.emit('workflow.execution.started', {
                workflowId: workflow.id,
                executionId,
                userId,
                organizationId: workflow.organizationId,
            });

            // Start execution in background
            this.executeWorkflowSteps(workflow, context).catch((error: Error) => {
                this.logger.error(`Workflow execution failed: ${error.message}`, error.stack, 'WorkflowExecutionService');
                this.handleExecutionError(context, error);
            });

            return executionId;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorStack = error instanceof Error ? error.stack : '';
            this.logger.error(`Failed to start workflow execution: ${errorMessage}`, errorStack, 'WorkflowExecutionService');
            throw new InternalServerErrorException('Failed to start workflow execution');
        }
    }

    async executeWorkflowSteps(workflow: Workflow, context: WorkflowExecutionContext): Promise<void> {
        try {
            const { steps } = workflow.definition;
            const executionOrder = this.calculateExecutionOrder(steps);

            for (const stepId of executionOrder) {
                if (context.status === 'paused') {
                    await this.waitForResume(context.executionId);
                }

                if (context.status === 'failed') {
                    break;
                }

                const step = steps.find(s => s.id === stepId);
                if (!step) {
                    throw new Error(`Step ${stepId} not found in workflow`);
                }

                await this.executeStep(workflow, step, context);
            }

            if (context.status === 'running') {
                context.status = 'completed';
                await this.finalizeExecution(context);
            }
        } catch (error) {
            const errorObj = error instanceof Error ? error : new Error('Unknown error');
            this.handleExecutionError(context, errorObj);
        }
    }

    private async executeStep(workflow: Workflow, step: any, context: WorkflowExecutionContext): Promise<void> {
        const stepResult: StepExecutionResult = {
            stepId: step.id,
            status: 'running',
            startTime: new Date(),
            metadata: {},
        };

        try {
            context.currentStep = step.id;
            context.lastActivity = new Date();

            // Check step conditions
            if (step.conditions && !this.evaluateConditions(step.conditions, context)) {
                stepResult.status = 'completed';
                stepResult.result = { skipped: true, reason: 'Conditions not met' };
                context.stepResults.set(step.id, stepResult);
                return;
            }

            // Execute step based on type
            const executor = this.stepExecutors.get(step.type);
            if (!executor) {
                throw new Error(`No executor found for step type: ${step.type}`);
            }

            const result = await executor(context, step);
            stepResult.status = 'completed';
            stepResult.result = result;
            stepResult.endTime = new Date();
            stepResult.duration = stepResult.endTime.getTime() - stepResult.startTime.getTime();

            // Update context variables
            if (result && typeof result === 'object') {
                context.variables = { ...context.variables, ...result };
            }

            context.stepResults.set(step.id, stepResult);

            // Emit step completed event
            this.eventEmitter.emit('workflow.step.completed', {
                workflowId: workflow.id,
                executionId: context.executionId,
                stepId: step.id,
                result,
                duration: stepResult.duration,
            });

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            stepResult.status = 'failed';
            stepResult.error = errorMessage;
            stepResult.endTime = new Date();
            stepResult.duration = stepResult.endTime.getTime() - stepResult.startTime.getTime();

            context.stepResults.set(step.id, stepResult);

            // Handle step failure
            const errorObj = error instanceof Error ? error : new Error(errorMessage);
            await this.handleStepFailure(workflow, step, context, errorObj);
        }
    }

    private async handleStepFailure(workflow: Workflow, step: any, context: WorkflowExecutionContext, error: Error): Promise<void> {
        const { errorHandling } = workflow.definition;
        let retryCount = 0;

        while (retryCount < errorHandling.retryCount && context.status === 'running') {
            retryCount++;
            this.logger.warn(`Retrying step ${step.id} (attempt ${retryCount}/${errorHandling.retryCount})`, 'WorkflowExecutionService');

            await this.delay(errorHandling.retryDelay);

            try {
                await this.executeStep(workflow, step, context);
                this.logger.log(`Step ${step.id} succeeded on retry ${retryCount}`, 'WorkflowExecutionService');
                return;
            } catch (retryError) {
                const errorMessage = retryError instanceof Error ? retryError.message : 'Unknown error';
                this.logger.error(`Step ${step.id} failed on retry ${retryCount}: ${errorMessage}`, 'WorkflowExecutionService');
            }
        }

        // All retries failed, check for fallback steps
        if (errorHandling.fallbackSteps && errorHandling.fallbackSteps.length > 0) {
            this.logger.log(`Executing fallback steps for ${step.id}`, 'WorkflowExecutionService');
            for (const fallbackStepId of errorHandling.fallbackSteps) {
                const fallbackStep = workflow.definition.steps.find(s => s.id === fallbackStepId);
                if (fallbackStep) {
                    try {
                        await this.executeStep(workflow, fallbackStep, context);
                    } catch (fallbackError) {
                        const errorMessage = fallbackError instanceof Error ? fallbackError.message : 'Unknown error';
                        this.logger.error(`Fallback step ${fallbackStepId} failed: ${errorMessage}`, 'WorkflowExecutionService');
                    }
                }
            }
        } else {
            // No fallback steps, fail the workflow
            context.status = 'failed';
            context.error = `Step ${step.id} failed after ${retryCount} retries: ${error.message}`;
        }
    }

    private calculateExecutionOrder(steps: any[]): string[] {
        const graph = new Map<string, string[]>();
        const inDegree = new Map<string, number>();
        const executionOrder: string[] = [];

        // Build graph and calculate in-degrees
        for (const step of steps) {
            graph.set(step.id, step.dependencies || []);
            inDegree.set(step.id, (step.dependencies || []).length);
        }

        // Topological sort
        const queue: string[] = [];
        for (const [stepId, degree] of inDegree) {
            if (degree === 0) {
                queue.push(stepId);
            }
        }

        while (queue.length > 0) {
            const stepId = queue.shift();
            if (stepId) {
                executionOrder.push(stepId);

                for (const [id, dependencies] of graph) {
                    if (dependencies.includes(stepId)) {
                        const currentDegree = inDegree.get(id) || 0;
                        inDegree.set(id, currentDegree - 1);
                        if (currentDegree - 1 === 0) {
                            queue.push(id);
                        }
                    }
                }
            }
        }

        if (executionOrder.length !== steps.length) {
            throw new Error('Circular dependency detected in workflow steps');
        }

        return executionOrder;
    }

    private evaluateConditions(conditions: Record<string, any>, context: WorkflowExecutionContext): boolean {
        // Implement condition evaluation logic
        // This is a simplified version - in production, you'd want a more sophisticated condition engine
        for (const [key, value] of Object.entries(conditions)) {
            const contextValue = this.getNestedValue(context.variables, key);
            if (contextValue !== value) {
                return false;
            }
        }
        return true;
    }

    private getNestedValue(obj: any, path: string): any {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }

    private async handleExecutionError(context: WorkflowExecutionContext, error: Error): Promise<void> {
        context.status = 'failed';
        context.error = error.message;
        context.lastActivity = new Date();

        await this.finalizeExecution(context);

        this.eventEmitter.emit('workflow.execution.failed', {
            workflowId: context.workflowId,
            executionId: context.executionId,
            error: error.message,
        });
    }

    private async finalizeExecution(context: WorkflowExecutionContext): Promise<void> {
        const endTime = new Date();
        const duration = endTime.getTime() - context.startTime.getTime();

        const executionSummary = {
            executionId: context.executionId,
            status: context.status,
            startTime: context.startTime,
            endTime,
            duration,
            stepResults: Array.from(context.stepResults.entries()),
            variables: context.variables,
            error: context.error,
        };

        // Store execution summary
        await this.redisService.set(`workflow:execution:${context.executionId}:summary`, JSON.stringify(executionSummary), 86400);

        // Clean up execution context
        this.executionCache.delete(context.executionId);
        await this.redisService.del(`workflow:execution:${context.executionId}`);

        this.eventEmitter.emit('workflow.execution.completed', executionSummary);
    }

    async pauseExecution(executionId: string): Promise<void> {
        const context = this.executionCache.get(executionId);
        if (context && context.status === 'running') {
            context.status = 'paused';
            context.lastActivity = new Date();
            await this.redisService.set(`workflow:execution:${executionId}`, JSON.stringify(context), 3600);
        }
    }

    async resumeExecution(executionId: string): Promise<void> {
        const context = this.executionCache.get(executionId);
        if (context && context.status === 'paused') {
            context.status = 'running';
            context.lastActivity = new Date();
            await this.redisService.set(`workflow:execution:${executionId}`, JSON.stringify(context), 3600);
        }
    }

    async cancelExecution(executionId: string): Promise<void> {
        const context = this.executionCache.get(executionId);
        if (context) {
            context.status = 'failed';
            context.error = 'Execution cancelled by user';
            await this.finalizeExecution(context);
        }
    }

    async getExecutionStatus(executionId: string): Promise<any> {
        const context = this.executionCache.get(executionId);
        if (!context) {
            const cached = await this.redisService.get(`workflow:execution:${executionId}`);
            if (cached) {
                return JSON.parse(cached);
            }
            return null;
        }
        return context;
    }

    private async waitForResume(executionId: string): Promise<void> {
        return new Promise((resolve) => {
            const checkResume = async () => {
                const context = this.executionCache.get(executionId);
                if (context && context.status === 'running') {
                    resolve();
                } else {
                    setTimeout(checkResume, 1000);
                }
            };
            checkResume();
        });
    }

    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    private generateExecutionId(): string {
        return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    private initializeStepExecutors(): void {
        // HTTP Request Step
        this.stepExecutors.set('http_request', async (context, step) => {
            const { url, method = 'GET', headers = {}, body } = step.config;
            const response = await fetch(url, { method, headers, body });
            return { status: response.status, data: await response.json() };
        });

        // Data Transformation Step
        this.stepExecutors.set('data_transformation', async (context, step) => {
            const { transformation } = step.config;
            // Apply transformation logic to context variables
            return this.applyTransformation(context.variables, transformation);
        });

        // Condition Step
        this.stepExecutors.set('condition', async (context, step) => {
            const { condition } = step.config;
            return { result: this.evaluateConditions(condition, context) };
        });

        // Loop Step
        this.stepExecutors.set('loop', async (context, step) => {
            const { iterator, steps: loopSteps } = step.config;
            const items = this.getNestedValue(context.variables, iterator);
            const results = [];

            for (const item of items) {
                const loopContext = { ...context, variables: { ...context.variables, item } };
                for (const loopStep of loopSteps) {
                    // Create a mock workflow for the loop step execution
                    const mockWorkflow = {
                        id: context.workflowId,
                        definition: { steps: [loopStep] }
                    } as Workflow;
                    const result = await this.executeStep(mockWorkflow, loopStep, loopContext);
                    results.push(result);
                }
            }

            return { results };
        });

        // Agent Step
        this.stepExecutors.set('agent', async (context, step) => {
            const { agentId, input } = step.config;
            // This would integrate with the agent service
            return { agentResponse: `Agent ${agentId} processed: ${input}` };
        });

        // Tool Step
        this.stepExecutors.set('tool', async (context, step) => {
            const { toolId, parameters } = step.config;
            // This would integrate with the tool service
            return { toolResult: `Tool ${toolId} executed with parameters: ${JSON.stringify(parameters)}` };
        });

        // Human Approval Step
        this.stepExecutors.set('human_approval', async (context, step) => {
            const { message, approvers } = step.config;
            // This would create an approval request and wait for response
            return { approvalStatus: 'pending', message, approvers };
        });
    }

    private applyTransformation(data: any, transformation: any): any {
        // Implement data transformation logic
        // This is a simplified version - in production, you'd want a more sophisticated transformation engine
        return data;
    }
} 