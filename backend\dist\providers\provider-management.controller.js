"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProviderManagementController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const swagger_1 = require("@nestjs/swagger");
const universal_sdk_service_1 = require("./universal-sdk.service");
const smart_routing_service_1 = require("./smart-routing.service");
const provider_analytics_service_1 = require("./provider-analytics.service");
const logger_service_1 = require("../common/services/logger.service");
const rxjs_1 = require("rxjs");
let ProviderManagementController = class ProviderManagementController {
    constructor(universalSDK, smartRouting, analytics, logger) {
        this.universalSDK = universalSDK;
        this.smartRouting = smartRouting;
        this.analytics = analytics;
        this.logger = logger;
    }
    async universalComplete(body, req) {
        try {
            const request = Object.assign(Object.assign({}, body), { organizationId: req.user.organizationId, userId: req.user.id });
            const result = await this.universalSDK.complete(request);
            return {
                success: true,
                data: result,
            };
        }
        catch (error) {
            this.logger.error('Universal AI completion failed', {
                userId: req.user.id,
                organizationId: req.user.organizationId,
                error: error instanceof Error ? error.message : 'Unknown error',
            });
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    async universalStream(body, req) {
        try {
            const request = Object.assign(Object.assign({}, body), { organizationId: req.user.organizationId, userId: req.user.id, requiresStreaming: true });
            const result = await this.universalSDK.stream(request);
            return {
                success: true,
                data: {
                    requestId: result.requestId,
                    provider: result.provider,
                    model: result.model,
                    streamUrl: `/providers/ai/stream/${result.requestId}`,
                },
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    async getOptimalProvider(priority = 'balanced', maxLatency, maxCost, requiresStreaming, req) {
        try {
            const routing = await this.smartRouting.selectOptimalProvider({
                organizationId: req.user.organizationId,
                priority,
                maxLatency: maxLatency ? Number(maxLatency) : undefined,
                maxCost: maxCost ? Number(maxCost) : undefined,
                requiresStreaming: requiresStreaming === 'true',
            });
            return {
                success: true,
                data: routing,
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    async getProviderMetrics(req) {
        try {
            const metrics = await this.smartRouting.getProviderMetricsForOrganization(req.user.organizationId);
            return {
                success: true,
                data: metrics,
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    async getOrganizationAnalytics(timeframe = 'day', req) {
        try {
            const report = await this.analytics.generateOrganizationReport(req.user.organizationId, timeframe);
            return {
                success: true,
                data: report,
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    async getProviderAnalytics(providerId, timeframe = 'day', req) {
        try {
            const report = await this.analytics.generateProviderReport(providerId, timeframe);
            return {
                success: true,
                data: report,
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    async getAvailableModels(req) {
        try {
            const models = await this.universalSDK.getAvailableModels(req.user.organizationId);
            return {
                success: true,
                data: models,
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    async getUsageStats(timeframe = 'day', req) {
        try {
            const stats = await this.universalSDK.getUsageStats(req.user.organizationId, timeframe);
            return {
                success: true,
                data: stats,
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    async checkProviderHealth(providerId, req) {
        try {
            const health = await this.smartRouting.checkProviderHealth(providerId);
            return {
                success: true,
                data: health,
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    liveMetrics(req) {
        return (0, rxjs_1.interval)(5000).pipe((0, rxjs_1.map)(async () => {
            try {
                const metrics = await this.smartRouting.getProviderMetricsForOrganization(req.user.organizationId);
                return {
                    data: JSON.stringify({
                        timestamp: new Date().toISOString(),
                        metrics,
                    }),
                };
            }
            catch (error) {
                return {
                    data: JSON.stringify({
                        timestamp: new Date().toISOString(),
                        error: 'Failed to fetch metrics',
                    }),
                };
            }
        }));
    }
    async getDashboardData(req) {
        try {
            const [metrics, analytics, models, usage] = await Promise.all([
                this.smartRouting.getProviderMetricsForOrganization(req.user.organizationId),
                this.analytics.generateOrganizationReport(req.user.organizationId, 'day'),
                this.universalSDK.getAvailableModels(req.user.organizationId),
                this.universalSDK.getUsageStats(req.user.organizationId, 'day'),
            ]);
            return {
                success: true,
                data: {
                    metrics,
                    analytics,
                    models,
                    usage,
                    lastUpdated: new Date().toISOString(),
                },
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
};
exports.ProviderManagementController = ProviderManagementController;
__decorate([
    (0, common_1.Post)('ai/complete'),
    (0, swagger_1.ApiOperation)({ summary: 'Universal AI completion with smart routing' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'AI completion successful' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ProviderManagementController.prototype, "universalComplete", null);
__decorate([
    (0, common_1.Post)('ai/stream'),
    (0, swagger_1.ApiOperation)({ summary: 'Universal AI streaming with smart routing' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'AI streaming started' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ProviderManagementController.prototype, "universalStream", null);
__decorate([
    (0, common_1.Get)('routing/optimal'),
    (0, swagger_1.ApiOperation)({ summary: 'Get optimal provider for request' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Optimal provider selected' }),
    __param(0, (0, common_1.Query)('priority')),
    __param(1, (0, common_1.Query)('maxLatency')),
    __param(2, (0, common_1.Query)('maxCost')),
    __param(3, (0, common_1.Query)('requiresStreaming')),
    __param(4, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number, Boolean, Object]),
    __metadata("design:returntype", Promise)
], ProviderManagementController.prototype, "getOptimalProvider", null);
__decorate([
    (0, common_1.Get)('metrics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get provider metrics for organization' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Provider metrics retrieved' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProviderManagementController.prototype, "getProviderMetrics", null);
__decorate([
    (0, common_1.Get)('analytics/organization'),
    (0, swagger_1.ApiOperation)({ summary: 'Get organization usage analytics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Analytics retrieved' }),
    __param(0, (0, common_1.Query)('timeframe')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ProviderManagementController.prototype, "getOrganizationAnalytics", null);
__decorate([
    (0, common_1.Get)('analytics/provider/:providerId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get provider performance analytics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Provider analytics retrieved' }),
    __param(0, (0, common_1.Param)('providerId')),
    __param(1, (0, common_1.Query)('timeframe')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], ProviderManagementController.prototype, "getProviderAnalytics", null);
__decorate([
    (0, common_1.Get)('models'),
    (0, swagger_1.ApiOperation)({ summary: 'Get available models for organization' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Available models retrieved' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProviderManagementController.prototype, "getAvailableModels", null);
__decorate([
    (0, common_1.Get)('usage/stats'),
    (0, swagger_1.ApiOperation)({ summary: 'Get usage statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Usage statistics retrieved' }),
    __param(0, (0, common_1.Query)('timeframe')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ProviderManagementController.prototype, "getUsageStats", null);
__decorate([
    (0, common_1.Get)('health/:providerId'),
    (0, swagger_1.ApiOperation)({ summary: 'Check provider health' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Provider health checked' }),
    __param(0, (0, common_1.Param)('providerId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ProviderManagementController.prototype, "checkProviderHealth", null);
__decorate([
    (0, common_1.Sse)('metrics/live'),
    (0, swagger_1.ApiOperation)({ summary: 'Live provider metrics stream' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", rxjs_1.Observable)
], ProviderManagementController.prototype, "liveMetrics", null);
__decorate([
    (0, common_1.Get)('dashboard'),
    (0, swagger_1.ApiOperation)({ summary: 'Get provider dashboard data' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Dashboard data retrieved' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProviderManagementController.prototype, "getDashboardData", null);
exports.ProviderManagementController = ProviderManagementController = __decorate([
    (0, swagger_1.ApiTags)('providers'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('providers'),
    __metadata("design:paramtypes", [universal_sdk_service_1.UniversalSDKService,
        smart_routing_service_1.SmartRoutingService,
        provider_analytics_service_1.ProviderAnalyticsService,
        logger_service_1.LoggerService])
], ProviderManagementController);
//# sourceMappingURL=provider-management.controller.js.map