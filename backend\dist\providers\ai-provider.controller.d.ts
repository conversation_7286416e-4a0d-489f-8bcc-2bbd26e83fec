import { AIProviderManagerService } from './ai-provider-manager.service';
import { AIProviderSelectorService } from './ai-provider-selector.service';
import { AIProviderIntegrationService } from './ai-provider-integration.service';
import { CreateAIProviderDto, UpdateAIProviderDto, AIRequestDto, ProviderSelectionDto, ProviderTestDto } from './dto/ai-provider.dto';
export declare class AIProviderController {
    private readonly providerManager;
    private readonly providerSelector;
    private readonly providerIntegration;
    constructor(providerManager: AIProviderManagerService, providerSelector: AIProviderSelectorService, providerIntegration: AIProviderIntegrationService);
    createProvider(createProviderDto: CreateAIProviderDto, req: any): Promise<{
        success: boolean;
        data: import("../database/entities/ai-provider.entity").AIProvider;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
    getProviders(req: any): Promise<{
        success: boolean;
        data: import("../database/entities/ai-provider.entity").AIProvider[];
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
    getActiveProviders(req: any): Promise<{
        success: boolean;
        data: import("../database/entities/ai-provider.entity").AIProvider[];
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
    getProviderRankings(req: any): Promise<{
        success: boolean;
        data: import("./ai-provider-selector.service").ProviderScore[];
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
    getProvider(id: string, req: any): Promise<{
        success: boolean;
        data: import("../database/entities/ai-provider.entity").AIProvider;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
    updateProvider(id: string, updateProviderDto: UpdateAIProviderDto, req: any): Promise<{
        success: boolean;
        data: import("../database/entities/ai-provider.entity").AIProvider;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
    deleteProvider(id: string, req: any): Promise<{
        success: boolean;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
    }>;
    selectProvider(selectionDto: ProviderSelectionDto, req: any): Promise<{
        success: boolean;
        data: {
            providerId: string;
            modelId: string;
            fallbackChain: string[];
            reasoning: string;
        };
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
    processRequest(requestDto: AIRequestDto, req: any): Promise<{
        success: boolean;
        data: import("./ai-provider-integration.service").AIResponse;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
    testProvider(id: string, testDto: ProviderTestDto, req: any): Promise<{
        success: boolean;
        data: any;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
    getUsageStats(startDate?: string, endDate?: string, req?: any): Promise<{
        success: boolean;
        data: {
            totalRequests: number;
            totalTokens: number;
            totalCostInCents: number;
            providerBreakdown: Array<{
                providerId: string;
                providerName: string;
                requests: number;
                tokens: number;
                costInCents: number;
            }>;
        };
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message: string;
        data?: undefined;
    }>;
}
