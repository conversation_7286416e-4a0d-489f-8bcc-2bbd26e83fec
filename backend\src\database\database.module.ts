import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { User } from '../users/entities/user.entity';
import { Role } from '../roles/entities/role.entity';
import { Permission } from '../permissions/entities/permission.entity';
import { Tenant } from '../tenants/entities/tenant.entity';
import { RefreshToken } from '../auth/entities/refresh-token.entity';

@Module({
    imports: [
        TypeOrmModule.forRootAsync({
            imports: [ConfigModule],
            useFactory: (configService: ConfigService) => ({
                type: 'postgres',
                host: configService.get('app.database.host'),
                port: configService.get('app.database.port'),
                username: configService.get('app.database.username'),
                password: configService.get('app.database.password'),
                database: configService.get('app.database.database'),
                entities: [User, Role, Permission, Tenant, RefreshToken],
                synchronize: configService.get('app.database.synchronize'),
                logging: configService.get('app.database.logging'),
                ssl: configService.get('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
                migrations: ['dist/database/migrations/*.js'],
                migrationsRun: true,
                migrationsTableName: 'migrations',
                cache: {
                    type: 'redis',
                    options: {
                        host: configService.get('app.redis.host'),
                        port: configService.get('app.redis.port'),
                        password: configService.get('app.redis.password'),
                        db: configService.get('app.redis.db'),
                    },
                },
                extra: {
                    max: 20,
                    connectionTimeoutMillis: 5000,
                    idleTimeoutMillis: 30000,
                },
            }),
            inject: [ConfigService],
        }),
    ],
})
export class DatabaseModule { } 