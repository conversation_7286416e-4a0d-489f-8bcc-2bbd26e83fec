{"version": 3, "file": "tenant.entity.js", "sourceRoot": "", "sources": ["../../../src/tenants/entities/tenant.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAA8H;AAC9H,kEAAwD;AAGjD,IAAM,MAAM,GAAZ,MAAM,MAAO,SAAQ,oBAAU;CAwBrC,CAAA;AAxBY,wBAAM;AAEf;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;kCACnB;AAGZ;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;oCACX;AAGd;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;sCACT;AAGhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;wCACR;AAG/B;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;;sCACc;AAG5C;IADC,IAAA,0BAAgB,GAAE;8BACP,IAAI;yCAAC;AAGjB;IADC,IAAA,0BAAgB,GAAE;8BACP,IAAI;yCAAC;AAGjB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC;;qCAC5B;iBAvBN,MAAM;IADlB,IAAA,gBAAM,EAAC,SAAS,CAAC;GACL,MAAM,CAwBlB"}