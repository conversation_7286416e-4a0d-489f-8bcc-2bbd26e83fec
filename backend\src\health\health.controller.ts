import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthService } from './health.service';

@ApiTags('health')
@Controller('health')
export class HealthController {
    constructor(private readonly healthService: HealthService) { }

    @Get()
    @ApiOperation({ summary: 'Get application health status' })
    @ApiResponse({ status: 200, description: 'Health check successful' })
    async getHealth() {
        return await this.healthService.checkHealth();
    }

    @Get('detailed')
    @ApiOperation({ summary: 'Get detailed application health status' })
    @ApiResponse({ status: 200, description: 'Detailed health check successful' })
    async getDetailedHealth() {
        return await this.healthService.getDetailedHealth();
    }
} 