import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class ApiXLatencyTrackerService {
    private readonly logger = new Logger(ApiXLatencyTrackerService.name);

    async trackLatency(sessionId: string, latency: number): Promise<void> {
        this.logger.log(`Tracking latency for session ${sessionId}: ${latency}ms`);
        // Implementation for tracking connection latency
    }
} 