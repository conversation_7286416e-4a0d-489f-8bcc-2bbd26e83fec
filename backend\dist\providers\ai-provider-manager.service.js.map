{"version": 3, "file": "ai-provider-manager.service.js", "sourceRoot": "", "sources": ["../../src/providers/ai-provider-manager.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAyE;AACzE,6CAAmD;AACnD,qCAAqC;AACrC,0DAAsD;AACtD,gFAA2G;AAC3G,4DAAwD;AAIjD,IAAM,wBAAwB,gCAA9B,MAAM,wBAAwB;IAGnC,YAEE,kBAAkD,EAElD,eAA4C,EAE5C,eAAkD,EACjC,YAA0B,EACnC,WAAwB;QANxB,uBAAkB,GAAlB,kBAAkB,CAAwB;QAE1C,oBAAe,GAAf,eAAe,CAAqB;QAEpC,oBAAe,GAAf,eAAe,CAA2B;QACjC,iBAAY,GAAZ,YAAY,CAAc;QACnC,gBAAW,GAAX,WAAW,CAAa;QAVjB,WAAM,GAAG,IAAI,eAAM,CAAC,0BAAwB,CAAC,IAAI,CAAC,CAAC;IAWhE,CAAC;IAEL,KAAK,CAAC,cAAc,CAClB,iBAAsC,EACtC,cAAsB,EACtB,MAAc;QAEd,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,IAAI,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAEpF,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;gBAC9C,IAAI,EAAE,iBAAiB,CAAC,IAAI;gBAC5B,IAAI,EAAE,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAkB;gBAC1D,MAAM,EAAE,iBAAiB,CAAC,MAAM;gBAChC,QAAQ,EAAE,IAAI;gBACd,cAAc;gBACd,SAAS,EAAE,MAAM;aAClB,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAGnE,IAAI,iBAAiB,CAAC,MAAM,IAAI,iBAAiB,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpE,MAAM,MAAM,GAAG,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CACtD,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;oBAC1B,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,UAAU,EAAE,aAAa,CAAC,EAAE;oBAC5B,YAAY,EAAE;wBACZ,IAAI,EAAE,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC;wBAC7C,UAAU,EAAE,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC;wBACzD,SAAS,EAAE,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC;wBACvD,MAAM,EAAE,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;wBACjD,eAAe,EAAE,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,iBAAiB,CAAC;wBACnE,cAAc,EAAE,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,gBAAgB,CAAC;wBACjE,QAAQ,EAAE,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC;wBACrD,UAAU,EAAE,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC;wBACzD,SAAS,EAAE,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC;qBACxD;oBACD,aAAa,EAAE,SAAS,CAAC,aAAa;oBACtC,SAAS,EAAE,SAAS,CAAC,SAAS;iBAC/B,CAAC,CACH,CAAC;gBACF,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1C,CAAC;YAGD,MAAM,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;YAG9C,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,cAAc,EAAE,kBAAkB,EAAE;gBACtE,UAAU,EAAE,aAAa,CAAC,EAAE;gBAC5B,cAAc;gBACd,IAAI,EAAE,aAAa,CAAC,IAAI;gBACxB,IAAI,EAAE,aAAa,CAAC,IAAI;gBACxB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,aAAa,CAAC,EAAE,sBAAsB,cAAc,EAAE,CAAC,CAAC;YAChG,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YACjK,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,UAAkB,EAClB,iBAAsC,EACtC,cAAsB;QAEtB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;gBACrD,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE,cAAc,EAAE;aAC1C,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;YACxC,CAAC;YAGD,IAAI,iBAAiB,CAAC,MAAM,EAAE,CAAC;gBAC7B,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,IAAI,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAC7E,CAAC;YAED,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;YAC3C,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAGrE,MAAM,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;YAGhD,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,cAAc,EAAE,kBAAkB,EAAE;gBACtE,UAAU,EAAE,eAAe,CAAC,EAAE;gBAC9B,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBACvC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,UAAU,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YAC/K,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,UAAkB,EAAE,cAAsB;QAC7D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;gBAClD,EAAE,EAAE,UAAU;gBACd,cAAc;aACf,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBAC1B,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;YACxC,CAAC;YAGD,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,UAAU,EAAE,CAAC,CAAC;YAGtD,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,cAAc,EAAE,kBAAkB,EAAE;gBACtE,UAAU;gBACV,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,UAAU,EAAE,CAAC,CAAC;QACxD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,UAAU,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YAC/K,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,0BAA0B,CAAC,cAAsB;QACrD,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAClC,KAAK,EAAE,EAAE,cAAc,EAAE;YACzB,SAAS,EAAE,CAAC,QAAQ,CAAC;YACrB,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,UAAkB,EAAE,cAAsB;QAC9D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE,cAAc,EAAE;YACzC,SAAS,EAAE,CAAC,QAAQ,CAAC;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;QACxC,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,cAAsB;QAC7C,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAClC,KAAK,EAAE,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE;YACzC,SAAS,EAAE,CAAC,QAAQ,CAAC;YACrB,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU,CACd,UAAkB,EAClB,KAIC;QAED,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;YACzB,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAE3B,IAAI,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;gBACnD,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE;aACnC,CAAC,CAAC;YAEH,IAAI,WAAW,EAAE,CAAC;gBAChB,WAAW,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC;gBACvC,WAAW,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC;gBAC3C,WAAW,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,CAAC;YAC/C,CAAC;iBAAM,CAAC;gBACN,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,iBACvC,UAAU,EACV,IAAI,EAAE,KAAK,IACR,KAAK,EACR,CAAC;YACL,CAAC;YAED,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAG7C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;YACtF,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,QAAQ,CAAC,cAAc,EAAE,uBAAuB,EAAE;oBACpF,UAAU;oBACV,IAAI,EAAE,KAAK;oBACX,QAAQ,EAAE,KAAK,CAAC,QAAQ;oBACxB,UAAU,EAAE,KAAK,CAAC,UAAU;oBAC5B,WAAW,EAAE,KAAK,CAAC,WAAW;oBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,UAAU,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QACvL,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CACjB,cAAsB,EACtB,SAAgB,EAChB,OAAc;QAad,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,cAAc,CAAC,CAAC;YACxE,MAAM,WAAW,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAE7C,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,OAAO,CAAC;iBAC3D,KAAK,CAAC,uCAAuC,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;YAEnE,IAAI,SAAS,EAAE,CAAC;gBACd,KAAK,CAAC,QAAQ,CAAC,0BAA0B,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;YAC5D,CAAC;YACD,IAAI,OAAO,EAAE,CAAC;gBACZ,KAAK,CAAC,QAAQ,CAAC,wBAAwB,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;YACxD,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;YAE3C,IAAI,aAAa,GAAG,CAAC,CAAC;YACtB,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,IAAI,gBAAgB,GAAG,CAAC,CAAC;YAEzB,MAAM,iBAAiB,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBACjD,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,KAAK,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAC7E,MAAM,QAAQ,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;gBACvE,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;gBACvE,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;gBAE7E,aAAa,IAAI,QAAQ,CAAC;gBAC1B,WAAW,IAAI,MAAM,CAAC;gBACtB,gBAAgB,IAAI,WAAW,CAAC;gBAEhC,OAAO;oBACL,UAAU,EAAE,QAAQ,CAAC,EAAE;oBACvB,YAAY,EAAE,QAAQ,CAAC,IAAI;oBAC3B,QAAQ;oBACR,MAAM;oBACN,WAAW;iBACZ,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,OAAO;gBACL,aAAa;gBACb,WAAW;gBACX,gBAAgB;gBAChB,iBAAiB;aAClB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8CAA8C,cAAc,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YACjM,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,IAAY,EAAE,MAAW;QAC5D,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,QAAQ;gBACX,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;gBAChD,CAAC;gBACD,MAAM;YACR,KAAK,QAAQ;gBACX,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;gBAChD,CAAC;gBACD,MAAM;YACR,KAAK,QAAQ;gBACX,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;gBAChD,CAAC;gBACD,MAAM;YACR,KAAK,SAAS;gBACZ,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;gBACjD,CAAC;gBACD,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;gBAC9C,CAAC;gBACD,MAAM;YACR;gBACE,MAAM,IAAI,KAAK,CAAC,8BAA8B,IAAI,EAAE,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,QAAoB;QACpD,MAAM,QAAQ,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC;QAC3C,MAAM,SAAS,GAAG;YAChB,EAAE,EAAE,QAAQ,CAAC,EAAE;YACf,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,cAAc,EAAE,QAAQ,CAAC,cAAc;SACxC,CAAC;QAEF,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,CAAC;IACzE,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,UAAkB;QACxC,MAAM,QAAQ,GAAG,YAAY,UAAU,EAAE,CAAC;QAC1C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAEzD,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAA;AAvVY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,+BAAU,CAAC,CAAA;IAE5B,WAAA,IAAA,0BAAgB,EAAC,4BAAO,CAAC,CAAA;IAEzB,WAAA,IAAA,0BAAgB,EAAC,kCAAa,CAAC,CAAA;qCAHJ,oBAAU;QAEb,oBAAU;QAEV,oBAAU;QACJ,4BAAY;QACtB,0BAAW;GAXvB,wBAAwB,CAuVpC"}