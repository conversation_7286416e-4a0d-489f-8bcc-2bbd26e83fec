import { LoggerService as NestLoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
export interface LogContext {
    userId?: string;
    organizationId?: string;
    requestId?: string;
    ip?: string;
    userAgent?: string;
    [key: string]: any;
}
export declare class LoggerService implements NestLoggerService {
    private configService;
    private logger;
    constructor(configService: ConfigService);
    log(message: string, context?: LogContext): void;
    error(message: string, trace?: string, context?: LogContext): void;
    warn(message: string, context?: LogContext): void;
    debug(message: string, context?: LogContext): void;
    verbose(message: string, context?: LogContext): void;
    audit(event: string, details: any, context?: LogContext): void;
    performance(operation: string, duration: number, context?: LogContext): void;
    business(event: string, details: any, context?: LogContext): void;
    security(event: string, details: any, context?: LogContext): void;
}
