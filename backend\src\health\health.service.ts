import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '../cache/redis.service';
import { CustomLoggerService } from '../logging/logger.service';
import { DataSource } from 'typeorm';

export interface HealthCheckResult {
    status: 'healthy' | 'unhealthy' | 'degraded';
    timestamp: string;
    uptime: number;
    version: string;
    environment: string;
    checks: {
        database: HealthCheck;
        redis: HealthCheck;
        memory: HealthCheck;
        disk: HealthCheck;
    };
}

export interface HealthCheck {
    status: 'healthy' | 'unhealthy';
    responseTime: number;
    details?: Record<string, any>;
}

@Injectable()
export class HealthService {
    constructor(
        private readonly configService: ConfigService,
        private readonly redisService: RedisService,
        private readonly dataSource: DataSource,
        private readonly logger: CustomLoggerService,
    ) { }

    async checkHealth(): Promise<HealthCheckResult> {
        const startTime = Date.now();
        const checks = {
            database: await this.checkDatabase(),
            redis: await this.checkRedis(),
            memory: await this.checkMemory(),
            disk: await this.checkDisk(),
        };

        const overallStatus = this.determineOverallStatus(checks);
        const responseTime = Date.now() - startTime;

        const result: HealthCheckResult = {
            status: overallStatus,
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            version: process.env.npm_package_version || '1.0.0',
            environment: this.configService.get('app.environment') || 'development',
            checks,
        };

        this.logger.logWithMeta('Health check completed', {
            status: overallStatus,
            responseTime,
            checks,
        }, 'HealthService');

        return result;
    }

    private async checkDatabase(): Promise<HealthCheck> {
        const startTime = Date.now();
        try {
            await this.dataSource.query('SELECT 1');
            const responseTime = Date.now() - startTime;

            return {
                status: 'healthy',
                responseTime,
                details: {
                    type: 'postgres',
                    host: this.configService.get('app.database.host'),
                    port: this.configService.get('app.database.port'),
                    database: this.configService.get('app.database.database'),
                },
            };
        } catch (error) {
            const responseTime = Date.now() - startTime;
            this.logger.error('Database health check failed', error instanceof Error ? error.stack : 'Unknown error', 'HealthService');

            return {
                status: 'unhealthy',
                responseTime,
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                },
            };
        }
    }

    private async checkRedis(): Promise<HealthCheck> {
        const startTime = Date.now();
        try {
            const ping = await this.redisService.ping();
            const responseTime = Date.now() - startTime;

            if (ping === 'PONG') {
                return {
                    status: 'healthy',
                    responseTime,
                    details: {
                        host: this.configService.get('app.redis.host'),
                        port: this.configService.get('app.redis.port'),
                    },
                };
            } else {
                throw new Error('Redis ping failed');
            }
        } catch (error) {
            const responseTime = Date.now() - startTime;
            this.logger.error('Redis health check failed', error instanceof Error ? error.stack : 'Unknown error', 'HealthService');

            return {
                status: 'unhealthy',
                responseTime,
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                },
            };
        }
    }

    private async checkMemory(): Promise<HealthCheck> {
        const startTime = Date.now();
        try {
            const memUsage = process.memoryUsage();
            const responseTime = Date.now() - startTime;

            const memoryUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
            const isHealthy = memoryUsagePercent < 90; // 90% threshold

            return {
                status: isHealthy ? 'healthy' : 'unhealthy',
                responseTime,
                details: {
                    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
                    heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
                    external: Math.round(memUsage.external / 1024 / 1024), // MB
                    rss: Math.round(memUsage.rss / 1024 / 1024), // MB
                    usagePercent: Math.round(memoryUsagePercent),
                },
            };
        } catch (error) {
            const responseTime = Date.now() - startTime;
            this.logger.error('Memory health check failed', error instanceof Error ? error.stack : 'Unknown error', 'HealthService');

            return {
                status: 'unhealthy',
                responseTime,
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                },
            };
        }
    }

    private async checkDisk(): Promise<HealthCheck> {
        const startTime = Date.now();
        try {
            // This is a simplified disk check
            // In production, you might want to check specific directories
            const responseTime = Date.now() - startTime;

            return {
                status: 'healthy',
                responseTime,
                details: {
                    message: 'Disk space check passed',
                },
            };
        } catch (error) {
            const responseTime = Date.now() - startTime;
            this.logger.error('Disk health check failed', error instanceof Error ? error.stack : 'Unknown error', 'HealthService');

            return {
                status: 'unhealthy',
                responseTime,
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                },
            };
        }
    }

    private determineOverallStatus(checks: HealthCheckResult['checks']): 'healthy' | 'unhealthy' | 'degraded' {
        const statuses = Object.values(checks).map(check => check.status);

        if (statuses.every(status => status === 'healthy')) {
            return 'healthy';
        }

        if (statuses.some(status => status === 'unhealthy')) {
            return 'unhealthy';
        }

        return 'degraded';
    }

    async getDetailedHealth(): Promise<HealthCheckResult & { system: Record<string, any> }> {
        const basicHealth = await this.checkHealth();

        return {
            ...basicHealth,
            system: {
                nodeVersion: process.version,
                platform: process.platform,
                arch: process.arch,
                pid: process.pid,
                memoryUsage: process.memoryUsage(),
                cpuUsage: process.cpuUsage(),
                env: this.configService.get('app.environment'),
            },
        };
    }
} 