import { AgentOrchestratorService, AgentWizardData, AgentTestResult } from './agent-orchestrator.service';
import { Agent, AgentExecution } from '../database/entities/agent.entity';
import { CreateAgentInstanceDto, UpdateAgentInstanceDto, ExecuteAgentDto } from './dto/agent.dto';
export declare class AgentsController {
    private readonly agentOrchestratorService;
    constructor(agentOrchestratorService: AgentOrchestratorService);
    createAgent(createAgentDto: CreateAgentInstanceDto, req: any): Promise<Agent>;
    createAgentFromWizard(wizardData: AgentWizardData, req: any): Promise<Agent>;
    getAgentWizardSteps(): Promise<any[]>;
    getAgents(req: any): Promise<Agent[]>;
    getAgent(id: string, req: any): Promise<Agent>;
    updateAgent(id: string, updateAgentDto: UpdateAgentInstanceDto, req: any): Promise<Agent>;
    deleteAgent(id: string, req: any): Promise<void>;
    executeAgent(id: string, executeDto: ExecuteAgentDto, req: any): Promise<AgentExecution>;
    testAgent(id: string, body: {
        message: string;
    }, req: any): Promise<AgentTestResult>;
    duplicateAgent(id: string, req: any): Promise<Agent>;
    getAgentExecutions(id: string, req: any): Promise<AgentExecution[]>;
    getAgentAnalytics(id: string, req: any): Promise<any>;
    createCollaboration(body: {
        name: string;
        agentIds: string[];
        coordinatorId: string;
        workflow: Record<string, any>;
    }, req: any): Promise<any>;
    executeCollaboration(id: string, body: {
        input: string;
    }, req: any): Promise<any>;
    getCollaborations(req: any): Promise<any[]>;
    getAgentsOverview(req: any): Promise<any>;
    getAgentsPerformance(req: any): Promise<any>;
    searchAgents(query: string, type?: string, status?: string, provider?: string, req: any): Promise<Agent[]>;
    enableAgent(id: string, req: any): Promise<Agent>;
    disableAgent(id: string, req: any): Promise<Agent>;
    previewTemplate(templateId: string, req: any): Promise<any>;
    bulkActions(body: {
        agentIds: string[];
        action: 'enable' | 'disable' | 'delete';
    }, req: any): Promise<any>;
}
