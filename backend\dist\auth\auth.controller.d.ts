import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    login(loginDto: LoginDto, tenantId?: string): Promise<{
        accessToken: string;
        refreshToken: string;
        user: {
            id: string;
            email: string;
            name: any;
            roles: {
                id: string;
                name: string;
                permissions: {
                    id: string;
                    name: string;
                    resource: string;
                    action: string;
                }[];
            }[];
            tenant: {
                id: string;
                name: string;
                domain: string;
            } | null;
        };
    }>;
    register(registerDto: RegisterDto, tenantId?: string): Promise<{
        accessToken: string;
        refreshToken: string;
        user: {
            id: any;
            email: any;
            name: any;
            roles: any;
            tenant: {
                id: any;
                name: any;
                domain: any;
            } | null;
        };
    }>;
    refreshToken(refreshTokenDto: RefreshTokenDto): Promise<{
        accessToken: string;
        refreshToken: string;
    }>;
    logout(user: any, body?: {
        refreshToken?: string;
    }): Promise<{
        message: string;
    }>;
    changePassword(user: any, changePasswordDto: ChangePasswordDto): Promise<{
        message: string;
    }>;
    getCurrentUser(user: any): Promise<{
        id: any;
        email: any;
        roles: any;
        tenantId: any;
    }>;
    getUserPermissions(user: any): Promise<string[]>;
    verifyPermission(user: any, body: {
        resource: string;
        action: string;
    }): Promise<{
        hasPermission: boolean;
        resource: string;
        action: string;
    }>;
}
