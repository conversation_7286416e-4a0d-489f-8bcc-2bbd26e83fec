import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { RedisService } from '../cache/redis.service';
import { ApiXConnectionGuard } from './guards/apix-connection.guard';
import { RateLimitGuard } from '../common/guards/rate-limit.guard';
import { ApiXConnectionService } from './services/apix-connection.service';
import { ApiXEventService } from './services/apix-event.service';
import { ApiXSubscriptionService } from './services/apix-subscription.service';
import { ApiXMessageQueueService } from './services/apix-message-queue.service';
import { ApiXLatencyTrackerService } from './services/apix-latency-tracker.service';
import { ApiXAuditLoggerService } from './services/apix-audit-logger.service';

export interface ApiXConnection {
  sessionId: string;
  organizationId: string;
  userId?: string;
  clientType: 'WEB_APP' | 'MOBILE_APP' | 'SDK_WIDGET' | 'API_CLIENT' | 'INTERNAL_SERVICE';
  channels: string[];
  metadata: Record<string, any>;
  connectedAt: Date;
  lastHeartbeat: Date;
  status: 'CONNECTED' | 'DISCONNECTED' | 'RECONNECTING' | 'SUSPENDED';
}

export interface ApiXEvent {
  type: string;
  channel: string;
  payload: any;
  metadata?: {
    timestamp: number;
    version: string;
    correlation_id?: string;
  };
}

export interface ApiXSubscription {
  channels: string[];
  filters?: Record<string, any>;
  acknowledgment: boolean;
}

@WebSocketGateway({
  namespace: '/apix',
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
  transports: ['websocket'],
})
@UseGuards(ApiXConnectionGuard, RateLimitGuard)
export class ApiXGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server!: Server;

  private readonly logger = new Logger(ApiXGateway.name);
  private readonly connections = new Map<string, ApiXConnection>();
  private readonly subscriptions = new Map<string, Set<string>>(); // sessionId -> channels
  private readonly channelSubscribers = new Map<string, Set<string>>(); // channel -> sessionIds

  constructor(
    private readonly jwtService: JwtService,
    private readonly eventEmitter: EventEmitter2,
    private readonly redisService: RedisService,
    private readonly connectionService: ApiXConnectionService,
    private readonly eventService: ApiXEventService,
    private readonly subscriptionService: ApiXSubscriptionService,
    private readonly messageQueueService: ApiXMessageQueueService,
    private readonly latencyTrackerService: ApiXLatencyTrackerService,
    private readonly auditLoggerService: ApiXAuditLoggerService,
  ) { }

  afterInit(server: Server) {
    this.logger.log('APIX Gateway initialized');

    // Start heartbeat monitoring
    setInterval(() => {
      this.checkHeartbeats();
    }, 30000); // Check every 30 seconds
  }

  async handleConnection(client: Socket) {
    try {
      this.logger.log(`Client attempting connection: ${client.id}`);

      // Extract token from handshake auth
      const token = client.handshake.auth?.token;
      if (!token) {
        this.logger.warn(`No token provided for client: ${client.id}`);
        client.disconnect(true);
        return;
      }

      // Verify JWT token
      const payload = this.jwtService.verify(token);
      const sessionId = client.id;
      const organizationId = payload.organizationId;
      const userId = payload.sub;

      // Create connection record
      const connection: ApiXConnection = {
        sessionId,
        organizationId,
        userId,
        clientType: client.handshake.auth?.clientType || 'WEB_APP',
        channels: [],
        metadata: {
          userAgent: client.handshake.headers['user-agent'],
          ip: client.handshake.address,
          timestamp: Date.now(),
        },
        connectedAt: new Date(),
        lastHeartbeat: new Date(),
        status: 'CONNECTED',
      };

      // Store connection
      this.connections.set(sessionId, connection);
      await this.connectionService.createConnection(connection);

      // Send connection established event
      client.emit('connection.established', {
        sessionId,
        timestamp: Date.now(),
      });

      this.logger.log(`Client connected: ${client.id} (${organizationId})`);
      this.auditLoggerService.logConnection(connection, 'CONNECTED');

    } catch (error) {
      this.logger.error(`Connection failed for client ${client.id}:`, error);
      client.emit('connection.error', {
        error: 'Authentication failed',
        timestamp: Date.now(),
      });
      client.disconnect(true);
    }
  }

  async handleDisconnect(client: Socket) {
    const sessionId = client.id;
    const connection = this.connections.get(sessionId);

    if (connection) {
      // Update connection status
      connection.status = 'DISCONNECTED';
      await this.connectionService.updateConnection(sessionId, { status: 'DISCONNECTED' });

      // Clean up subscriptions
      const subscribedChannels = this.subscriptions.get(sessionId) || new Set();
      for (const channel of subscribedChannels) {
        this.removeSubscriberFromChannel(sessionId, channel);
      }
      this.subscriptions.delete(sessionId);

      // Remove from connections
      this.connections.delete(sessionId);

      this.logger.log(`Client disconnected: ${client.id}`);
      this.auditLoggerService.logConnection(connection, 'DISCONNECTED');
    }
  }

  @SubscribeMessage('connection.authenticate')
  async handleAuthentication(
    @MessageBody() data: { token: string; organizationId?: string; clientType?: string; metadata?: Record<string, any> },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const payload = this.jwtService.verify(data.token);
      const sessionId = client.id;
      const connection = this.connections.get(sessionId);

      if (connection) {
        // Update connection with additional metadata
        connection.metadata = { ...connection.metadata, ...data.metadata };
        await this.connectionService.updateConnection(sessionId, { metadata: connection.metadata });

        client.emit('connection.authenticated', {
          sessionId,
          organizationId: payload.organizationId,
          timestamp: Date.now(),
        });

        this.logger.log(`Client authenticated: ${client.id}`);
      }
    } catch (error) {
      this.logger.error(`Authentication failed for client ${client.id}:`, error);
      client.emit('connection.error', {
        error: 'Authentication failed',
        timestamp: Date.now(),
      });
    }
  }

  @SubscribeMessage('subscription.add')
  async handleSubscriptionAdd(
    @MessageBody() data: { channel: string; eventType?: string; acknowledgment?: boolean },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const sessionId = client.id;
      const connection = this.connections.get(sessionId);

      if (!connection) {
        client.emit('subscription.error', { error: 'Not connected' });
        return;
      }

      // Validate channel permissions
      const hasPermission = await this.subscriptionService.validateChannelPermission(
        data.channel,
        connection.organizationId,
        connection.userId,
      );

      if (!hasPermission) {
        client.emit('subscription.error', { error: 'Insufficient permissions' });
        return;
      }

      // Add subscription
      await this.addSubscriberToChannel(sessionId, data.channel);

      // Update connection channels
      if (!connection.channels.includes(data.channel)) {
        connection.channels.push(data.channel);
        await this.connectionService.updateConnection(sessionId, { channels: connection.channels });
      }

      client.emit('subscription.added', {
        channel: data.channel,
        timestamp: Date.now(),
      });

      this.logger.log(`Subscription added: ${sessionId} -> ${data.channel}`);
      this.auditLoggerService.logSubscription(sessionId, data.channel, 'ADDED');

    } catch (error) {
      this.logger.error(`Subscription add failed:`, error);
      client.emit('subscription.error', { error: 'Subscription failed' });
    }
  }

  @SubscribeMessage('subscription.remove')
  async handleSubscriptionRemove(
    @MessageBody() data: { channel: string },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const sessionId = client.id;
      const connection = this.connections.get(sessionId);

      if (!connection) {
        client.emit('subscription.error', { error: 'Not connected' });
        return;
      }

      // Remove subscription
      this.removeSubscriberFromChannel(sessionId, data.channel);

      // Update connection channels
      connection.channels = connection.channels.filter(ch => ch !== data.channel);
      await this.connectionService.updateConnection(sessionId, { channels: connection.channels });

      client.emit('subscription.removed', {
        channel: data.channel,
        timestamp: Date.now(),
      });

      this.logger.log(`Subscription removed: ${sessionId} -> ${data.channel}`);
      this.auditLoggerService.logSubscription(sessionId, data.channel, 'REMOVED');

    } catch (error) {
      this.logger.error(`Subscription remove failed:`, error);
      client.emit('subscription.error', { error: 'Unsubscription failed' });
    }
  }

  @SubscribeMessage('heartbeat.ping')
  async handleHeartbeatPing(
    @MessageBody() data: { timestamp: number },
    @ConnectedSocket() client: Socket,
  ) {
    const sessionId = client.id;
    const connection = this.connections.get(sessionId);

    if (connection) {
      // Update last heartbeat
      connection.lastHeartbeat = new Date();
      await this.connectionService.updateConnection(sessionId, { lastHeartbeat: connection.lastHeartbeat });

      // Send pong response
      client.emit('heartbeat.pong', {
        timestamp: Date.now(),
        sessionId,
      });
    }
  }

  @SubscribeMessage('message.send')
  async handleMessageSend(
    @MessageBody() data: ApiXEvent,
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const sessionId = client.id;
      const connection = this.connections.get(sessionId);

      if (!connection) {
        client.emit('message.error', { error: 'Not connected' });
        return;
      }

      // Validate event
      const isValidEvent = await this.eventService.validateEvent(data, connection);
      if (!isValidEvent) {
        client.emit('message.error', { error: 'Invalid event' });
        return;
      }

      // Store event
      await this.eventService.storeEvent({
        ...data,
        sessionId,
        createdAt: new Date(),
      });

      // Route event to subscribers
      await this.routeEventToSubscribers(data);

      // Send acknowledgment
      client.emit('message.sent', {
        eventId: data.metadata?.correlation_id,
        timestamp: Date.now(),
      });

      this.logger.log(`Message sent: ${data.type} -> ${data.channel}`);
      this.auditLoggerService.logEvent(sessionId, data, 'SENT');

    } catch (error) {
      this.logger.error(`Message send failed:`, error);
      client.emit('message.error', { error: 'Message failed' });
    }
  }

  // Public method to broadcast events (used by other services)
  async broadcastEvent(event: ApiXEvent) {
    try {
      // Store event
      await this.eventService.storeEvent({
        ...event,
        createdAt: new Date(),
      });

      // Route to subscribers
      await this.routeEventToSubscribers(event);

      this.logger.log(`Event broadcasted: ${event.type} -> ${event.channel}`);
      this.auditLoggerService.logEvent('SYSTEM', event, 'BROADCAST');

    } catch (error) {
      this.logger.error(`Event broadcast failed:`, error);
    }
  }

  // Private helper methods
  private async addSubscriberToChannel(sessionId: string, channel: string) {
    // Add to session subscriptions
    if (!this.subscriptions.has(sessionId)) {
      this.subscriptions.set(sessionId, new Set());
    }
    this.subscriptions.get(sessionId)!.add(channel);

    // Add to channel subscribers
    if (!this.channelSubscribers.has(channel)) {
      this.channelSubscribers.set(channel, new Set());
    }
    this.channelSubscribers.get(channel)!.add(sessionId);

    // Store in Redis for persistence
    await this.redisService.set(`apix:channel:${channel}:subscribers:${sessionId}`, '1');
  }

  private async removeSubscriberFromChannel(sessionId: string, channel: string) {
    // Remove from session subscriptions
    const sessionSubs = this.subscriptions.get(sessionId);
    if (sessionSubs) {
      sessionSubs.delete(channel);
      if (sessionSubs.size === 0) {
        this.subscriptions.delete(sessionId);
      }
    }

    // Remove from channel subscribers
    const channelSubs = this.channelSubscribers.get(channel);
    if (channelSubs) {
      channelSubs.delete(sessionId);
      if (channelSubs.size === 0) {
        this.channelSubscribers.delete(channel);
      }
    }

    // Remove from Redis
    await this.redisService.del(`apix:channel:${channel}:subscribers:${sessionId}`);
  }

  private async routeEventToSubscribers(event: ApiXEvent) {
    const channel = event.channel;
    const subscribers = this.channelSubscribers.get(channel);

    if (!subscribers || subscribers.size === 0) {
      return;
    }

    const connectedSubscribers = Array.from(subscribers).filter(sessionId => {
      const connection = this.connections.get(sessionId);
      return connection && connection.status === 'CONNECTED';
    });

    // Send to connected subscribers
    for (const sessionId of connectedSubscribers) {
      const socket = this.server.sockets.sockets.get(sessionId);
      if (socket) {
        socket.emit(event.type, {
          ...event,
          timestamp: Date.now(),
        });
      }
    }

    // Queue for offline subscribers
    const offlineSubscribers = Array.from(subscribers).filter(sessionId => {
      const connection = this.connections.get(sessionId);
      return !connection || connection.status !== 'CONNECTED';
    });

    for (const sessionId of offlineSubscribers) {
      await this.messageQueueService.queueMessage(sessionId, event);
    }

    this.logger.log(`Event routed: ${event.type} -> ${connectedSubscribers.length} connected, ${offlineSubscribers.length} queued`);
  }

  private async checkHeartbeats() {
    const now = new Date();
    const timeout = 60000; // 60 seconds

    for (const [sessionId, connection] of this.connections) {
      const timeSinceHeartbeat = now.getTime() - connection.lastHeartbeat.getTime();

      if (timeSinceHeartbeat > timeout) {
        this.logger.warn(`Client ${sessionId} heartbeat timeout, disconnecting`);

        const socket = this.server.sockets.sockets.get(sessionId);
        if (socket) {
          socket.disconnect(true);
        }
      }
    }
  }

  // Get connection statistics
  getConnectionStats() {
    return {
      totalConnections: this.connections.size,
      totalSubscriptions: this.subscriptions.size,
      totalChannels: this.channelSubscribers.size,
      connectionsByStatus: this.getConnectionsByStatus(),
    };
  }

  private getConnectionsByStatus() {
    const stats = { CONNECTED: 0, DISCONNECTED: 0, RECONNECTING: 0, SUSPENDED: 0 };
    for (const connection of this.connections.values()) {
      stats[connection.status]++;
    }
    return stats;
  }
}