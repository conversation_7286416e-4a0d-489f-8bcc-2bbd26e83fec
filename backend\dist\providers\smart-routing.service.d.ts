import { PrismaService } from '../prisma/prisma.service';
import { LoggerService } from '../common/services/logger.service';
import { ConfigService } from '@nestjs/config';
export interface ProviderMetrics {
    providerId: string;
    averageLatency: number;
    successRate: number;
    costPerToken: number;
    availability: number;
    lastUpdated: Date;
}
export interface RoutingRequest {
    organizationId: string;
    modelType?: string;
    maxLatency?: number;
    maxCost?: number;
    requiresStreaming?: boolean;
    priority: 'cost' | 'speed' | 'quality' | 'balanced';
}
export interface RoutingResult {
    providerId: string;
    modelId: string;
    confidence: number;
    reasoning: string;
    fallbacks: Array<{
        providerId: string;
        modelId: string;
        confidence: number;
    }>;
}
export declare class SmartRoutingService {
    private prisma;
    private loggerService;
    private configService;
    private readonly logger;
    private metricsCache;
    private cacheExpiry;
    constructor(prisma: PrismaService, loggerService: LoggerService, configService: ConfigService);
    selectOptimalProvider(request: RoutingRequest): Promise<RoutingResult>;
    private getAvailableProviders;
    private getProviderMetrics;
    private calculateProviderMetrics;
    private calculateAvailability;
    private scoreProviders;
    private calculateScore;
    private generateReasoning;
    private updateMetricsCache;
    getProviderMetricsForOrganization(organizationId: string): Promise<ProviderMetrics[]>;
    checkProviderHealth(providerId: string): Promise<{
        healthy: boolean;
        latency: number;
        error?: string;
    }>;
}
