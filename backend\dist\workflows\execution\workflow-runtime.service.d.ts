import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { LoggerService } from '../../common/services/logger.service';
import { AgentRuntimeService } from '../../agents/execution/agent-runtime.service';
import { ToolRuntimeService } from '../../tools/execution/tool-runtime.service';
export interface WorkflowNode {
    id: string;
    type: 'agent' | 'tool' | 'condition' | 'parallel' | 'delay';
    config: Record<string, any>;
    position: {
        x: number;
        y: number;
    };
}
export interface WorkflowEdge {
    id: string;
    source: string;
    target: string;
    condition?: string;
    label?: string;
}
export interface WorkflowDefinition {
    id: string;
    name: string;
    nodes: WorkflowNode[];
    edges: WorkflowEdge[];
    variables: Record<string, any>;
    settings: {
        timeout?: number;
        retryPolicy?: {
            maxRetries: number;
            backoffStrategy: 'linear' | 'exponential';
        };
    };
}
export interface WorkflowExecutionContext {
    workflowId: string;
    input: Record<string, any>;
    variables?: Record<string, any>;
    sessionId?: string;
    userId?: string;
    organizationId?: string;
}
export interface WorkflowExecutionResult {
    success: boolean;
    output: Record<string, any>;
    duration: number;
    steps: Array<{
        nodeId: string;
        type: string;
        success: boolean;
        output: any;
        duration: number;
        error?: string;
    }>;
    error?: string;
}
export declare class WorkflowRuntimeService {
    private prisma;
    private configService;
    private loggerService;
    private agentRuntime;
    private toolRuntime;
    private readonly logger;
    constructor(prisma: PrismaService, configService: ConfigService, loggerService: LoggerService, agentRuntime: AgentRuntimeService, toolRuntime: ToolRuntimeService);
    executeWorkflow(context: WorkflowExecutionContext): Promise<WorkflowExecutionResult>;
    private loadWorkflow;
    private executeWorkflowNodes;
    private findStartNode;
    private executeNode;
    private executeAgentNode;
    private executeToolNode;
    private executeConditionNode;
    private executeParallelNode;
    private executeDelayNode;
    private getNextNodes;
    private resolveVariables;
    private evaluateCondition;
    private createExecutionRecord;
    private updateExecutionRecord;
}
