import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, BaseEntity   } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Organization } from '../../database/entities/organization.entity';

@Entity('tenants')
export class Tenant extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id!: string;

    @Column({ unique: true })
    name!: string;

    @Column({ unique: true })
    domain!: string;

    @Column({ type: 'jsonb', default: {} })
    settings!: Record<string, any>;

    @Column({ default: 'active' })
    status!: 'active' | 'suspended' | 'deleted';

    @CreateDateColumn()
    createdAt!: Date;

    @UpdateDateColumn()
    updatedAt!: Date;

    @OneToMany(() => User, user => user.tenant)
    users!: User[];

    @OneToMany(() => Organization, organization => organization.tenant)
    organizations!: Organization[];
} 