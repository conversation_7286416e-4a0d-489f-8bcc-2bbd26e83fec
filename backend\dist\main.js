"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const swagger_1 = require("@nestjs/swagger");
const helmet_1 = require("helmet");
const compression = require("compression");
const app_module_1 = require("./app.module");
const logger_service_1 = require("./logging/logger.service");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule, {
        logger: ['error', 'warn', 'log', 'debug', 'verbose'],
    });
    const configService = app.get(config_1.ConfigService);
    const logger = app.get(logger_service_1.CustomLoggerService);
    const apiPrefix = configService.get('app.api.prefix');
    const apiVersion = configService.get('app.api.version');
    app.setGlobalPrefix(`${apiPrefix}/${apiVersion}`);
    app.use((0, helmet_1.default)({
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                styleSrc: ["'self'", "'unsafe-inline'"],
                scriptSrc: ["'self'"],
                imgSrc: ["'self'", "data:", "https:"],
            },
        },
        crossOriginEmbedderPolicy: false,
    }));
    app.use(compression());
    const corsOrigins = configService.get('app.security.corsOrigins');
    app.enableCors({
        origin: corsOrigins,
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-Tenant-ID'],
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
            enableImplicitConversion: true,
        },
        errorHttpStatusCode: 422,
    }));
    const swaggerEnabled = configService.get('app.api.swaggerEnabled');
    if (swaggerEnabled) {
        const config = new swagger_1.DocumentBuilder()
            .setTitle('AI Agent Orchestration Platform API')
            .setDescription('Revolutionary AI Agent Orchestration Platform with Multi-Tenant Support')
            .setVersion('1.0.0')
            .addBearerAuth()
            .addTag('auth', 'Authentication endpoints')
            .addTag('users', 'User management endpoints')
            .addTag('agents', 'AI Agent management endpoints')
            .addTag('workflows', 'Workflow orchestration endpoints')
            .addTag('health', 'Health check endpoints')
            .build();
        const document = swagger_1.SwaggerModule.createDocument(app, config);
        swagger_1.SwaggerModule.setup('api/docs', app, document, {
            swaggerOptions: {
                persistAuthorization: true,
            },
        });
    }
    const port = configService.get('app.port') || 3000;
    const environment = configService.get('app.environment');
    await app.listen(port, '0.0.0.0');
    logger.logWithMeta('Application started successfully', {
        port,
        environment,
        apiPrefix: `${apiPrefix}/${apiVersion}`,
        swaggerEnabled,
        corsOrigins,
    }, 'Bootstrap');
    process.on('SIGTERM', async () => {
        logger.log('SIGTERM received, shutting down gracefully', 'Bootstrap');
        await app.close();
        process.exit(0);
    });
    process.on('SIGINT', async () => {
        logger.log('SIGINT received, shutting down gracefully', 'Bootstrap');
        await app.close();
        process.exit(0);
    });
}
bootstrap().catch((error) => {
    console.error('Failed to start application:', error);
    process.exit(1);
});
//# sourceMappingURL=main.js.map