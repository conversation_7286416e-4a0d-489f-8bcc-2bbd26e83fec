import { AIProviderIntegrationService } from './ai-provider-integration.service';
import { SmartRoutingService } from './smart-routing.service';
import { LoggerService } from '../common/services/logger.service';
import { PrismaService } from '../prisma/prisma.service';
export interface UniversalAIRequest {
    prompt: string;
    organizationId: string;
    provider?: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    stop?: string[];
    priority?: 'cost' | 'speed' | 'quality' | 'balanced';
    maxLatency?: number;
    maxCost?: number;
    requiresStreaming?: boolean;
    systemPrompt?: string;
    context?: Array<{
        role: 'user' | 'assistant' | 'system';
        content: string;
    }>;
    tools?: string[];
    userId?: string;
    sessionId?: string;
    tags?: string[];
}
export interface UniversalAIResponse {
    content: string;
    finishReason?: string;
    provider: string;
    model: string;
    usage: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
    cost: number;
    latency: number;
    requestId: string;
    timestamp: Date;
    routing: {
        selected: string;
        confidence: number;
        reasoning: string;
        fallbacksAvailable: number;
    };
}
export interface StreamingResponse {
    requestId: string;
    provider: string;
    model: string;
    stream: AsyncIterable<string>;
}
export declare class UniversalSDKService {
    private aiProvider;
    private smartRouting;
    private loggerService;
    private prisma;
    private readonly logger;
    constructor(aiProvider: AIProviderIntegrationService, smartRouting: SmartRoutingService, loggerService: LoggerService, prisma: PrismaService);
    complete(request: UniversalAIRequest): Promise<UniversalAIResponse>;
    stream(request: UniversalAIRequest): Promise<StreamingResponse>;
    private buildAIRequest;
    private executeWithFallback;
    private logRequest;
    getAvailableModels(organizationId: string): Promise<Array<{
        provider: string;
        models: Array<{
            id: string;
            name: string;
            capabilities: string[];
            costPerToken: number;
        }>;
    }>>;
    getUsageStats(organizationId: string, timeframe?: 'hour' | 'day' | 'week' | 'month'): Promise<{
        requests: any;
        totalCost: any;
        totalTokens: any;
        averageLatency: any;
        timeframe: "hour" | "day" | "week" | "month";
    }>;
}
