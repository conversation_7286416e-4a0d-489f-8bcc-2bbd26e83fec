import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ChangePasswordDto {
    @ApiProperty({
        description: 'Current password',
        example: 'currentPassword123',
    })
    @IsString({ message: 'Current password must be a string' })
    @MinLength(8, { message: 'Current password must be at least 8 characters long' })
    @MaxLength(128, { message: 'Current password must not exceed 128 characters' })
    currentPassword!: string;

    @ApiProperty({
        description: 'New password',
        example: 'newSecurePassword123',
        minLength: 8,
        maxLength: 128,
    })
    @IsString({ message: 'New password must be a string' })
    @MinLength(8, { message: 'New password must be at least 8 characters long' })
    @MaxLength(128, { message: 'New password must not exceed 128 characters' })
    newPassword!: string;
} 