"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SessionMemoryService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionMemoryService = void 0;
const common_1 = require("@nestjs/common");
const ioredis_1 = require("@nestjs-modules/ioredis");
const ioredis_2 = require("ioredis");
let SessionMemoryService = SessionMemoryService_1 = class SessionMemoryService {
    constructor(redis) {
        this.redis = redis;
        this.logger = new common_1.Logger(SessionMemoryService_1.name);
        this.SESSION_PREFIX = 'session:';
        this.DEFAULT_MAX_TOKENS = 4000;
        this.DEFAULT_RETENTION_DAYS = 30;
    }
    async createSession(agentId, organizationId, config) {
        const sessionId = `${organizationId}_${agentId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const session = {
            id: sessionId,
            agentId,
            organizationId,
            messages: [],
            context: {},
            metadata: {
                tokenCount: 0,
                maxTokens: (config === null || config === void 0 ? void 0 : config.maxTokens) || this.DEFAULT_MAX_TOKENS,
                retentionDays: (config === null || config === void 0 ? void 0 : config.retentionDays) || this.DEFAULT_RETENTION_DAYS,
                enableLongTerm: (config === null || config === void 0 ? void 0 : config.enableLongTerm) || false,
            },
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        await this.saveSession(session);
        const expirationSeconds = session.metadata.retentionDays * 24 * 60 * 60;
        await this.redis.expire(`${this.SESSION_PREFIX}${sessionId}`, expirationSeconds);
        this.logger.log(`Session created: ${sessionId} for agent: ${agentId}`);
        return session;
    }
    async getSession(sessionId, organizationId) {
        try {
            const sessionData = await this.redis.get(`${this.SESSION_PREFIX}${sessionId}`);
            if (!sessionData) {
                return null;
            }
            const session = JSON.parse(sessionData);
            if (session.organizationId !== organizationId) {
                throw new Error('Unauthorized access to session');
            }
            return session;
        }
        catch (error) {
            this.logger.error(`Failed to get session ${sessionId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            return null;
        }
    }
    async addMessage(sessionId, message) {
        var _a;
        try {
            const session = await this.getSession(sessionId, (_a = message.metadata) === null || _a === void 0 ? void 0 : _a.organizationId);
            if (!session) {
                throw new Error('Session not found');
            }
            session.messages.push(message);
            session.updatedAt = new Date();
            const messageTokens = this.estimateTokens(message.content);
            session.metadata.tokenCount += messageTokens;
            if (session.metadata.tokenCount > session.metadata.maxTokens) {
                await this.truncateSession(session);
            }
            await this.saveSession(session);
            this.logger.debug(`Message added to session ${sessionId}, total messages: ${session.messages.length}`);
        }
        catch (error) {
            this.logger.error(`Failed to add message to session ${sessionId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            throw error;
        }
    }
    async updateContext(sessionId, organizationId, context) {
        try {
            const session = await this.getSession(sessionId, organizationId);
            if (!session) {
                throw new Error('Session not found');
            }
            session.context = Object.assign(Object.assign({}, session.context), context);
            session.updatedAt = new Date();
            await this.saveSession(session);
        }
        catch (error) {
            this.logger.error(`Failed to update session context ${sessionId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            throw error;
        }
    }
    async getSessionHistory(sessionId, organizationId, limit) {
        try {
            const session = await this.getSession(sessionId, organizationId);
            if (!session) {
                return [];
            }
            const messages = session.messages;
            return limit ? messages.slice(-limit) : messages;
        }
        catch (error) {
            this.logger.error(`Failed to get session history ${sessionId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            return [];
        }
    }
    async deleteSession(sessionId, organizationId) {
        try {
            const session = await this.getSession(sessionId, organizationId);
            if (!session) {
                throw new Error('Session not found');
            }
            await this.redis.del(`${this.SESSION_PREFIX}${sessionId}`);
            this.logger.log(`Session deleted: ${sessionId}`);
        }
        catch (error) {
            this.logger.error(`Failed to delete session ${sessionId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            throw error;
        }
    }
    async getSessionsByAgent(agentId, organizationId) {
        try {
            const pattern = `${this.SESSION_PREFIX}${organizationId}_${agentId}_*`;
            const keys = await this.redis.keys(pattern);
            const sessions = [];
            for (const key of keys) {
                const sessionData = await this.redis.get(key);
                if (sessionData) {
                    sessions.push(JSON.parse(sessionData));
                }
            }
            return sessions.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
        }
        catch (error) {
            this.logger.error(`Failed to get sessions for agent ${agentId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            return [];
        }
    }
    async saveSession(session) {
        await this.redis.set(`${this.SESSION_PREFIX}${session.id}`, JSON.stringify(session));
    }
    async truncateSession(session) {
        const systemMessages = session.messages.filter(m => m.role === 'system');
        const recentMessages = session.messages.filter(m => m.role !== 'system').slice(-10);
        session.messages = [...systemMessages, ...recentMessages];
        session.metadata.tokenCount = session.messages.reduce((total, message) => total + this.estimateTokens(message.content), 0);
        this.logger.debug(`Session ${session.id} truncated to ${session.messages.length} messages`);
    }
    estimateTokens(text) {
        return Math.ceil(text.length / 4);
    }
    async getSessionStats(organizationId) {
        try {
            const pattern = `${this.SESSION_PREFIX}${organizationId}_*`;
            const keys = await this.redis.keys(pattern);
            let totalMessages = 0;
            let activeSessions = 0;
            const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
            for (const key of keys) {
                const sessionData = await this.redis.get(key);
                if (sessionData) {
                    const session = JSON.parse(sessionData);
                    totalMessages += session.messages.length;
                    if (new Date(session.updatedAt) > oneDayAgo) {
                        activeSessions++;
                    }
                }
            }
            return {
                totalSessions: keys.length,
                activeSessions,
                totalMessages,
                averageSessionLength: keys.length > 0 ? totalMessages / keys.length : 0,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get session stats for organization ${organizationId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            return {
                totalSessions: 0,
                activeSessions: 0,
                totalMessages: 0,
                averageSessionLength: 0,
            };
        }
    }
};
exports.SessionMemoryService = SessionMemoryService;
exports.SessionMemoryService = SessionMemoryService = SessionMemoryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, ioredis_1.InjectRedis)()),
    __metadata("design:paramtypes", [ioredis_2.default])
], SessionMemoryService);
//# sourceMappingURL=session-memory.service.js.map