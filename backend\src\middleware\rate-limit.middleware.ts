import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { CustomLoggerService } from '../logging/logger.service';

interface RequestWithUser extends Request {
    user?: {
        userId: string;
        organizationId: string;
    };
}

@Injectable()
export class RateLimitMiddleware implements NestMiddleware {
    private readonly rateLimitStore = new Map<string, { count: number; resetTime: number }>();

    constructor(private readonly logger: CustomLoggerService) { }

    use(req: RequestWithUser, res: Response, next: NextFunction) {
        try {
            const clientId = this.getClientId(req);
            const rateLimit = this.getRateLimit(req);

            if (!clientId || !rateLimit) {
                return next();
            }

            const now = Date.now();
            const key = `${clientId}:${req.method}:${req.path}`;
            const current = this.rateLimitStore.get(key);

            if (!current || now > current.resetTime) {
                // Reset or create new rate limit entry
                this.rateLimitStore.set(key, {
                    count: 1,
                    resetTime: now + rateLimit.windowMs,
                });
            } else if (current.count >= rateLimit.max) {
                // Rate limit exceeded
                this.logger.warn(`Rate limit exceeded for ${clientId}`, 'RateLimitMiddleware');
                return res.status(429).json({
                    error: 'Too Many Requests',
                    message: 'Rate limit exceeded. Please try again later.',
                    retryAfter: Math.ceil((current.resetTime - now) / 1000),
                });
            } else {
                // Increment count
                current.count++;
            }

            // Add rate limit headers
            const currentEntry = this.rateLimitStore.get(key);
            res.setHeader('X-RateLimit-Limit', rateLimit.max);
            res.setHeader('X-RateLimit-Remaining', Math.max(0, rateLimit.max - (currentEntry?.count || 1)));
            res.setHeader('X-RateLimit-Reset', Math.ceil((currentEntry?.resetTime || now + rateLimit.windowMs) / 1000));

            next();
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error(`Rate limit middleware error: ${errorMessage}`, 'RateLimitMiddleware');
            next();
        }
    }

    private getClientId(req: RequestWithUser): string | null {
        // Check for authenticated user first
        if (req.user?.userId) {
            return req.user.userId;
        }

        // Fallback to IP address
        const forwarded = req.headers['x-forwarded-for'] as string;
        const ip = forwarded ? forwarded.split(',')[0] : req.ip || req.connection.remoteAddress;
        return ip || 'unknown';
    }

    private getRateLimit(req: Request): { max: number; windowMs: number } | null {
        const path = req.path;
        const method = req.method;

        // Auth endpoints - stricter limits
        if (path.startsWith('/api/auth')) {
            return { max: 5, windowMs: 60000 }; // 5 requests per minute
        }

        // API endpoints - moderate limits
        if (path.startsWith('/api/')) {
            return { max: 100, windowMs: 60000 }; // 100 requests per minute
        }

        // Health check - more lenient
        if (path.startsWith('/health')) {
            return { max: 30, windowMs: 60000 }; // 30 requests per minute
        }

        // Default - no rate limiting
        return null;
    }
} 