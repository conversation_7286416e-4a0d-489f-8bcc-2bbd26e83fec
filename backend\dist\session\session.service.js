"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const ioredis_1 = require("ioredis");
const session_entity_1 = require("../database/entities/session.entity");
const apix_gateway_1 = require("../websocket/apix.gateway");
let SessionService = class SessionService {
    constructor(prisma, apixGateway) {
        this.prisma = prisma;
        this.apixGateway = apixGateway;
        this.redis = new ioredis_1.default({
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT || '6379'),
            password: process.env.REDIS_PASSWORD,
        });
    }
    async createSession(createSessionDto) {
        const { userId, organizationId, metadata = {} } = createSessionDto;
        const session = await this.prisma.session.create({
            data: {
                userId,
                organizationId,
                status: session_entity_1.SessionStatus.ACTIVE,
                messages: [],
                context: {},
                metadata,
                expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
            },
        });
        await this.redis.setex(`session:${session.id}`, 24 * 60 * 60, JSON.stringify({
            id: session.id,
            userId: session.userId,
            organizationId: session.organizationId,
            status: session.status,
            messages: session.messages,
            context: session.context,
            metadata: session.metadata,
        }));
        await this.apixGateway.publishSessionEvent('created', { sessionId: session.id }, organizationId, userId);
        return session;
    }
    async getSession(sessionId, organizationId) {
        const cachedSession = await this.redis.get(`session:${sessionId}`);
        if (cachedSession) {
            const sessionData = JSON.parse(cachedSession);
            if (sessionData.organizationId !== organizationId) {
                throw new common_1.NotFoundException('Session not found');
            }
            return sessionData;
        }
        const session = await this.prisma.session.findUnique({
            where: {
                id: sessionId,
                organizationId,
            },
        });
        if (!session) {
            throw new common_1.NotFoundException('Session not found');
        }
        await this.redis.setex(`session:${sessionId}`, 24 * 60 * 60, JSON.stringify(session));
        return session;
    }
    async addMessage(addMessageDto, organizationId) {
        const { sessionId, role, content, metadata = {} } = addMessageDto;
        const session = await this.getSession(sessionId, organizationId);
        if (session.status !== session_entity_1.SessionStatus.ACTIVE) {
            throw new Error('Cannot add message to inactive session');
        }
        const message = {
            id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            role,
            content,
            timestamp: new Date(),
            metadata,
        };
        const messages = Array.isArray(session.messages) ? session.messages : [];
        messages.push(message);
        const limitedMessages = await this.applyMemoryLimits(messages, session.metadata);
        await this.prisma.session.update({
            where: {
                id: sessionId,
                organizationId,
            },
            data: {
                messages: limitedMessages,
                updatedAt: new Date(),
            },
        });
        const updatedSession = Object.assign(Object.assign({}, session), { messages: limitedMessages });
        await this.redis.setex(`session:${sessionId}`, 24 * 60 * 60, JSON.stringify(updatedSession));
        await this.apixGateway.publishSessionEvent('message_added', { sessionId, message }, organizationId, session.userId);
        return updatedSession;
    }
    async updateContext(updateContextDto, organizationId) {
        const { sessionId, context } = updateContextDto;
        const session = await this.getSession(sessionId, organizationId);
        const mergedContext = Object.assign(Object.assign({}, session.context), context);
        await this.prisma.session.update({
            where: {
                id: sessionId,
                organizationId,
            },
            data: {
                context: mergedContext,
                updatedAt: new Date(),
            },
        });
        const updatedSession = Object.assign(Object.assign({}, session), { context: mergedContext });
        await this.redis.setex(`session:${sessionId}`, 24 * 60 * 60, JSON.stringify(updatedSession));
        await this.apixGateway.publishSessionEvent('context_updated', { sessionId, context }, organizationId, session.userId);
        return updatedSession;
    }
    async getUserSessions(userId, organizationId, limit = 20) {
        return this.prisma.session.findMany({
            where: {
                userId,
                organizationId,
            },
            orderBy: { updatedAt: 'desc' },
            take: limit,
        });
    }
    async completeSession(sessionId, organizationId) {
        const session = await this.getSession(sessionId, organizationId);
        await this.prisma.session.update({
            where: {
                id: sessionId,
                organizationId,
            },
            data: {
                status: session_entity_1.SessionStatus.COMPLETED,
                updatedAt: new Date(),
            },
        });
        await this.redis.del(`session:${sessionId}`);
        await this.apixGateway.publishSessionEvent('completed', { sessionId }, organizationId, session.userId);
        return Object.assign(Object.assign({}, session), { status: session_entity_1.SessionStatus.COMPLETED });
    }
    async shareSessionContext(sessionId, organizationId, targetModule) {
        const session = await this.getSession(sessionId, organizationId);
        const sharedContext = {
            sessionId: session.id,
            userId: session.userId,
            organizationId: session.organizationId,
            context: session.context,
            recentMessages: Array.isArray(session.messages)
                ? session.messages.slice(-10)
                : [],
            metadata: session.metadata,
        };
        await this.redis.setex(`shared_context:${targetModule}:${sessionId}`, 60 * 60, JSON.stringify(sharedContext));
        return sharedContext;
    }
    async getSharedContext(sessionId, module, organizationId) {
        const sharedContext = await this.redis.get(`shared_context:${module}:${sessionId}`);
        if (!sharedContext)
            return null;
        const context = JSON.parse(sharedContext);
        if (context.organizationId !== organizationId) {
            return null;
        }
        return context;
    }
    async applyMemoryLimits(messages, metadata = {}) {
        const maxMessages = metadata.maxMessages || 100;
        if (messages.length > maxMessages) {
            const systemMessages = messages.filter(msg => msg.role === 'system');
            const recentMessages = messages
                .filter(msg => msg.role !== 'system')
                .slice(-(maxMessages - systemMessages.length));
            return [...systemMessages, ...recentMessages];
        }
        return messages;
    }
    async cleanupExpiredSessions() {
        const expiredSessions = await this.prisma.session.findMany({
            where: {
                expiresAt: {
                    lt: new Date(),
                },
                status: session_entity_1.SessionStatus.ACTIVE,
            },
        });
        for (const session of expiredSessions) {
            await this.prisma.session.update({
                where: { id: session.id },
                data: { status: session_entity_1.SessionStatus.TIMEOUT },
            });
            await this.redis.del(`session:${session.id}`);
            await this.apixGateway.publishSessionEvent('expired', { sessionId: session.id }, session.organizationId, session.userId);
        }
    }
};
exports.SessionService = SessionService;
exports.SessionService = SessionService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        apix_gateway_1.ApixGateway])
], SessionService);
//# sourceMappingURL=session.service.js.map