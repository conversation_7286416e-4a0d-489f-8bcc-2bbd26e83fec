import { Repository } from 'typeorm';
import { AgentCollaboration, Agent } from '../database/entities/agent.entity';
import { AgentExecution } from '../database/entities/agent-execution.entity';
import { SessionMemoryService } from './session-memory.service';
import { ApixGateway } from '../websocket/apix.gateway';
import { AIProviderIntegrationService } from '../providers/ai-provider-integration.service';
export interface CollaborationWorkflow {
    id: string;
    name: string;
    steps: CollaborationStep[];
    conditions: CollaborationCondition[];
    settings: {
        maxIterations: number;
        timeoutMs: number;
        failureStrategy: 'stop' | 'continue' | 'retry';
        parallelExecution: boolean;
    };
}
export interface CollaborationStep {
    id: string;
    agentId: string;
    type: 'execute' | 'review' | 'synthesize' | 'validate';
    input: {
        source: 'user' | 'previous' | 'context' | 'shared';
        transformation?: string;
    };
    output: {
        target: 'next' | 'shared' | 'final';
        key?: string;
    };
    conditions?: {
        skipIf?: string;
        retryIf?: string;
        maxRetries?: number;
    };
}
export interface CollaborationCondition {
    id: string;
    type: 'if' | 'while' | 'until';
    condition: string;
    actions: {
        true: string[];
        false: string[];
    };
}
export declare class AgentCollaborationService {
    private collaborationRepository;
    private agentRepository;
    private executionRepository;
    private sessionMemoryService;
    private apixGateway;
    private aiProviderIntegration;
    private readonly logger;
    constructor(collaborationRepository: Repository<AgentCollaboration>, agentRepository: Repository<Agent>, executionRepository: Repository<AgentExecution>, sessionMemoryService: SessionMemoryService, apixGateway: ApixGateway, aiProviderIntegration: AIProviderIntegrationService);
    createCollaboration(name: string, workflow: CollaborationWorkflow, organizationId: string, userId: string): Promise<AgentCollaboration>;
    executeCollaboration(collaborationId: string, input: string, organizationId: string, userId: string, options?: {
        sessionId?: string;
        context?: Record<string, any>;
        streaming?: boolean;
    }): Promise<{
        collaborationId: string;
        sessionId: string;
        results: Array<{
            stepId: string;
            agentId: string;
            output: string;
            executionId: string;
            metadata: Record<string, any>;
        }>;
        finalOutput: string;
        sharedContext: Record<string, any>;
    }>;
    private executeWorkflow;
    private executeStep;
    private buildStepSystemPrompt;
    private prepareStepInput;
    private updateContextWithResult;
    private shouldSkipStep;
    private isWorkflowComplete;
    private evaluateCondition;
    private determineFinalOutput;
    private validateWorkflow;
    private extractAgentIds;
    getCollaborationsByOrganization(organizationId: string): Promise<AgentCollaboration[]>;
    getCollaborationById(collaborationId: string, organizationId: string): Promise<AgentCollaboration>;
    updateCollaborationStatus(collaborationId: string, status: string, organizationId: string): Promise<AgentCollaboration>;
}
