import { RedisService } from '../cache/redis.service';
import { AIProviderManagerService } from './ai-provider-manager.service';
import { ApixGateway } from '../websocket/apix.gateway';
export interface ProviderScore {
    providerId: string;
    score: number;
    latency: number;
    cost: number;
    reliability: number;
    capabilities: string[];
}
export interface ProviderSelectionRequest {
    organizationId: string;
    capabilities?: string[];
    maxCost?: number;
    maxLatency?: number;
    preferredProviders?: string[];
    excludeProviders?: string[];
    maxTokens?: number;
    temperature?: number;
    maxRetries?: number;
    timeout?: number;
    apiKey?: string;
    baseUrl?: string;
    customHeaders?: Record<string, string>;
    model?: string;
    modelId?: string;
    modelVersion?: string;
    modelContextLength?: number;
    modelMaxTokens?: number;
}
export declare class AIProviderSelectorService {
    private providerManager;
    private readonly redisService;
    private apixGateway;
    private readonly logger;
    private readonly PERFORMANCE_CACHE_TTL;
    private readonly HEALTH_CHECK_INTERVAL;
    constructor(providerManager: AIProviderManagerService, redisService: RedisService, apixGateway: ApixGateway);
    selectOptimalProvider(request: ProviderSelectionRequest): Promise<{
        providerId: string;
        modelId: string;
        fallbackChain: string[];
        reasoning: string;
    }>;
    private scoreProvider;
    private calculateLatencyScore;
    private calculateCostScore;
    private calculateCapabilityScore;
    private hasRequiredCapabilities;
    private getProviderCapabilities;
    private selectBestModel;
    private modelSupportsCapabilities;
    private compareModels;
    private getModelScore;
    private applyPreferences;
    private generateSelectionReasoning;
    private getProviderPerformance;
    updateProviderPerformance(providerId: string, metrics: {
        latency: number;
        cost: number;
        success: boolean;
    }): Promise<void>;
    private performHealthChecks;
    getProviderRankings(request: ProviderSelectionRequest): Promise<ProviderScore[]>;
}
