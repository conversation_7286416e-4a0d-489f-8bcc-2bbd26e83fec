"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var GlobalExceptionFilter_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const library_1 = require("@prisma/client/runtime/library");
let GlobalExceptionFilter = GlobalExceptionFilter_1 = class GlobalExceptionFilter {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(GlobalExceptionFilter_1.name);
    }
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        const errorResponse = this.buildErrorResponse(exception, request);
        this.logError(exception, request, errorResponse);
        response.status(errorResponse.statusCode).json(errorResponse);
    }
    buildErrorResponse(exception, request) {
        const timestamp = new Date().toISOString();
        const path = request.url;
        const method = request.method;
        const requestId = request.headers['x-request-id'];
        let statusCode = common_1.HttpStatus.INTERNAL_SERVER_ERROR;
        let message = 'Internal server error';
        let error = 'Internal Server Error';
        let details = undefined;
        if (exception instanceof common_1.HttpException) {
            statusCode = exception.getStatus();
            const exceptionResponse = exception.getResponse();
            if (typeof exceptionResponse === 'string') {
                message = exceptionResponse;
                error = exception.name;
            }
            else if (typeof exceptionResponse === 'object') {
                message = exceptionResponse.message || exception.message;
                error = exceptionResponse.error || exception.name;
                details = exceptionResponse.details;
            }
        }
        else if (exception instanceof library_1.PrismaClientKnownRequestError) {
            statusCode = this.getPrismaErrorStatus(exception.code);
            message = this.getPrismaErrorMessage(exception);
            error = 'Database Error';
            if (this.configService.get('app.server.nodeEnv') !== 'production') {
                details = {
                    code: exception.code,
                    meta: exception.meta,
                };
            }
        }
        else if (exception instanceof Error) {
            message = exception.message;
            error = exception.name;
            if (this.configService.get('app.server.nodeEnv') === 'development') {
                details = { stack: exception.stack };
            }
        }
        return Object.assign(Object.assign({ statusCode,
            timestamp,
            path,
            method,
            message,
            error }, (details && { details })), (requestId && { requestId }));
    }
    logError(exception, request, errorResponse) {
        const { statusCode, message, path, method } = errorResponse;
        const userAgent = request.get('User-Agent') || '';
        const ip = request.ip;
        const logMessage = `${method} ${path} ${statusCode} - ${userAgent} ${ip}`;
        if (statusCode >= 500) {
            this.logger.error(logMessage, exception instanceof Error ? exception.stack : exception);
        }
        else if (statusCode >= 400) {
            this.logger.warn(logMessage);
        }
        if (this.configService.get('app.server.nodeEnv') === 'development') {
            this.logger.debug('Request details:', {
                headers: request.headers,
                body: request.body,
                params: request.params,
                query: request.query,
            });
        }
    }
    getPrismaErrorStatus(code) {
        switch (code) {
            case 'P2002':
                return common_1.HttpStatus.CONFLICT;
            case 'P2025':
                return common_1.HttpStatus.NOT_FOUND;
            case 'P2003':
                return common_1.HttpStatus.BAD_REQUEST;
            case 'P2004':
                return common_1.HttpStatus.BAD_REQUEST;
            default:
                return common_1.HttpStatus.INTERNAL_SERVER_ERROR;
        }
    }
    getPrismaErrorMessage(exception) {
        var _a;
        switch (exception.code) {
            case 'P2002':
                const target = (_a = exception.meta) === null || _a === void 0 ? void 0 : _a.target;
                return `A record with this ${target === null || target === void 0 ? void 0 : target.join(', ')} already exists`;
            case 'P2025':
                return 'Record not found';
            case 'P2003':
                return 'Invalid reference to related record';
            case 'P2004':
                return 'Constraint violation';
            default:
                return 'Database operation failed';
        }
    }
};
exports.GlobalExceptionFilter = GlobalExceptionFilter;
exports.GlobalExceptionFilter = GlobalExceptionFilter = GlobalExceptionFilter_1 = __decorate([
    (0, common_1.Catch)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], GlobalExceptionFilter);
//# sourceMappingURL=global-exception.filter.js.map