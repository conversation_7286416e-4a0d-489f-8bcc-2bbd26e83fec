"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AIProviderManagerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIProviderManagerService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const redis_service_1 = require("../cache/redis.service");
const ai_provider_entity_1 = require("../database/entities/ai-provider.entity");
const apix_gateway_1 = require("../websocket/apix.gateway");
let AIProviderManagerService = AIProviderManagerService_1 = class AIProviderManagerService {
    constructor(providerRepository, modelRepository, usageRepository, redisService, apixGateway) {
        this.providerRepository = providerRepository;
        this.modelRepository = modelRepository;
        this.usageRepository = usageRepository;
        this.redisService = redisService;
        this.apixGateway = apixGateway;
        this.logger = new common_1.Logger(AIProviderManagerService_1.name);
    }
    async createProvider(createProviderDto, organizationId, userId) {
        try {
            await this.validateProviderConfig(createProviderDto.type, createProviderDto.config);
            const provider = this.providerRepository.create({
                name: createProviderDto.name,
                type: createProviderDto.type.toUpperCase(),
                config: createProviderDto.config,
                isActive: true,
                organizationId,
                createdBy: userId,
            });
            const savedProvider = await this.providerRepository.save(provider);
            if (createProviderDto.models && createProviderDto.models.length > 0) {
                const models = createProviderDto.models.map(modelData => this.modelRepository.create({
                    name: modelData.name,
                    providerId: savedProvider.id,
                    capabilities: {
                        chat: modelData.capabilities.includes('chat'),
                        completion: modelData.capabilities.includes('completion'),
                        embedding: modelData.capabilities.includes('embedding'),
                        vision: modelData.capabilities.includes('vision'),
                        functionCalling: modelData.capabilities.includes('functionCalling'),
                        codeGeneration: modelData.capabilities.includes('codeGeneration'),
                        analysis: modelData.capabilities.includes('analysis'),
                        multimodal: modelData.capabilities.includes('multimodal'),
                        streaming: modelData.capabilities.includes('streaming'),
                    },
                    contextLength: modelData.contextLength,
                    maxTokens: modelData.maxTokens,
                }));
                await this.modelRepository.save(models);
            }
            await this.cacheProviderConfig(savedProvider);
            this.apixGateway.emitToOrganization(organizationId, 'provider.created', {
                providerId: savedProvider.id,
                organizationId,
                name: savedProvider.name,
                type: savedProvider.type,
                timestamp: new Date(),
            });
            this.logger.log(`AI Provider created: ${savedProvider.id} for organization: ${organizationId}`);
            return savedProvider;
        }
        catch (error) {
            this.logger.error(`Failed to create AI provider: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async updateProvider(providerId, updateProviderDto, organizationId) {
        try {
            const provider = await this.providerRepository.findOne({
                where: { id: providerId, organizationId },
            });
            if (!provider) {
                throw new Error('Provider not found');
            }
            if (updateProviderDto.config) {
                await this.validateProviderConfig(provider.type, updateProviderDto.config);
            }
            Object.assign(provider, updateProviderDto);
            const updatedProvider = await this.providerRepository.save(provider);
            await this.cacheProviderConfig(updatedProvider);
            this.apixGateway.emitToOrganization(organizationId, 'provider.updated', {
                providerId: updatedProvider.id,
                changes: Object.keys(updateProviderDto),
                timestamp: new Date(),
            });
            return updatedProvider;
        }
        catch (error) {
            this.logger.error(`Failed to update AI provider ${providerId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async deleteProvider(providerId, organizationId) {
        try {
            const result = await this.providerRepository.delete({
                id: providerId,
                organizationId,
            });
            if (result.affected === 0) {
                throw new Error('Provider not found');
            }
            await this.redisService.del(`provider:${providerId}`);
            this.apixGateway.emitToOrganization(organizationId, 'provider.deleted', {
                providerId,
                timestamp: new Date(),
            });
            this.logger.log(`AI Provider deleted: ${providerId}`);
        }
        catch (error) {
            this.logger.error(`Failed to delete AI provider ${providerId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async getProvidersByOrganization(organizationId) {
        return this.providerRepository.find({
            where: { organizationId },
            relations: ['models'],
            order: { createdAt: 'DESC' },
        });
    }
    async getProviderById(providerId, organizationId) {
        const provider = await this.providerRepository.findOne({
            where: { id: providerId, organizationId },
            relations: ['models'],
        });
        if (!provider) {
            throw new Error('Provider not found');
        }
        return provider;
    }
    async getActiveProviders(organizationId) {
        return this.providerRepository.find({
            where: { organizationId, isActive: true },
            relations: ['models'],
            order: { createdAt: 'DESC' },
        });
    }
    async trackUsage(providerId, usage) {
        try {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            let usageRecord = await this.usageRepository.findOne({
                where: { providerId, date: today },
            });
            if (usageRecord) {
                usageRecord.requests += usage.requests;
                usageRecord.tokensUsed += usage.tokensUsed;
                usageRecord.costInCents += usage.costInCents;
            }
            else {
                usageRecord = this.usageRepository.create(Object.assign({ providerId, date: today }, usage));
            }
            await this.usageRepository.save(usageRecord);
            const provider = await this.providerRepository.findOne({ where: { id: providerId } });
            if (provider) {
                this.apixGateway.emitToOrganization(provider.organizationId, 'provider.usage.report', {
                    providerId,
                    date: today,
                    requests: usage.requests,
                    tokensUsed: usage.tokensUsed,
                    costInCents: usage.costInCents,
                    timestamp: new Date(),
                });
            }
        }
        catch (error) {
            this.logger.error(`Failed to track usage for provider ${providerId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
        }
    }
    async getUsageStats(organizationId, startDate, endDate) {
        try {
            const providers = await this.getProvidersByOrganization(organizationId);
            const providerIds = providers.map(p => p.id);
            const query = this.usageRepository.createQueryBuilder('usage')
                .where('usage.providerId IN (:...providerIds)', { providerIds });
            if (startDate) {
                query.andWhere('usage.date >= :startDate', { startDate });
            }
            if (endDate) {
                query.andWhere('usage.date <= :endDate', { endDate });
            }
            const usageRecords = await query.getMany();
            let totalRequests = 0;
            let totalTokens = 0;
            let totalCostInCents = 0;
            const providerBreakdown = providers.map(provider => {
                const providerUsage = usageRecords.filter(u => u.providerId === provider.id);
                const requests = providerUsage.reduce((sum, u) => sum + u.requests, 0);
                const tokens = providerUsage.reduce((sum, u) => sum + u.tokensUsed, 0);
                const costInCents = providerUsage.reduce((sum, u) => sum + u.costInCents, 0);
                totalRequests += requests;
                totalTokens += tokens;
                totalCostInCents += costInCents;
                return {
                    providerId: provider.id,
                    providerName: provider.name,
                    requests,
                    tokens,
                    costInCents,
                };
            });
            return {
                totalRequests,
                totalTokens,
                totalCostInCents,
                providerBreakdown,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get usage stats for organization ${organizationId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async validateProviderConfig(type, config) {
        switch (type) {
            case 'OPENAI':
                if (!config.apiKey) {
                    throw new Error('OpenAI API key is required');
                }
                break;
            case 'CLAUDE':
                if (!config.apiKey) {
                    throw new Error('Claude API key is required');
                }
                break;
            case 'GEMINI':
                if (!config.apiKey) {
                    throw new Error('Gemini API key is required');
                }
                break;
            case 'MISTRAL':
                if (!config.apiKey) {
                    throw new Error('Mistral API key is required');
                }
                break;
            case 'GROQ':
                if (!config.apiKey) {
                    throw new Error('Groq API key is required');
                }
                break;
            default:
                throw new Error(`Unsupported provider type: ${type}`);
        }
    }
    async cacheProviderConfig(provider) {
        const cacheKey = `provider:${provider.id}`;
        const cacheData = {
            id: provider.id,
            name: provider.name,
            type: provider.type,
            config: provider.config,
            isActive: provider.isActive,
            organizationId: provider.organizationId,
        };
        await this.redisService.set(cacheKey, JSON.stringify(cacheData), 3600);
    }
    async getCachedProvider(providerId) {
        const cacheKey = `provider:${providerId}`;
        const cachedData = await this.redisService.get(cacheKey);
        if (cachedData) {
            return JSON.parse(cachedData);
        }
        return null;
    }
};
exports.AIProviderManagerService = AIProviderManagerService;
exports.AIProviderManagerService = AIProviderManagerService = AIProviderManagerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(ai_provider_entity_1.AIProvider)),
    __param(1, (0, typeorm_1.InjectRepository)(ai_provider_entity_1.AIModel)),
    __param(2, (0, typeorm_1.InjectRepository)(ai_provider_entity_1.ProviderUsage)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        redis_service_1.RedisService,
        apix_gateway_1.ApixGateway])
], AIProviderManagerService);
//# sourceMappingURL=ai-provider-manager.service.js.map