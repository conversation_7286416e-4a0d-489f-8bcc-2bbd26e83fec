{"version": 3, "file": "ai-provider-selector.service.js", "sourceRoot": "", "sources": ["../../src/providers/ai-provider-selector.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,0DAAsD;AACtD,+EAAyE;AACzE,4DAAwD;AAiCjD,IAAM,yBAAyB,iCAA/B,MAAM,yBAAyB;IAKpC,YACU,eAAyC,EAChC,YAA0B,EACnC,WAAwB;QAFxB,oBAAe,GAAf,eAAe,CAA0B;QAChC,iBAAY,GAAZ,YAAY,CAAc;QACnC,gBAAW,GAAX,WAAW,CAAa;QAPjB,WAAM,GAAG,IAAI,eAAM,CAAC,2BAAyB,CAAC,IAAI,CAAC,CAAC;QACpD,0BAAqB,GAAG,GAAG,CAAC;QAC5B,0BAAqB,GAAG,KAAK,CAAC;QAQ7C,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,cAAc,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,EAA8B,CAAC,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;IAChJ,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,OAAiC;QAM3D,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAE9F,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;YACnD,CAAC;YAGD,MAAM,gBAAgB,GAAG,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CACzD,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,OAAO,CAAC,YAAY,IAAI,EAAE,CAAC,CACnE,CAAC;YAEF,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;YACpE,CAAC;YAGD,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,GAAG,CACvC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CACxE,CAAC;YAGF,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;YAGlD,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YAE1E,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACnC,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;YAC/D,CAAC;YAED,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,aAAa,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;YAG3E,MAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,gBAAgB,CAAC,UAAU,CAAC,CAAC;YAClF,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC;YAEjF,MAAM,SAAS,GAAG,IAAI,CAAC,0BAA0B,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;YAE7E,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,gBAAgB,CAAC,UAAU,qBAAqB,OAAO,CAAC,cAAc,EAAE,CAAC,CAAC;YAE/G,OAAO;gBACL,UAAU,EAAE,gBAAgB,CAAC,UAAU;gBACvC,OAAO,EAAE,aAAa,CAAC,EAAE;gBACzB,aAAa;gBACb,SAAS;aACV,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YAC9J,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,QAAa,EAAE,OAAiC;QAC1E,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAGvE,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,cAAc,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;QACpG,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,WAAW,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QACxF,MAAM,gBAAgB,GAAG,eAAe,CAAC,WAAW,IAAI,IAAI,CAAC;QAC7D,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,OAAO,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC;QAG5F,MAAM,KAAK,GAAG,CACZ,YAAY,GAAG,IAAI;YACnB,SAAS,GAAG,IAAI;YAChB,gBAAgB,GAAG,GAAG;YACtB,eAAe,GAAG,GAAG,CACtB,CAAC;QAEF,OAAO;YACL,UAAU,EAAE,QAAQ,CAAC,EAAE;YACvB,KAAK;YACL,OAAO,EAAE,eAAe,CAAC,cAAc;YACvC,IAAI,EAAE,eAAe,CAAC,WAAW;YACjC,WAAW,EAAE,gBAAgB;YAC7B,YAAY,EAAE,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC;SACrD,CAAC;IACJ,CAAC;IAEO,qBAAqB,CAAC,OAAe,EAAE,UAAmB;QAChE,IAAI,UAAU,IAAI,OAAO,GAAG,UAAU,EAAE,CAAC;YACvC,OAAO,CAAC,CAAC;QACX,CAAC;QAID,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,kBAAkB,CAAC,IAAY,EAAE,OAAgB;QACvD,IAAI,OAAO,IAAI,IAAI,GAAG,OAAO,EAAE,CAAC;YAC9B,OAAO,CAAC,CAAC;QACX,CAAC;QAID,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;IACxD,CAAC;IAEO,wBAAwB,CAAC,QAAa,EAAE,oBAA8B;QAC5E,MAAM,oBAAoB,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;QACpE,MAAM,mBAAmB,GAAG,oBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAC5D,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,CACnC,CAAC;QAEF,OAAO,mBAAmB,CAAC,MAAM,GAAG,oBAAoB,CAAC,MAAM,CAAC;IAClE,CAAC;IAEO,uBAAuB,CAAC,QAAa,EAAE,oBAA8B;QAC3E,MAAM,oBAAoB,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;QACpE,OAAO,oBAAoB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/E,CAAC;IAEO,uBAAuB,CAAC,QAAa;QAC3C,MAAM,gBAAgB,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAEhD,QAAQ,QAAQ,CAAC,IAAI,EAAE,CAAC;YACtB,KAAK,QAAQ;gBACX,OAAO,CAAC,GAAG,gBAAgB,EAAE,kBAAkB,EAAE,QAAQ,EAAE,iBAAiB,CAAC,CAAC;YAChF,KAAK,QAAQ;gBACX,OAAO,CAAC,GAAG,gBAAgB,EAAE,kBAAkB,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;YACzE,KAAK,QAAQ;gBACX,OAAO,CAAC,GAAG,gBAAgB,EAAE,QAAQ,EAAE,YAAY,EAAE,iBAAiB,CAAC,CAAC;YAC1E,KAAK,SAAS;gBACZ,OAAO,CAAC,GAAG,gBAAgB,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;YACtE,KAAK,MAAM;gBACT,OAAO,CAAC,GAAG,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;YACjD;gBACE,OAAO,gBAAgB,CAAC;QAC5B,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,QAAa,EAAE,YAAsB;QAC3D,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrD,MAAM,IAAI,KAAK,CAAC,oCAAoC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;QACrE,CAAC;QAGD,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAU,EAAE,EAAE,CAC3D,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,YAAY,CAAC,CACpD,CAAC;QAEF,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;QAGD,OAAO,cAAc,CAAC,MAAM,CAAC,CAAC,IAAS,EAAE,OAAY,EAAE,EAAE,CACvD,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CACvD,CAAC;IACJ,CAAC;IAEO,yBAAyB,CAAC,KAAU,EAAE,YAAsB;QAClE,MAAM,iBAAiB,GAAG,KAAK,CAAC,YAAY,IAAI,EAAE,CAAC;QACnD,OAAO,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC;IACpE,CAAC;IAEO,aAAa,CAAC,MAAW,EAAE,MAAW;QAE5C,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAC1C,OAAO,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAEO,aAAa,CAAC,KAAU;QAE9B,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QACrE,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE,OAAO,eAAe,GAAG,YAAY,CAAC;IACxC,CAAC;IAEO,gBAAgB,CACtB,eAAgC,EAChC,OAAiC;QAEjC,IAAI,QAAQ,GAAG,CAAC,GAAG,eAAe,CAAC,CAAC;QAGpC,IAAI,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpE,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,gBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;QACrF,CAAC;QAGD,IAAI,OAAO,CAAC,kBAAkB,IAAI,OAAO,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxE,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,iCACxB,CAAC,KACJ,KAAK,EAAE,OAAO,CAAC,kBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IACnF,CAAC,CAAC;YAGJ,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,0BAA0B,CAAC,QAAuB,EAAE,OAAiC;QAC3F,MAAM,OAAO,GAAG,EAAE,CAAC;QAEnB,IAAI,QAAQ,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC;YACzB,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,QAAQ,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC;YAC3B,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,QAAQ,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,QAAQ,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC;YACzB,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,OAAO,CAAC,kBAAkB,IAAI,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3F,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC;IAC3E,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,UAAkB;QAMrD,MAAM,QAAQ,GAAG,wBAAwB,UAAU,EAAE,CAAC;QACtD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAErD,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC5B,CAAC;QAGD,MAAM,kBAAkB,GAAG;YACzB,cAAc,EAAE,IAAI;YACpB,WAAW,EAAE,IAAI;YACjB,WAAW,EAAE,IAAI;YACjB,WAAW,EAAE,IAAI,IAAI,EAAE;SACxB,CAAC;QAGF,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAEtG,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,yBAAyB,CAC7B,UAAkB,EAClB,OAIC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,wBAAwB,UAAU,EAAE,CAAC;YACtD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;YAG/D,MAAM,KAAK,GAAG,GAAG,CAAC;YAClB,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;YACnF,MAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC;YAC1E,MAAM,cAAc,GAAG,QAAQ,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;YAE9F,MAAM,OAAO,GAAG;gBACd,cAAc,EAAE,UAAU;gBAC1B,WAAW,EAAE,OAAO;gBACpB,WAAW,EAAE,cAAc;gBAC3B,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;YAEF,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAE3F,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4CAA4C,UAAU,EAAE,CAAC,CAAC;QAC9E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QAC5K,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,OAAiC;QACjE,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAE9F,KAAK,MAAM,QAAQ,IAAI,eAAe,EAAE,CAAC;gBACvC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACvE,MAAM,WAAW,GAAG,eAAe,CAAC,WAAW,CAAC;gBAChD,MAAM,QAAQ,GAAG,WAAW,GAAG,IAAI,CAAC;YACtC,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;QAExD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QAC1J,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,OAAiC;QACzD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAE9F,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,GAAG,CACvC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC3D,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,YAAY,EAAE,CAAC,MAAM,CAAC;SACvB,CAAC,CAAC,CACJ,CAAC;QAEF,OAAO,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IAC3D,CAAC;CACF,CAAA;AA7UY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;qCAOgB,sDAAwB;QAClB,4BAAY;QACtB,0BAAW;GARvB,yBAAyB,CA6UrC"}