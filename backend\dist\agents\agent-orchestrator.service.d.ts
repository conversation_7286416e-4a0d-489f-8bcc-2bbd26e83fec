import { Repository } from 'typeorm';
import { Agent, AgentCollaboration, AgentType } from '../database/entities/agent.entity';
import { AgentExecution } from '../database/entities/agent-execution.entity';
import { SessionMemoryService } from './session-memory.service';
import { AgentAnalyticsService } from './agent-analytics.service';
import { ApixGateway } from '../websocket/apix.gateway';
import { AIProviderIntegrationService } from '../providers/ai-provider-integration.service';
import { AIProviderSelectorService } from '../providers/ai-provider-selector.service';
import { CreateAgentInstanceDto, UpdateAgentInstanceDto, ExecuteAgentDto } from './dto/agent.dto';
export interface AgentWizardStep {
    step: number;
    title: string;
    description: string;
    fields: string[];
    isCompleted: boolean;
    validation?: Record<string, any>;
}
export interface AgentWizardData {
    step: number;
    name: string;
    description: string;
    templateId: string;
    type: AgentType;
    primaryProvider: string;
    fallbackProviders: string[];
    config: {
        temperature: number;
        maxTokens: number;
        systemPrompt: string;
        skills: string[];
        capabilities: string[];
        personality: string;
        responseStyle: string;
    };
    memoryConfig: {
        maxTokens: number;
        retentionDays: number;
        enableLongTerm: boolean;
    };
    testMessages: string[];
    isPublic: boolean;
}
export interface AgentTestResult {
    success: boolean;
    output: string;
    metadata: {
        provider: string;
        model: string;
        tokens: {
            input: number;
            output: number;
            total: number;
        };
        duration: number;
        cost: number;
    };
    error?: string;
}
export declare class AgentOrchestratorService {
    private agentRepository;
    private executionRepository;
    private collaborationRepository;
    private sessionMemoryService;
    private analyticsService;
    private apixGateway;
    private aiProviderIntegration;
    private aiProviderSelector;
    private readonly logger;
    constructor(agentRepository: Repository<Agent>, executionRepository: Repository<AgentExecution>, collaborationRepository: Repository<AgentCollaboration>, sessionMemoryService: SessionMemoryService, analyticsService: AgentAnalyticsService, apixGateway: ApixGateway, aiProviderIntegration: AIProviderIntegrationService, aiProviderSelector: AIProviderSelectorService);
    createAgent(createAgentDto: CreateAgentInstanceDto, organizationId: string, userId: string): Promise<Agent>;
    createAgentFromWizard(wizardData: AgentWizardData, organizationId: string, userId: string): Promise<Agent>;
    getAgentWizardSteps(): Promise<AgentWizardStep[]>;
    validateWizardData(wizardData: AgentWizardData): Promise<void>;
    validateAgentConfiguration(createAgentDto: CreateAgentInstanceDto): Promise<void>;
    updateAgent(agentId: string, updateAgentDto: UpdateAgentInstanceDto, organizationId: string): Promise<Agent>;
    deleteAgent(agentId: string, organizationId: string): Promise<void>;
    executeAgent(agentId: string, executeDto: ExecuteAgentDto, organizationId: string): Promise<AgentExecution>;
    testAgent(agentId: string, testMessage: string, organizationId: string): Promise<AgentTestResult>;
    getAgentById(agentId: string, organizationId: string): Promise<Agent>;
    getAgentsByOrganization(organizationId: string): Promise<Agent[]>;
    getAgentExecutions(agentId: string, organizationId: string): Promise<AgentExecution[]>;
    getAgentAnalytics(agentId: string, organizationId: string): Promise<any>;
    duplicateAgent(agentId: string, organizationId: string, userId: string): Promise<Agent>;
    private executeAgentLogic;
    private buildSystemPrompt;
    private updateAgentMetrics;
    createCollaboration(name: string, agentIds: string[], coordinatorId: string, workflow: Record<string, any>, organizationId: string, userId: string): Promise<AgentCollaboration>;
    executeCollaboration(collaborationId: string, input: string, organizationId: string): Promise<{
        collaborationId: string;
        results: Array<{
            agentId: string;
            output: string;
            executionId: string;
        }>;
        finalOutput: string;
    }>;
    private executeCollaborationWorkflow;
    getCollaborationsByOrganization(organizationId: string): Promise<AgentCollaboration[]>;
}
