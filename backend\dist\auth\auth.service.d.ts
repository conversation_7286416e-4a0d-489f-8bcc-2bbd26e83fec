import { JwtService } from '@nestjs/jwt';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { Role } from '../roles/entities/role.entity';
import { Permission } from '../permissions/entities/permission.entity';
import { Tenant } from '../tenants/entities/tenant.entity';
import { RefreshToken } from './entities/refresh-token.entity';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
export declare class AuthService {
    private readonly userRepository;
    private readonly roleRepository;
    private readonly permissionRepository;
    private readonly tenantRepository;
    private readonly refreshTokenRepository;
    private readonly jwtService;
    constructor(userRepository: Repository<User>, roleRepository: Repository<Role>, permissionRepository: Repository<Permission>, tenantRepository: Repository<Tenant>, refreshTokenRepository: Repository<RefreshToken>, jwtService: JwtService);
    validateUser(email: string, password: string): Promise<any>;
    login(loginDto: LoginDto, tenantId?: string): Promise<{
        accessToken: string;
        refreshToken: string;
        user: {
            id: string;
            email: string;
            name: any;
            roles: {
                id: string;
                name: string;
                permissions: {
                    id: string;
                    name: string;
                    resource: string;
                    action: string;
                }[];
            }[];
            tenant: {
                id: string;
                name: string;
                domain: string;
            } | null;
        };
    }>;
    register(registerDto: RegisterDto, tenantId?: string): Promise<{
        accessToken: string;
        refreshToken: string;
        user: {
            id: any;
            email: any;
            name: any;
            roles: any;
            tenant: {
                id: any;
                name: any;
                domain: any;
            } | null;
        };
    }>;
    refreshToken(refreshTokenDto: RefreshTokenDto): Promise<{
        accessToken: string;
        refreshToken: string;
    }>;
    logout(userId: string, refreshToken?: string): Promise<{
        message: string;
    }>;
    changePassword(userId: string, changePasswordDto: ChangePasswordDto): Promise<{
        message: string;
    }>;
    hasPermission(userId: string, resource: string, action: string): Promise<boolean>;
    getUserPermissions(userId: string): Promise<string[]>;
    private generateTokens;
    private hashPassword;
    private verifyPassword;
    private saveRefreshToken;
    private revokeRefreshToken;
}
