import { Modu<PERSON> } from '@nestjs/common';
import { WorkflowRuntimeService } from './workflow-runtime.service';
import { PrismaModule } from '../../prisma/prisma.module';
import { CommonModule } from '../../common/common.module';
import { AgentExecutionModule } from '../../agents/execution/execution.module';
import { ToolExecutionModule } from '../../tools/execution/execution.module';

@Module({
  imports: [
    PrismaModule, 
    CommonModule, 
    AgentExecutionModule, 
    ToolExecutionModule
  ],
  providers: [WorkflowRuntimeService],
  exports: [WorkflowRuntimeService],
})
export class WorkflowExecutionModule {}
