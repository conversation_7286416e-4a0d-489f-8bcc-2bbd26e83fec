import { z } from 'zod';

// API Response Schema
const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  message: z.string().optional()
});

// Provider Types
export enum ProviderType {
  OPENAI = 'OPENAI',
  CLAUDE = 'CLAUDE',
  GEMINI = 'GEMINI',
  MISTRAL = 'MISTRAL',
  GROQ = 'GROQ',
  DEEPSEEK = 'DEEPSEEK',
  HUGGING_FACE = 'HUGGING_FACE',
  LOCAL_AI = 'LOCAL_AI',
  CUSTOM = 'CUSTOM',
  OTHER = 'OTHER'
}

// Provider Schema
const AIProviderSchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  name: z.string(),
  type: z.nativeEnum(ProviderType),
  config: z.record(z.any(), z.any()),
  models: z.array(z.object({
    id: z.string(),
    name: z.string(),
    description: z.string().optional(),
    version: z.string(),
    capabilities: z.record(z.any(), z.any()),
    fineTuned: z.boolean().optional(),
    maxTokens: z.number().optional(),
    costPer1KInput: z.number().optional(),
    costPer1KOutput: z.number().optional()
  })),
  isActive: z.boolean(),
  performanceMetrics: z.object({
    averageLatency: z.number(),
    reliability: z.number(),
    healthStatus: z.enum(['healthy', 'degraded', 'unhealthy']),
    totalRequests: z.number(),
    successRate: z.number(),
    lastChecked: z.date()
  }),
  usageMetrics: z.object({
    totalTokens: z.number(),
    totalCost: z.number(),
    requestsToday: z.number(),
    costToday: z.number()
  }),
  createdAt: z.date(),
  updatedAt: z.date()
});

// Provider Analytics Schema
const ProviderAnalyticsSchema = z.object({
  totalProviders: z.number(),
  activeProviders: z.number(),
  totalRequests: z.number(),
  totalCost: z.number(),
  averageLatency: z.number(),
  overallReliability: z.number(),
  usageByProvider: z.record(z.any(), z.object({
    requests: z.number(),
    tokens: z.number(),
    cost: z.number(),
    latency: z.number()
  })),
  costTrends: z.array(z.object({
    date: z.string(),
    cost: z.number(),
    requests: z.number()
  }))
});

// Request Types
const CreateProviderRequest = z.object({
  name: z.string(),
  type: z.nativeEnum(ProviderType),
  config: z.object({
    apiKey: z.string(),
    baseUrl: z.string().optional(),
    timeout: z.number().optional()
  }),
  isActive: z.boolean().default(true)
});

const TestProviderRequest = z.object({
  type: z.nativeEnum(ProviderType),
  apiKey: z.string(),
  baseUrl: z.string().optional(),
  timeout: z.number().optional()
});

// API Client Class
class AIProviderApiClient {
  private baseUrl: string;
  private getAuthHeaders: () => Record<string, string>;

  constructor(baseUrl: string = '/api/v1', getAuthHeaders?: () => Record<string, string>) {
    this.baseUrl = baseUrl;
    this.getAuthHeaders = getAuthHeaders || (() => ({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    }));
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<{ success: boolean; data?: T; error?: string }> {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      const response = await fetch(url, {
        ...options,
        headers: {
          ...this.getAuthHeaders(),
          ...options.headers
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const validatedData = ApiResponseSchema.parse(data);

      if (!validatedData.success) {
        throw new Error(validatedData.error || 'Request failed');
      }

      return {
        success: true,
        data: validatedData.data as T
      };
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  // Provider Management
  async getProviders(): Promise<{ success: boolean; data?: z.infer<typeof AIProviderSchema>[]; error?: string }> {
    return this.request<z.infer<typeof AIProviderSchema>[]>('/providers');
  }

  async getProvider(id: string): Promise<{ success: boolean; data?: z.infer<typeof AIProviderSchema>; error?: string }> {
    return this.request<z.infer<typeof AIProviderSchema>>(`/providers/${id}`);
  }

  async createProvider(providerData: z.infer<typeof CreateProviderRequest>): Promise<{ success: boolean; data?: z.infer<typeof AIProviderSchema>; error?: string }> {
    return this.request<z.infer<typeof AIProviderSchema>>('/providers', {
      method: 'POST',
      body: JSON.stringify(providerData)
    });
  }

  async updateProvider(id: string, providerData: Partial<z.infer<typeof CreateProviderRequest>>): Promise<{ success: boolean; data?: z.infer<typeof AIProviderSchema>; error?: string }> {
    return this.request<z.infer<typeof AIProviderSchema>>(`/providers/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(providerData)
    });
  }

  async deleteProvider(id: string): Promise<{ success: boolean; error?: string }> {
    return this.request(`/providers/${id}`, {
      method: 'DELETE'
    });
  }

  async toggleProvider(id: string, isActive: boolean): Promise<{ success: boolean; data?: z.infer<typeof AIProviderSchema>; error?: string }> {
    return this.request<z.infer<typeof AIProviderSchema>>(`/providers/${id}/toggle`, {
      method: 'PATCH',
      body: JSON.stringify({ isActive })
    });
  }

  // Provider Testing
  async testProvider(id: string): Promise<{ success: boolean; data?: { latency: number; models: string[] }; error?: string }> {
    return this.request<{ latency: number; models: string[] }>(`/providers/${id}/test`, {
      method: 'POST'
    });
  }

  async testProviderConfig(config: z.infer<typeof TestProviderRequest>): Promise<{ success: boolean; data?: { latency: number; models: string[]; provider: string }; error?: string }> {
    return this.request<{ latency: number; models: string[]; provider: string }>('/providers/test', {
      method: 'POST',
      body: JSON.stringify(config)
    });
  }

  // Analytics
  async getProviderAnalytics(timeRange: string = '7d'): Promise<{ success: boolean; data?: z.infer<typeof ProviderAnalyticsSchema>; error?: string }> {
    return this.request<z.infer<typeof ProviderAnalyticsSchema>>(`/providers/analytics?range=${timeRange}`);
  }

  async getProviderUsage(providerId: string, timeRange: string = '30d'): Promise<{ success: boolean; data?: any; error?: string }> {
    return this.request(`/providers/${providerId}/usage?range=${timeRange}`);
  }

  // Model Management
  async getProviderModels(providerId: string): Promise<{ success: boolean; data?: any[]; error?: string }> {
    return this.request<any[]>(`/providers/${providerId}/models`);
  }

  async addProviderModel(providerId: string, modelData: any): Promise<{ success: boolean; data?: any; error?: string }> {
    return this.request(`/providers/${providerId}/models`, {
      method: 'POST',
      body: JSON.stringify(modelData)
    });
  }

  // Health Check
  async getProviderHealth(providerId: string): Promise<{ success: boolean; data?: { status: string; latency: number; lastChecked: string }; error?: string }> {
    return this.request<{ status: string; latency: number; lastChecked: string }>(`/providers/${providerId}/health`);
  }

  // Smart Routing
  async getOptimalProvider(request: {
    capabilities: string[];
    maxLatency?: number;
    maxCost?: number;
    preferredProviders?: string[];
    excludeProviders?: string[];
  }): Promise<{ success: boolean; data?: { providerId: string; modelId: string; score: number }; error?: string }> {
    return this.request<{ providerId: string; modelId: string; score: number }>('/providers/select', {
      method: 'POST',
      body: JSON.stringify(request)
    });
  }

  // Real-time Request Tracking
  async sendProviderRequest(providerId: string, modelId: string, requestData: {
    messages?: any[];
    prompt?: string;
    maxTokens?: number;
    temperature?: number;
    stream?: boolean;
  }): Promise<{ success: boolean; data?: { requestId: string; response?: any }; error?: string }> {
    return this.request<{ requestId: string; response?: any }>(`/providers/${providerId}/models/${modelId}/request`, {
      method: 'POST',
      body: JSON.stringify(requestData)
    });
  }

  // Streaming Support
  async streamProviderRequest(providerId: string, modelId: string, requestData: {
    messages?: any[];
    prompt?: string;
    maxTokens?: number;
    temperature?: number;
  }): Promise<ReadableStream<Uint8Array> | null> {
    try {
      const response = await fetch(`${this.baseUrl}/providers/${providerId}/models/${modelId}/stream`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response.body;
    } catch (error) {
      console.error('Streaming request failed:', error);
      return null;
    }
  }
}

// Export singleton instance
export const aiProviderApiClient = new AIProviderApiClient();

// Export types
export type AIProvider = z.infer<typeof AIProviderSchema>;
export type ProviderAnalytics = z.infer<typeof ProviderAnalyticsSchema>;
export type CreateProviderRequest = z.infer<typeof CreateProviderRequest>;
export type TestProviderRequest = z.infer<typeof TestProviderRequest>;