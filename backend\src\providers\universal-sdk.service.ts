import { Injectable, Logger } from '@nestjs/common';
import { AIProviderIntegrationService } from './ai-provider-integration.service';
import { SmartRoutingService, RoutingRequest } from './smart-routing.service';
import { LoggerService } from '../common/services/logger.service';
import { PrismaService } from '../prisma/prisma.service';

export interface UniversalAIRequest {
  // Required
  prompt: string;
  organizationId: string;

  // Optional - will be auto-selected if not provided
  provider?: string;
  model?: string;

  // Generation parameters
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stop?: string[];

  // Routing preferences
  priority?: 'cost' | 'speed' | 'quality' | 'balanced';
  maxLatency?: number;
  maxCost?: number;
  requiresStreaming?: boolean;

  // Context and tools
  systemPrompt?: string;
  context?: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
  }>;
  tools?: string[];

  // Metadata
  userId?: string;
  sessionId?: string;
  tags?: string[];
}

export interface UniversalAIResponse {
  // Response content
  content: string;
  finishReason?: string;

  // Provider information
  provider: string;
  model: string;

  // Usage and cost
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  cost: number;

  // Performance metrics
  latency: number;

  // Metadata
  requestId: string;
  timestamp: Date;

  // Routing information
  routing: {
    selected: string;
    confidence: number;
    reasoning: string;
    fallbacksAvailable: number;
  };
}

export interface StreamingResponse {
  requestId: string;
  provider: string;
  model: string;
  stream: AsyncIterable<string>;
}

@Injectable()
export class UniversalSDKService {
  private readonly logger = new Logger(UniversalSDKService.name);

  constructor(
    private aiProvider: AIProviderIntegrationService,
    private smartRouting: SmartRoutingService,
    private loggerService: LoggerService,
    private prisma: PrismaService,
  ) { }

  async complete(request: UniversalAIRequest): Promise<UniversalAIResponse> {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    try {
      // 1. Provider selection (if not specified)
      let selectedProvider = request.provider;
      let selectedModel = request.model;
      let routingInfo: any = null;

      if (!selectedProvider || !selectedModel) {
        const routingRequest: RoutingRequest = {
          organizationId: request.organizationId,
          priority: request.priority || 'balanced',
          maxLatency: request.maxLatency,
          maxCost: request.maxCost,
          requiresStreaming: request.requiresStreaming,
        };

        const routing = await this.smartRouting.selectOptimalProvider(routingRequest);
        selectedProvider = routing.providerId;
        selectedModel = routing.modelId;
        routingInfo = routing;
      }

      // 2. Prepare the AI request
      const aiRequest = this.buildAIRequest(request, selectedProvider, selectedModel, requestId);

      // 3. Execute with fallback logic
      const result = await this.executeWithFallback(aiRequest, routingInfo?.fallbacks || []);

      // 4. Calculate final metrics
      const latency = Date.now() - startTime;

      // 5. Log the request
      await this.logRequest(request, result, latency, requestId);

      // 6. Build response
      return {
        content: result.content,
        finishReason: result.finishReason,
        provider: result.providerId,
        model: result.modelId,
        usage: result.usage,
        cost: result.cost,
        latency,
        requestId,
        timestamp: new Date(),
        routing: {
          selected: selectedProvider!,
          confidence: routingInfo?.confidence || 1.0,
          reasoning: routingInfo?.reasoning || 'Manually specified',
          fallbacksAvailable: routingInfo?.fallbacks?.length || 0,
        },
      };

    } catch (error) {
      const latency = Date.now() - startTime;

      this.loggerService.error('Universal AI request failed', {
        requestId,
        organizationId: request.organizationId,
        userId: request.userId,
        error: error instanceof Error ? error.message : 'Unknown error',
        latency,
      });

      throw error;
    }
  }

  async stream(request: UniversalAIRequest): Promise<StreamingResponse> {
    const requestId = `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      // Provider selection for streaming
      let selectedProvider = request.provider;
      let selectedModel = request.model;

      if (!selectedProvider || !selectedModel) {
        const routingRequest: RoutingRequest = {
          organizationId: request.organizationId,
          priority: request.priority || 'speed', // Prefer speed for streaming
          requiresStreaming: true,
        };

        const routing = await this.smartRouting.selectOptimalProvider(routingRequest);
        selectedProvider = routing.providerId;
        selectedModel = routing.modelId;
      }

      // Build streaming request
      const aiRequest = this.buildAIRequest(request, selectedProvider, selectedModel, requestId);
      aiRequest.stream = true;

      // Get streaming response
      const stream = await this.aiProvider.streamRequest(aiRequest);

      this.loggerService.business('streaming_started', {
        requestId,
        provider: selectedProvider,
        model: selectedModel,
        organizationId: request.organizationId,
        userId: request.userId,
      });

      return {
        requestId,
        provider: selectedProvider!,
        model: selectedModel!,
        stream,
      };

    } catch (error) {
      this.loggerService.error('Streaming request failed', {
        requestId,
        organizationId: request.organizationId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      throw error;
    }
  }

  private buildAIRequest(request: UniversalAIRequest, provider: string, model: string, requestId: string) {
    // Build messages array
    const messages: Array<{ role: 'user' | 'assistant' | 'system'; content: string }> = [];

    // Add system prompt if provided
    if (request.systemPrompt) {
      messages.push({ role: 'system', content: request.systemPrompt });
    }

    // Add context messages
    if (request.context) {
      messages.push(...request.context);
    }

    // Add main prompt
    messages.push({ role: 'user', content: request.prompt });

    return {
      requestId,
      providerId: provider,
      modelId: model,
      messages,
      temperature: request.temperature,
      maxTokens: request.maxTokens,
      topP: request.topP,
      frequencyPenalty: request.frequencyPenalty,
      presencePenalty: request.presencePenalty,
      stop: request.stop,
      organizationId: request.organizationId,
      userId: request.userId,
      sessionId: request.sessionId,
      tools: request.tools,
      stream: false,
    };
  }

  private async executeWithFallback(aiRequest: any, fallbacks: any[]): Promise<any> {
    const attempts = [
      { providerId: aiRequest.providerId, modelId: aiRequest.modelId },
      ...fallbacks.map(f => ({ providerId: f.providerId, modelId: f.modelId })),
    ];

    let lastError: Error | null = null;

    for (let i = 0; i < attempts.length; i++) {
      const attempt = attempts[i];

      try {
        const requestWithProvider = {
          ...aiRequest,
          providerId: attempt.providerId,
          modelId: attempt.modelId,
        };

        const result = await this.aiProvider.processRequest(requestWithProvider);

        // Log successful execution (with fallback info if applicable)
        if (i > 0) {
          this.loggerService.business('fallback_success', {
            requestId: aiRequest.requestId,
            originalProvider: attempts[0].providerId,
            successfulProvider: attempt.providerId,
            attemptNumber: i + 1,
          });
        }

        return result;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        this.loggerService.warn('Provider attempt failed', {
          requestId: aiRequest.requestId,
          provider: attempt.providerId,
          model: attempt.modelId,
          attemptNumber: i + 1,
          error: lastError.message,
        });

        // Continue to next fallback
        continue;
      }
    }

    // All attempts failed
    throw new Error(`All providers failed. Last error: ${lastError?.message}`);
  }

  private async logRequest(request: UniversalAIRequest, result: any, latency: number, requestId: string) {
    try {
      // Log to database
      await this.prisma.aIRequest.create({
        data: {
          id: requestId,
          organizationId: request.organizationId,
          userId: request.userId,
          sessionId: request.sessionId,
          provider: result.providerId,
          model: result.modelId,
          prompt: request.prompt,
          response: result.content,
          tokens: result.usage,
          cost: result.cost,
          latency,
          status: 'COMPLETED',
          metadata: {
            priority: request.priority,
            temperature: request.temperature,
            maxTokens: request.maxTokens,
            tags: request.tags,
          },
        },
      });

      // Log business event
      this.loggerService.business('ai_request_completed', {
        requestId,
        organizationId: request.organizationId,
        userId: request.userId,
        provider: result.providerId,
        model: result.modelId,
        tokens: result.usage.totalTokens,
        cost: result.cost,
        latency,
      });

    } catch (error) {
      this.logger.warn(`Failed to log request: ${error}`);
    }
  }

  // Utility methods for SDK users

  async getAvailableModels(organizationId: string): Promise<Array<{
    provider: string;
    models: Array<{
      id: string;
      name: string;
      capabilities: string[];
      costPerToken: number;
    }>;
  }>> {
    const providers = await this.prisma.aIProvider.findMany({
      where: {
        organizationId,
        isActive: true,
      },
      include: {
        aiModels: {
          where: { isActive: true },
        },
      },
    });

    return providers.map(provider => ({
      provider: provider.name,
      models: provider.aiModels.map(model => ({
        id: model.id,
        name: model.name,
        capabilities: model.capabilities as string[] || [],
        costPerToken: model.costPerToken || 0,
      })),
    }));
  }

  async getUsageStats(organizationId: string, timeframe: 'hour' | 'day' | 'week' | 'month' = 'day') {
    const timeMap = {
      hour: 1,
      day: 24,
      week: 24 * 7,
      month: 24 * 30,
    };

    const hoursAgo = timeMap[timeframe];
    const since = new Date(Date.now() - hoursAgo * 60 * 60 * 1000);

    const stats = await this.prisma.aIRequest.aggregate({
      where: {
        organizationId,
        createdAt: { gte: since },
        status: 'COMPLETED',
      },
      _sum: {
        cost: true,
      },
      _avg: {
        latency: true,
      },
      _count: {
        id: true,
      },
    });

    const tokenStats = await this.prisma.aIRequest.findMany({
      where: {
        organizationId,
        createdAt: { gte: since },
        status: 'COMPLETED',
      },
      select: {
        tokens: true,
      },
    });

    const totalTokens = tokenStats.reduce((sum, req) => {
      const tokens = req.tokens as any;
      return sum + (tokens?.totalTokens || 0);
    }, 0);

    return {
      requests: stats._count.id || 0,
      totalCost: stats._sum.cost || 0,
      totalTokens,
      averageLatency: stats._avg.latency || 0,
      timeframe,
    };
  }
}
