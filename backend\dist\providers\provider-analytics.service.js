"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ProviderAnalyticsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProviderAnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const logger_service_1 = require("../common/services/logger.service");
const schedule_1 = require("@nestjs/schedule");
let ProviderAnalyticsService = ProviderAnalyticsService_1 = class ProviderAnalyticsService {
    constructor(prisma, loggerService) {
        this.prisma = prisma;
        this.loggerService = loggerService;
        this.logger = new common_1.Logger(ProviderAnalyticsService_1.name);
    }
    async generateProviderReport(providerId, timeframe = 'day') {
        const { since, previousSince } = this.getTimeframes(timeframe);
        const currentMetrics = await this.calculateProviderMetrics(providerId, since);
        const previousMetrics = await this.calculateProviderMetrics(providerId, previousSince, since);
        const provider = await this.prisma.aIProvider.findUnique({
            where: { id: providerId },
            select: { name: true },
        });
        const trends = this.calculateTrends(currentMetrics, previousMetrics);
        const recommendations = this.generateRecommendations(currentMetrics, trends);
        return {
            providerId,
            providerName: (provider === null || provider === void 0 ? void 0 : provider.name) || 'Unknown',
            timeframe,
            metrics: currentMetrics,
            trends,
            recommendations,
        };
    }
    async generateOrganizationReport(organizationId, timeframe = 'day') {
        const { since } = this.getTimeframes(timeframe);
        const requests = await this.prisma.aIRequest.findMany({
            where: {
                organizationId,
                createdAt: { gte: since },
                status: 'COMPLETED',
            },
            select: {
                provider: true,
                model: true,
                cost: true,
                tokens: true,
                latency: true,
            },
        });
        if (requests.length === 0) {
            return this.getEmptyReport(organizationId, timeframe);
        }
        const totalRequests = requests.length;
        const totalCost = requests.reduce((sum, req) => sum + req.cost, 0);
        const totalTokens = requests.reduce((sum, req) => {
            const tokens = req.tokens;
            return sum + ((tokens === null || tokens === void 0 ? void 0 : tokens.totalTokens) || 0);
        }, 0);
        const averageLatency = requests.reduce((sum, req) => sum + req.latency, 0) / totalRequests;
        const providerCounts = this.groupBy(requests, 'provider');
        const modelCounts = this.groupBy(requests, 'model');
        const topProvider = Object.keys(providerCounts).reduce((a, b) => providerCounts[a].length > providerCounts[b].length ? a : b);
        const topModel = Object.keys(modelCounts).reduce((a, b) => modelCounts[a].length > modelCounts[b].length ? a : b);
        const providerBreakdown = Object.entries(providerCounts).map(([provider, reqs]) => {
            const providerCost = reqs.reduce((sum, req) => sum + req.cost, 0);
            const providerTokens = reqs.reduce((sum, req) => {
                const tokens = req.tokens;
                return sum + ((tokens === null || tokens === void 0 ? void 0 : tokens.totalTokens) || 0);
            }, 0);
            return {
                provider,
                requests: reqs.length,
                cost: providerCost,
                tokens: providerTokens,
                percentage: (reqs.length / totalRequests) * 100,
            };
        }).sort((a, b) => b.requests - a.requests);
        const costOptimization = await this.analyzeCostOptimization(requests, organizationId);
        return {
            organizationId,
            timeframe,
            summary: {
                totalRequests,
                totalCost,
                totalTokens,
                averageLatency,
                topProvider,
                topModel,
            },
            providerBreakdown,
            costOptimization,
        };
    }
    async calculateProviderMetrics(providerId, since, until) {
        const whereClause = {
            provider: providerId,
            createdAt: { gte: since },
        };
        if (until) {
            whereClause.createdAt.lt = until;
        }
        const requests = await this.prisma.aIRequest.findMany({
            where: whereClause,
            select: {
                status: true,
                latency: true,
                cost: true,
                tokens: true,
            },
        });
        if (requests.length === 0) {
            return this.getEmptyMetrics();
        }
        const successful = requests.filter(r => r.status === 'COMPLETED');
        const failed = requests.filter(r => r.status === 'FAILED');
        const latencies = successful.map(r => r.latency).sort((a, b) => a - b);
        const totalTokens = requests.reduce((sum, req) => {
            const tokens = req.tokens;
            return sum + ((tokens === null || tokens === void 0 ? void 0 : tokens.totalTokens) || 0);
        }, 0);
        const totalCost = requests.reduce((sum, req) => sum + req.cost, 0);
        return {
            totalRequests: requests.length,
            successfulRequests: successful.length,
            failedRequests: failed.length,
            successRate: successful.length / requests.length,
            averageLatency: latencies.reduce((sum, l) => sum + l, 0) / latencies.length,
            p95Latency: latencies[Math.floor(latencies.length * 0.95)] || 0,
            p99Latency: latencies[Math.floor(latencies.length * 0.99)] || 0,
            totalTokens,
            totalCost,
            costPerToken: totalTokens > 0 ? totalCost / totalTokens : 0,
            availability: this.calculateAvailability(requests),
        };
    }
    calculateTrends(current, previous) {
        const safeDivide = (a, b) => b === 0 ? 0 : ((a - b) / b) * 100;
        return {
            requestsChange: safeDivide(current.totalRequests, previous.totalRequests),
            latencyChange: safeDivide(current.averageLatency, previous.averageLatency),
            costChange: safeDivide(current.totalCost, previous.totalCost),
            successRateChange: safeDivide(current.successRate, previous.successRate),
        };
    }
    generateRecommendations(metrics, trends) {
        const recommendations = [];
        if (metrics.successRate < 0.95) {
            recommendations.push('Success rate is below 95%. Consider investigating error patterns.');
        }
        if (metrics.averageLatency > 5000) {
            recommendations.push('Average latency is high (>5s). Consider optimizing requests or switching providers.');
        }
        if (trends.latencyChange > 20) {
            recommendations.push('Latency has increased significantly. Monitor provider performance.');
        }
        if (trends.costChange > 50) {
            recommendations.push('Costs have increased significantly. Review usage patterns and consider optimization.');
        }
        if (metrics.costPerToken > 0.00005) {
            recommendations.push('Cost per token is high. Consider using more cost-effective models.');
        }
        if (metrics.availability < 0.99) {
            recommendations.push('Provider availability is below 99%. Consider adding fallback providers.');
        }
        if (recommendations.length === 0) {
            recommendations.push('Provider performance is within acceptable ranges.');
        }
        return recommendations;
    }
    async analyzeCostOptimization(requests, organizationId) {
        const totalCost = requests.reduce((sum, req) => sum + req.cost, 0);
        const potentialSavings = totalCost * 0.3;
        const recommendations = [
            'Consider using GPT-3.5-turbo instead of GPT-4 for simple tasks',
            'Implement request caching to reduce duplicate API calls',
            'Use shorter prompts to reduce token usage',
            'Batch similar requests when possible',
        ];
        return {
            potentialSavings,
            recommendations,
        };
    }
    getTimeframes(timeframe) {
        const now = new Date();
        const timeMap = {
            hour: 1,
            day: 24,
            week: 24 * 7,
            month: 24 * 30,
        };
        const hours = timeMap[timeframe] || 24;
        const since = new Date(now.getTime() - hours * 60 * 60 * 1000);
        const previousSince = new Date(since.getTime() - hours * 60 * 60 * 1000);
        return { since, previousSince };
    }
    getEmptyMetrics() {
        return {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            successRate: 0,
            averageLatency: 0,
            p95Latency: 0,
            p99Latency: 0,
            totalTokens: 0,
            totalCost: 0,
            costPerToken: 0,
            availability: 1,
        };
    }
    getEmptyReport(organizationId, timeframe) {
        return {
            organizationId,
            timeframe,
            summary: {
                totalRequests: 0,
                totalCost: 0,
                totalTokens: 0,
                averageLatency: 0,
                topProvider: 'None',
                topModel: 'None',
            },
            providerBreakdown: [],
            costOptimization: {
                potentialSavings: 0,
                recommendations: ['No usage data available for the selected timeframe'],
            },
        };
    }
    groupBy(array, key) {
        return array.reduce((groups, item) => {
            const group = String(item[key]);
            groups[group] = groups[group] || [];
            groups[group].push(item);
            return groups;
        }, {});
    }
    calculateAvailability(requests) {
        const recentFailures = requests.filter(r => r.status === 'FAILED' &&
            Date.now() - new Date(r.createdAt).getTime() < 15 * 60 * 1000);
        if (recentFailures.length === 0)
            return 1.0;
        if (recentFailures.length >= 5)
            return 0.8;
        return 0.95;
    }
    async generateDailyReports() {
        try {
            this.logger.log('Starting daily analytics report generation');
            const organizations = await this.prisma.organization.findMany({
                select: { id: true, name: true },
            });
            for (const org of organizations) {
                const report = await this.generateOrganizationReport(org.id, 'day');
                if (report.summary.totalCost > 100) {
                    this.loggerService.business('high_usage_alert', {
                        organizationId: org.id,
                        dailyCost: report.summary.totalCost,
                        requests: report.summary.totalRequests,
                    });
                }
            }
            this.logger.log(`Generated daily reports for ${organizations.length} organizations`);
        }
        catch (error) {
            this.logger.error(`Failed to generate daily reports: ${error}`);
        }
    }
};
exports.ProviderAnalyticsService = ProviderAnalyticsService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_DAY_AT_MIDNIGHT),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ProviderAnalyticsService.prototype, "generateDailyReports", null);
exports.ProviderAnalyticsService = ProviderAnalyticsService = ProviderAnalyticsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        logger_service_1.LoggerService])
], ProviderAnalyticsService);
//# sourceMappingURL=provider-analytics.service.js.map