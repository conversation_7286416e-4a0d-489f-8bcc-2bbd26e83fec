import { WorkflowStatus, WorkflowTriggerType } from '../workflow.entity';
export declare class WorkflowStepDto {
    id: string;
    type: string;
    name: string;
    config: Record<string, any>;
    dependencies?: string[];
    conditions?: Record<string, any>;
}
export declare class WorkflowTriggerDto {
    type: WorkflowTriggerType;
    config: Record<string, any>;
}
export declare class WorkflowErrorHandlingDto {
    retryCount: number;
    retryDelay: number;
    fallbackSteps?: string[];
}
export declare class WorkflowDefinitionDto {
    version: string;
    steps: WorkflowStepDto[];
    triggers: WorkflowTriggerDto[];
    variables: Record<string, any>;
    errorHandling: WorkflowErrorHandlingDto;
}
export declare class WorkflowMetadataDto {
    tags: string[];
    category: string;
    priority: 'low' | 'medium' | 'high' | 'critical';
    estimatedDuration: number;
    costEstimate: number;
}
export declare class WorkflowPermissionsDto {
    owners: string[];
    editors: string[];
    viewers: string[];
    public: boolean;
}
export declare class CreateWorkflowDto {
    name: string;
    description?: string;
    definition: WorkflowDefinitionDto;
    metadata?: WorkflowMetadataDto;
    permissions?: WorkflowPermissionsDto;
}
export declare class UpdateWorkflowDto {
    name?: string;
    description?: string;
    definition?: WorkflowDefinitionDto;
    metadata?: WorkflowMetadataDto;
    permissions?: WorkflowPermissionsDto;
    forceUpdate?: boolean;
}
export declare class WorkflowFiltersDto {
    search?: string;
    status?: WorkflowStatus;
    userId?: string;
    createdAfter?: string;
    createdBefore?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
}
