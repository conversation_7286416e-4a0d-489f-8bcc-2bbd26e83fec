"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentTemplatesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const agent_templates_service_1 = require("./agent-templates.service");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const permission_guard_1 = require("../auth/permission.guard");
const permissions_decorator_1 = require("../auth/permissions.decorator");
let AgentTemplatesController = class AgentTemplatesController {
    constructor(templatesService) {
        this.templatesService = templatesService;
    }
    async createTemplate(createTemplateDto, req) {
        return this.templatesService.createTemplate(createTemplateDto, req.user.organizationId, req.user.id);
    }
    async getTemplates(req) {
        return this.templatesService.getTemplatesByOrganization(req.user.organizationId);
    }
    async getTemplateCategories(req) {
        return this.templatesService.getTemplateCategories(req.user.organizationId);
    }
    async getTemplatesByCategory(category, req) {
        return this.templatesService.getTemplatesByCategory(category, req.user.organizationId);
    }
    async searchTemplates(query, req) {
        return this.templatesService.searchTemplates(query, req.user.organizationId);
    }
    async getPopularTemplates(limit = 10, req) {
        return this.templatesService.getPopularTemplates(req.user.organizationId, limit);
    }
    async getTemplate(id, req) {
        return this.templatesService.getTemplateById(id, req.user.organizationId);
    }
    async updateTemplate(id, updateData, req) {
        return this.templatesService.updateTemplate(id, updateData, req.user.organizationId);
    }
    async deleteTemplate(id, req) {
        return this.templatesService.deleteTemplate(id, req.user.organizationId);
    }
    async duplicateTemplate(id, req) {
        return this.templatesService.duplicateTemplate(id, req.user.organizationId, req.user.id);
    }
    async getTemplateStats(id, req) {
        return this.templatesService.getTemplateUsageStats(id, req.user.organizationId);
    }
    async seedDefaultTemplates(req) {
        return this.templatesService.seedDefaultTemplates(req.user.organizationId, req.user.id);
    }
};
exports.AgentTemplatesController = AgentTemplatesController;
__decorate([
    (0, common_1.Post)(),
    (0, permissions_decorator_1.RequirePermissions)('agents:create', 'agents:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new agent template' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Template created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input data' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Insufficient permissions' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AgentTemplatesController.prototype, "createTemplate", null);
__decorate([
    (0, common_1.Get)(),
    (0, permissions_decorator_1.RequirePermissions)('agents:read', 'agents:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all templates for the organization' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Templates retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AgentTemplatesController.prototype, "getTemplates", null);
__decorate([
    (0, common_1.Get)('categories'),
    (0, permissions_decorator_1.RequirePermissions)('agents:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get template categories with counts' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Categories retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AgentTemplatesController.prototype, "getTemplateCategories", null);
__decorate([
    (0, common_1.Get)('category/:category'),
    (0, permissions_decorator_1.RequirePermissions)('agents:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get templates by category' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Templates retrieved successfully' }),
    __param(0, (0, common_1.Param)('category')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentTemplatesController.prototype, "getTemplatesByCategory", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, permissions_decorator_1.RequirePermissions)('agents:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Search templates by query' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Search results retrieved successfully' }),
    __param(0, (0, common_1.Query)('q')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentTemplatesController.prototype, "searchTemplates", null);
__decorate([
    (0, common_1.Get)('popular'),
    (0, permissions_decorator_1.RequirePermissions)('agents:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get popular templates' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Popular templates retrieved successfully' }),
    __param(0, (0, common_1.Query)('limit')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], AgentTemplatesController.prototype, "getPopularTemplates", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, permissions_decorator_1.RequirePermissions)('agents:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get template by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Template retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Template not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentTemplatesController.prototype, "getTemplate", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, permissions_decorator_1.RequirePermissions)('agents:update'),
    (0, swagger_1.ApiOperation)({ summary: 'Update template' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Template updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Template not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], AgentTemplatesController.prototype, "updateTemplate", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, permissions_decorator_1.RequirePermissions)('agents:delete'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete template' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Template deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Template not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentTemplatesController.prototype, "deleteTemplate", null);
__decorate([
    (0, common_1.Post)(':id/duplicate'),
    (0, permissions_decorator_1.RequirePermissions)('agents:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Duplicate template' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Template duplicated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Template not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentTemplatesController.prototype, "duplicateTemplate", null);
__decorate([
    (0, common_1.Get)(':id/stats'),
    (0, permissions_decorator_1.RequirePermissions)('agents:read'),
    (0, swagger_1.ApiOperation)({ summary: 'Get template usage statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Statistics retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Template not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentTemplatesController.prototype, "getTemplateStats", null);
__decorate([
    (0, common_1.Post)('seed-defaults'),
    (0, permissions_decorator_1.RequirePermissions)('agents:create'),
    (0, swagger_1.ApiOperation)({ summary: 'Seed default templates for organization' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Default templates seeded successfully' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AgentTemplatesController.prototype, "seedDefaultTemplates", null);
exports.AgentTemplatesController = AgentTemplatesController = __decorate([
    (0, swagger_1.ApiTags)('Agent Templates'),
    (0, common_1.Controller)('agent-templates'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, permission_guard_1.PermissionGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [agent_templates_service_1.AgentTemplatesService])
], AgentTemplatesController);
//# sourceMappingURL=agent-templates.controller.js.map