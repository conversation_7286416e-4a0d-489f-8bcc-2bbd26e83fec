"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TenantId = void 0;
const common_1 = require("@nestjs/common");
exports.TenantId = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const host = request.headers.host;
    if (host) {
        const subdomain = host.split('.')[0];
        if (subdomain && subdomain !== 'www' && subdomain !== 'api') {
            return subdomain;
        }
    }
    const tenantHeader = request.headers['x-tenant-id'];
    if (tenantHeader) {
        return tenantHeader;
    }
    return undefined;
});
//# sourceMappingURL=tenant-id.decorator.js.map