import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { LoggerService } from '../../common/services/logger.service';
export interface ToolExecutionContext {
    toolId: string;
    agentId?: string;
    sessionId?: string;
    parameters: Record<string, any>;
    timeout?: number;
    retryCount?: number;
}
export interface ToolExecutionResult {
    success: boolean;
    output: any;
    duration: number;
    error?: string;
    metadata?: Record<string, any>;
}
export interface ToolDefinition {
    id: string;
    name: string;
    type: 'API' | 'WEBHOOK' | 'FUNCTION' | 'DATABASE';
    config: {
        endpoint?: string;
        method?: string;
        headers?: Record<string, string>;
        authentication?: {
            type: 'bearer' | 'api_key' | 'basic';
            credentials: Record<string, string>;
        };
        parameters?: Array<{
            name: string;
            type: string;
            required: boolean;
            description: string;
        }>;
        timeout?: number;
        retries?: number;
    };
    schema: {
        input: Record<string, any>;
        output: Record<string, any>;
    };
}
export declare class ToolRuntimeService {
    private prisma;
    private configService;
    private loggerService;
    private readonly logger;
    constructor(prisma: PrismaService, configService: ConfigService, loggerService: LoggerService);
    executeTool(context: ToolExecutionContext): Promise<ToolExecutionResult>;
    private loadTool;
    private validateParameters;
    private validateParameterType;
    private executeByType;
    private executeApiTool;
    private executeWebhookTool;
    private executeFunctionTool;
    private executeDatabaseTool;
    private executeCustomFunction;
    private executeDatabaseQuery;
    private logExecution;
    getAvailableTools(agentId: string): Promise<ToolDefinition[]>;
}
