import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthService } from '../auth.service';

export interface PermissionMetadata {
    resource: string;
    action: string;
}

@Injectable()
export class PermissionGuard implements CanActivate {
    constructor(
        private reflector: Reflector,
        private authService: AuthService,
    ) { }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const requiredPermissions = this.reflector.getAllAndOverride<PermissionMetadata[]>('permissions', [
            context.getHandler(),
            context.getClass(),
        ]);

        if (!requiredPermissions || requiredPermissions.length === 0) {
            return true; // No permissions required
        }

        const request = context.switchToHttp().getRequest();
        const user = request.user;

        if (!user) {
            throw new ForbiddenException('Authentication required');
        }

        // Check if user has any of the required permissions
        for (const permission of requiredPermissions) {
            const hasPermission = await this.authService.hasPermission(
                user.userId,
                permission.resource,
                permission.action,
            );

            if (hasPermission) {
                return true;
            }
        }

        throw new ForbiddenException('Insufficient permissions');
    }
} 