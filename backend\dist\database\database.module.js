"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const user_entity_1 = require("../users/entities/user.entity");
const role_entity_1 = require("../roles/entities/role.entity");
const permission_entity_1 = require("../permissions/entities/permission.entity");
const tenant_entity_1 = require("../tenants/entities/tenant.entity");
const refresh_token_entity_1 = require("../auth/entities/refresh-token.entity");
let DatabaseModule = class DatabaseModule {
};
exports.DatabaseModule = DatabaseModule;
exports.DatabaseModule = DatabaseModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => ({
                    type: 'postgres',
                    host: configService.get('app.database.host'),
                    port: configService.get('app.database.port'),
                    username: configService.get('app.database.username'),
                    password: configService.get('app.database.password'),
                    database: configService.get('app.database.database'),
                    entities: [user_entity_1.User, role_entity_1.Role, permission_entity_1.Permission, tenant_entity_1.Tenant, refresh_token_entity_1.RefreshToken],
                    synchronize: configService.get('app.database.synchronize'),
                    logging: configService.get('app.database.logging'),
                    ssl: configService.get('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
                    migrations: ['dist/database/migrations/*.js'],
                    migrationsRun: true,
                    migrationsTableName: 'migrations',
                    cache: {
                        type: 'redis',
                        options: {
                            host: configService.get('app.redis.host'),
                            port: configService.get('app.redis.port'),
                            password: configService.get('app.redis.password'),
                            db: configService.get('app.redis.db'),
                        },
                    },
                    extra: {
                        max: 20,
                        connectionTimeoutMillis: 5000,
                        idleTimeoutMillis: 30000,
                    },
                }),
                inject: [config_1.ConfigService],
            }),
        ],
    })
], DatabaseModule);
//# sourceMappingURL=database.module.js.map