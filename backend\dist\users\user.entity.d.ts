import { BaseEntity } from 'typeorm';
import { Workflow } from '../workflows/workflow.entity';
import { Agent } from '../agents/agent.entity';
import { Tool } from '../tools/tool.entity';
import { Tenant } from '../tenants/entities/tenant.entity';
import { Organization } from '../organizations/entities/organization.entity';
export declare enum UserRole {
    ADMIN = "admin",
    USER = "user",
    VIEWER = "viewer"
}
export declare enum AuthProvider {
    LOCAL = "local",
    GOOGLE = "google",
    GITHUB = "github"
}
export declare class User extends BaseEntity {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    password: string;
    avatar: string;
    role: UserRole;
    authProvider: AuthProvider;
    authToken: string;
    googleId: string;
    githubId: string;
    isActive: boolean;
    lastLoginAt: Date;
    preferences: Record<string, any>;
    workflows: Workflow[];
    agents: Agent[];
    tools: Tool[];
    tenantId: string;
    tenant: Tenant;
    organizationId: string;
    organization: Organization;
    createdAt: Date;
    updatedAt: Date;
}
