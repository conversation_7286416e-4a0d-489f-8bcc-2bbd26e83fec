"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AgentAnalyticsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentAnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const agent_execution_entity_1 = require("../database/entities/agent-execution.entity");
const agent_entity_1 = require("../database/entities/agent.entity");
let AgentAnalyticsService = AgentAnalyticsService_1 = class AgentAnalyticsService {
    constructor(executionRepository, agentRepository) {
        this.executionRepository = executionRepository;
        this.agentRepository = agentRepository;
        this.logger = new common_1.Logger(AgentAnalyticsService_1.name);
    }
    async trackExecution(execution) {
        try {
            this.logger.debug(`Tracked execution: ${execution.id} for agent: ${execution.agentId}`);
        }
        catch (error) {
            this.logger.error(`Failed to track execution analytics: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async getAgentAnalytics(agent, organizationId) {
        try {
            const executions = await this.executionRepository.find({
                where: { agentId: agent.id, organizationId },
                order: { createdAt: 'DESC' },
            });
            const totalExecutions = executions.length;
            const successfulExecutions = executions.filter(e => e.status === 'COMPLETED').length;
            const successRate = totalExecutions > 0 ? successfulExecutions / totalExecutions : 0;
            const responseTimes = executions
                .filter(e => { var _a; return (_a = e.metadata) === null || _a === void 0 ? void 0 : _a.duration; })
                .map(e => e.metadata.duration);
            const averageResponseTime = responseTimes.length > 0
                ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
                : 0;
            const totalCost = executions
                .filter(e => { var _a; return (_a = e.metadata) === null || _a === void 0 ? void 0 : _a.cost; })
                .reduce((sum, e) => sum + e.metadata.cost, 0);
            const providerDistribution = executions
                .filter(e => { var _a; return (_a = e.metadata) === null || _a === void 0 ? void 0 : _a.provider; })
                .reduce((acc, e) => {
                const provider = e.metadata.provider;
                acc[provider] = (acc[provider] || 0) + 1;
                return acc;
            }, {});
            const errorRate = totalExecutions > 0
                ? (totalExecutions - successfulExecutions) / totalExecutions
                : 0;
            const lastExecuted = executions.length > 0 ? executions[0].createdAt : new Date();
            const performanceTrend = await this.generatePerformanceTrend(agent.id, organizationId, 7);
            return {
                agentId: agent.id,
                totalExecutions,
                successRate,
                averageResponseTime,
                totalCost,
                providerDistribution,
                errorRate,
                lastExecuted,
                performanceTrend,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get agent analytics for ${agent.id}: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }
    async getOrganizationAnalytics(organizationId) {
        try {
            const agents = await this.agentRepository.find({
                where: { organizationId },
            });
            const executions = await this.executionRepository.find({
                where: { organizationId },
                relations: ['agent'],
                order: { createdAt: 'DESC' },
            });
            const totalAgents = agents.length;
            const totalExecutions = executions.length;
            const totalCost = executions
                .filter(e => { var _a; return (_a = e.metadata) === null || _a === void 0 ? void 0 : _a.cost; })
                .reduce((sum, e) => sum + e.metadata.cost, 0);
            const successfulExecutions = executions.filter(e => e.status === 'COMPLETED').length;
            const averageSuccessRate = totalExecutions > 0 ? successfulExecutions / totalExecutions : 0;
            const agentStats = agents.map(agent => {
                const agentExecutions = executions.filter(e => e.agentId === agent.id);
                const successful = agentExecutions.filter(e => e.status === 'COMPLETED').length;
                const successRate = agentExecutions.length > 0 ? successful / agentExecutions.length : 0;
                return {
                    agentId: agent.id,
                    name: agent.name,
                    executions: agentExecutions.length,
                    successRate,
                };
            });
            const topPerformingAgents = agentStats
                .sort((a, b) => b.executions - a.executions)
                .slice(0, 5);
            const providerUsage = executions
                .filter(e => { var _a; return (_a = e.metadata) === null || _a === void 0 ? void 0 : _a.provider; })
                .reduce((acc, e) => {
                const provider = e.metadata.provider;
                acc[provider] = (acc[provider] || 0) + 1;
                return acc;
            }, {});
            const dailyExecutions = await this.getDailyExecutions(organizationId, 7);
            return {
                totalAgents,
                totalExecutions,
                totalCost,
                averageSuccessRate,
                topPerformingAgents,
                providerUsage,
                dailyExecutions,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get organization analytics: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }
    async generatePerformanceTrend(agentId, organizationId, days) {
        const trend = [];
        const now = new Date();
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date(now);
            date.setDate(date.getDate() - i);
            const startOfDay = new Date(date.setHours(0, 0, 0, 0));
            const endOfDay = new Date(date.setHours(23, 59, 59, 999));
            const dayExecutions = await this.executionRepository.find({
                where: {
                    agentId,
                    organizationId,
                    createdAt: {
                        gte: startOfDay,
                        lte: endOfDay,
                    },
                },
            });
            const executions = dayExecutions.length;
            const successful = dayExecutions.filter(e => e.status === 'COMPLETED').length;
            const successRate = executions > 0 ? successful / executions : 0;
            const responseTimes = dayExecutions
                .filter(e => { var _a; return (_a = e.metadata) === null || _a === void 0 ? void 0 : _a.duration; })
                .map(e => e.metadata.duration);
            const averageTime = responseTimes.length > 0
                ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
                : 0;
            const cost = dayExecutions
                .filter(e => { var _a; return (_a = e.metadata) === null || _a === void 0 ? void 0 : _a.cost; })
                .reduce((sum, e) => sum + e.metadata.cost, 0);
            trend.push({
                period: startOfDay.toISOString().split('T')[0],
                executions,
                successRate,
                averageTime,
                cost,
            });
        }
        return trend;
    }
    async getDailyExecutions(organizationId, days) {
        const dailyData = [];
        const now = new Date();
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date(now);
            date.setDate(date.getDate() - i);
            const startOfDay = new Date(date.setHours(0, 0, 0, 0));
            const endOfDay = new Date(date.setHours(23, 59, 59, 999));
            const dayExecutions = await this.executionRepository.find({
                where: {
                    organizationId,
                    createdAt: {
                        gte: startOfDay,
                        lte: endOfDay,
                    },
                },
            });
            const executions = dayExecutions.length;
            const cost = dayExecutions
                .filter(e => { var _a; return (_a = e.metadata) === null || _a === void 0 ? void 0 : _a.cost; })
                .reduce((sum, e) => sum + e.metadata.cost, 0);
            dailyData.push({
                date: startOfDay.toISOString().split('T')[0],
                executions,
                cost,
            });
        }
        return dailyData;
    }
    async getProviderPerformance(organizationId) {
        try {
            const executions = await this.executionRepository.find({
                where: { organizationId },
            });
            const providerStats = executions
                .filter(e => { var _a; return (_a = e.metadata) === null || _a === void 0 ? void 0 : _a.provider; })
                .reduce((acc, execution) => {
                const provider = execution.metadata.provider;
                if (!acc[provider]) {
                    acc[provider] = {
                        executions: 0,
                        successful: 0,
                        totalTime: 0,
                        totalCost: 0,
                        timeCount: 0,
                    };
                }
                acc[provider].executions++;
                if (execution.status === 'COMPLETED') {
                    acc[provider].successful++;
                }
                if (execution.metadata.duration) {
                    acc[provider].totalTime += execution.metadata.duration;
                    acc[provider].timeCount++;
                }
                if (execution.metadata.cost) {
                    acc[provider].totalCost += execution.metadata.cost;
                }
                return acc;
            }, {});
            return Object.entries(providerStats).map(([provider, stats]) => ({
                provider,
                executions: stats.executions,
                successRate: stats.executions > 0 ? stats.successful / stats.executions : 0,
                averageResponseTime: stats.timeCount > 0 ? stats.totalTime / stats.timeCount : 0,
                totalCost: stats.totalCost,
                reliability: stats.executions > 0 ? stats.successful / stats.executions : 0,
            }));
        }
        catch (error) {
            this.logger.error(`Failed to get provider performance: ${error instanceof Error ? error.message : String(error)}`);
            return [];
        }
    }
};
exports.AgentAnalyticsService = AgentAnalyticsService;
exports.AgentAnalyticsService = AgentAnalyticsService = AgentAnalyticsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(agent_execution_entity_1.AgentExecution)),
    __param(1, (0, typeorm_1.InjectRepository)(agent_entity_1.Agent)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], AgentAnalyticsService);
//# sourceMappingURL=agent-analytics.service.js.map