{"version": 3, "file": "agent-runtime.service.js", "sourceRoot": "", "sources": ["../../../src/agents/execution/agent-runtime.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,gEAA4D;AAC5D,yEAAqE;AA8C9D,IAAM,mBAAmB,2BAAzB,MAAM,mBAAmB;IAG9B,YACU,MAAqB,EACrB,aAA4B,EAC5B,aAA4B;QAF5B,WAAM,GAAN,MAAM,CAAe;QACrB,kBAAa,GAAb,aAAa,CAAe;QAC5B,kBAAa,GAAb,aAAa,CAAe;QALrB,WAAM,GAAG,IAAI,eAAM,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;IAM5D,CAAC;IAEJ,KAAK,CAAC,YAAY,CAAC,OAA8B;QAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YAEH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACpD,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,KAAK,CAAC,SAAS,OAAO,CAAC,OAAO,YAAY,CAAC,CAAC;YACxD,CAAC;YAGD,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAG5E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;YAG9E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAGzE,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,SAAS,EAAE;gBAC/D,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,OAAO,EAAE,gBAAgB,CAAC,SAAS,IAAI,EAAE;aAC1C,CAAC,CAAC;YAGH,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;YAEpE,uCACK,MAAM,KACT,QAAQ,IACR;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAE9E,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,2BAA2B,YAAY,EAAE,EAAE;gBAClE,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,KAAK,EAAE,YAAY;gBACnB,QAAQ;aACT,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,UAAU,YAAY,EAAE;gBAChC,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE;gBACzC,IAAI,EAAE,CAAC;gBACP,QAAQ;gBACR,QAAQ,EAAE,SAAS;gBACnB,KAAK,EAAE,SAAS;gBAChB,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,OAAe;QACrC,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC;YACxC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;YACtB,OAAO,EAAE;gBACP,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,KAAU,EAAE,OAA8B;;QAE9E,MAAM,UAAU,GAAG,CAAA,MAAA,KAAK,CAAC,QAAQ,0CAAE,MAAM,KAAI,KAAK,CAAC,MAAM,IAAI,EAAE,CAAC;QAEhE,OAAO;YACL,OAAO,EAAE,KAAK,CAAC,EAAE;YACjB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,QAAQ,EAAE,KAAK,CAAC,eAAe;YAC/B,KAAK,EAAE,UAAU,CAAC,KAAK,IAAI,eAAe;YAC1C,WAAW,EAAE,UAAU,CAAC,WAAW,IAAI,GAAG;YAC1C,SAAS,EAAE,UAAU,CAAC,SAAS,IAAI,IAAI;YACvC,cAAc,EAAE,CAAA,MAAA,KAAK,CAAC,QAAQ,0CAAE,cAAc,KAAI,KAAK,CAAC,cAAc;YACtE,SAAS,gDACJ,UAAU,CAAC,SAAS,GACpB,OAAO,CAAC,SAAS,KACpB,KAAK,EAAE,OAAO,CAAC,KAAK,EACpB,UAAU,EAAE,KAAK,CAAC,IAAI,EACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,GACpC;YACD,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,EAAE;SAC3B,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,OAAe,EAAE,SAAkB;QAC/D,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO;gBACL,SAAS,EAAE,EAAE;gBACb,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,EAAE;aACZ,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YAEH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC;gBACxD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;gBACxB,OAAO,EAAE;oBACP,QAAQ,EAAE;wBACR,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;wBAC9B,IAAI,EAAE,EAAE;qBACT;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO;oBACL,SAAS,EAAE,EAAE;oBACb,QAAQ,EAAE,EAAE;oBACZ,OAAO,EAAE,EAAE;iBACZ,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,SAAS,EAAE,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;oBACtC,IAAI,EAAE,GAAG,CAAC,IAAuC;oBACjD,OAAO,EAAE,GAAG,CAAC,OAAO;oBACpB,SAAS,EAAE,GAAG,CAAC,SAAS;iBACzB,CAAC,CAAC;gBACH,QAAQ,EAAE,OAAO,CAAC,cAAuB,IAAI,EAAE;gBAC/C,OAAO,EAAE,OAAO,CAAC,OAA8B,IAAI,EAAE;aACtD,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,OAAO,KAAK,KAAK,EAAE,CAAC,CAAC;YACzE,OAAO;gBACL,SAAS,EAAE,EAAE;gBACb,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,EAAE;aACZ,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,KAAU,EAAE,OAAY,EAAE,MAAmB;QAEvE,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAGnF,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAG3D,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC;YACrC,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,MAAM;YACN,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,KAAK,EAAE,OAAO,CAAC,KAAK;SACrB,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,MAAM,CAAC,OAAO;YACtB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,QAAQ,EAAE;gBACR,YAAY,EAAE,MAAM,CAAC,MAAM;gBAC3B,WAAW,EAAE,MAAM,CAAC,SAAS,CAAC,MAAM;aACrC;SACF,CAAC;IACJ,CAAC;IAEO,WAAW,CAAC,QAAgB,EAAE,SAA8B,EAAE,MAAmB;QACvF,IAAI,MAAM,GAAG,QAAQ,CAAC;QAGtB,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YACjD,MAAM,WAAW,GAAG,KAAK,GAAG,IAAI,CAAC;YACjC,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAGH,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS;iBACnC,KAAK,CAAC,CAAC,CAAC,CAAC;iBACT,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,OAAO,EAAE,CAAC;iBACzC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEd,MAAM,GAAG,2BAA2B,aAAa,OAAO,MAAM,EAAE,CAAC;QACnE,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,kBAAkB,CAAC,QAAgB;QAGzC,OAAO;YACL,QAAQ,EAAE,KAAK,EAAE,MAAW,EAAE,EAAE,CAAC,CAAC;gBAChC,OAAO,EAAE,sBAAsB,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK;gBACnE,MAAM,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE;gBAC9C,IAAI,EAAE,KAAK;aACZ,CAAC;SACH,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,OAAe,EAAE,SAA6B,EAAE,IAAS;QACvF,IAAI,CAAC,SAAS;YAAE,OAAO;QAEvB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;gBACpC,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;gBACxB,IAAI,EAAE;oBACJ,QAAQ,EAAE;wBACR,MAAM,EAAE;4BACN;gCACE,IAAI,EAAE,MAAM;gCACZ,OAAO,EAAE,IAAI,CAAC,KAAK;gCACnB,QAAQ,EAAE,IAAI,CAAC,OAAO;6BACvB;4BACD;gCACE,IAAI,EAAE,WAAW;gCACjB,OAAO,EAAE,IAAI,CAAC,MAAM;gCACpB,QAAQ,EAAE,EAAE,OAAO,EAAE;6BACtB;yBACF;qBACF;oBACD,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,OAAe,EAAE,OAA8B,EAAE,MAA4B,EAAE,QAAgB;QACxH,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;gBACtC,IAAI,EAAE;oBACJ,OAAO;oBACP,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ;oBAC/C,QAAQ,EAAE,MAAM,CAAC,QAAQ;oBACzB,KAAK,EAAE,MAAM,CAAC,KAAK;oBACnB,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,QAAQ;oBACR,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,EAAE;oBAC/B,YAAY,EAAE,MAAM,CAAC,KAAK;iBAC3B;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,iBAAiB,EAAE;gBAC7C,OAAO;gBACP,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,QAAQ;gBACR,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK;gBAC3B,IAAI,EAAE,MAAM,CAAC,IAAI;aAClB,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;CACF,CAAA;AApRY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAKO,8BAAa;QACN,sBAAa;QACb,8BAAa;GAN3B,mBAAmB,CAoR/B"}