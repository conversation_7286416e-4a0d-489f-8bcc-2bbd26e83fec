import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { AIProvider, AIModel, ProviderUsage, ProviderHealthCheck } from '../database/entities/ai-provider.entity';
import { AIProviderController } from './ai-provider.controller';
import { AIProviderManagerService } from './ai-provider-manager.service';
import { AIProviderSelectorService } from './ai-provider-selector.service';
import { AIProviderIntegrationService } from './ai-provider-integration.service';
import { AIProviderStreamService } from './ai-provider-stream.service';
import { UniversalSDKService } from './universal-sdk.service';
import { SmartRoutingService } from './smart-routing.service';
import { ProviderAnalyticsService } from './provider-analytics.service';
import { ProviderManagementController } from './provider-management.controller';
import { ApixModule } from '../websocket/apix.module';
import { PrismaModule } from '../prisma/prisma.module';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AIProvider,
      AIModel,
      ProviderUsage,
      ProviderHealthCheck,
    ]),
    ScheduleModule.forRoot(),
    ApixModule,
    PrismaModule,
    CommonModule,
  ],
  controllers: [AIProviderController, ProviderManagementController],
  providers: [
    AIProviderManagerService,
    AIProviderSelectorService,
    AIProviderIntegrationService,
    AIProviderStreamService,
    UniversalSDKService,
    SmartRoutingService,
    ProviderAnalyticsService,
  ],
  exports: [
    AIProviderManagerService,
    AIProviderSelectorService,
    AIProviderIntegrationService,
    AIProviderStreamService,
    UniversalSDKService,
    SmartRoutingService,
    ProviderAnalyticsService,
  ],
})
export class AIProviderModule { }