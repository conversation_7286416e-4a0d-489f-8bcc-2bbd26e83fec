import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import {
    Zap,
    Settings,
    Shield,
    Eye,
    EyeOff,
    CheckCircle,
    AlertCircle,
    Loader2,
    Plus,
    TestTube,
    Sparkles,
    Globe,
    Cpu,
    Brain,
    Code,
    Camera,
    MessageSquare,
    BarChart3
} from 'lucide-react';
import { aiProviderApiClient } from '@/lib/api/ai-provider-api';

interface ProviderConfig {
    id: string;
    name: string;
    type: 'OPENAI' | 'CLAUDE' | 'GEMINI' | 'MISTRAL' | 'GROQ' | 'CUSTOM';
    apiKey: string;
    baseUrl?: string;
    timeout: number;
    isActive: boolean;
    models: Array<{
        name: string;
        capabilities: {
            chat: boolean;
            completion: boolean;
            embedding: boolean;
            vision: boolean;
            functionCalling: boolean;
            codeGeneration: boolean;
            analysis: boolean;
            multimodal: boolean;
            streaming: boolean;
        };
        maxTokens: number;
        costPer1KInput: number;
        costPer1KOutput: number;
    }>;
}

interface ProviderTemplate {
    id: string;
    name: string;
    type: string;
    description: string;
    icon: string;
    color: string;
    setupSteps: string[];
    features: string[];
}

const providerTemplates: ProviderTemplate[] = [
    {
        id: 'openai',
        name: 'OpenAI',
        type: 'OPENAI',
        description: 'Powerful language models including GPT-4 and GPT-3.5',
        icon: '🤖',
        color: 'bg-green-500',
        setupSteps: [
            'Get your API key from OpenAI platform',
            'Enter your API key below',
            'Test the connection'
        ],
        features: ['GPT-4', 'GPT-3.5', 'DALL-E', 'Whisper', 'Embeddings']
    },
    {
        id: 'claude',
        name: 'Claude',
        type: 'CLAUDE',
        description: 'Anthropic\'s advanced AI assistant',
        icon: '🧠',
        color: 'bg-purple-500',
        setupSteps: [
            'Get your API key from Anthropic console',
            'Enter your API key below',
            'Test the connection'
        ],
        features: ['Claude-3', 'Claude-2', 'Sonnet', 'Haiku', 'Opus']
    },
    {
        id: 'gemini',
        name: 'Gemini',
        type: 'GEMINI',
        description: 'Google\'s multimodal AI model',
        icon: '💎',
        color: 'bg-blue-500',
        setupSteps: [
            'Get your API key from Google AI Studio',
            'Enter your API key below',
            'Test the connection'
        ],
        features: ['Gemini Pro', 'Gemini Flash', 'Multimodal', 'Code Generation']
    },
    {
        id: 'mistral',
        name: 'Mistral',
        type: 'MISTRAL',
        description: 'High-performance open models',
        icon: '🌪️',
        color: 'bg-orange-500',
        setupSteps: [
            'Get your API key from Mistral AI',
            'Enter your API key below',
            'Test the connection'
        ],
        features: ['Mistral Large', 'Mistral Medium', 'Mistral Small', 'Embeddings']
    },
    {
        id: 'groq',
        name: 'Groq',
        type: 'GROQ',
        description: 'Ultra-fast inference platform',
        icon: '⚡',
        color: 'bg-red-500',
        setupSteps: [
            'Get your API key from Groq console',
            'Enter your API key below',
            'Test the connection'
        ],
        features: ['Llama-3', 'Mixtral', 'Gemma', 'Ultra-fast inference']
    }
];

interface ProviderConfigModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (config: ProviderConfig) => void;
}

export default function ProviderConfigModal({ isOpen, onClose, onSave }: ProviderConfigModalProps) {
    const [selectedTemplate, setSelectedTemplate] = useState<ProviderTemplate | null>(null);
    const [config, setConfig] = useState<ProviderConfig>({
        id: '',
        name: '',
        type: 'OPENAI',
        apiKey: '',
        baseUrl: '',
        timeout: 30000,
        isActive: true,
        models: []
    });
    const [showApiKey, setShowApiKey] = useState(false);
    const [isTesting, setIsTesting] = useState(false);
    const [testResult, setTestResult] = useState<any>(null);
    const [activeTab, setActiveTab] = useState('template');
    const { toast } = useToast();

    const handleTemplateSelect = (template: ProviderTemplate) => {
        setSelectedTemplate(template);
        setConfig(prev => ({
            ...prev,
            name: template.name,
            type: template.type as any
        }));
        setActiveTab('config');
    };

    const handleTestConnection = async () => {
        setIsTesting(true);
        try {
            const response = await aiProviderApiClient.testProvider(config.id);


    if (!response.success) {
        throw new Error(response.error || 'Provider test failed');
    }

    const result = response.data;
    if (result) {
        setTestResult(result);
        toast({
            title: "Test Successful",
            description: `Provider test completed successfully. Latency: ${result.latency}ms`
        });
    } else {
        throw new Error('Provider test failed'); 
    }
} catch (error) {
    console.error('Test connection failed:', error);
    setTestResult({
        success: false,
        error: error instanceof Error ? error.message : 'Connection failed'
    });
    toast({
        title: "Test Failed",
        description: error instanceof Error ? error.message : "Provider test failed",
        variant: "destructive"
    });
} finally {
    setIsTesting(false);
}
    };

const handleSave = () => {
    onSave(config);
    onClose();
};

return (
    <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
                <DialogTitle className="flex items-center gap-2 text-2xl">
                    <Zap className="h-6 w-6 text-blue-600" />
                    Add AI Provider
                </DialogTitle>
                <DialogDescription>
                    Configure a new AI provider for intelligent routing and optimization
                </DialogDescription>
            </DialogHeader>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="template">Choose Provider</TabsTrigger>
                    <TabsTrigger value="config">Configuration</TabsTrigger>
                    <TabsTrigger value="test">Test & Save</TabsTrigger>
                </TabsList>

                <TabsContent value="template" className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {providerTemplates.map((template) => (
                            <div
                                key={template.id}
                                className={`group relative p-6 rounded-2xl border-2 cursor-pointer transition-all duration-300 hover:shadow-lg ${selectedTemplate?.id === template.id
                                    ? 'border-blue-500 bg-blue-50 shadow-lg'
                                    : 'border-gray-200 hover:border-blue-300 bg-white'
                                    }`}
                                onClick={() => handleTemplateSelect(template)}
                            >
                                <div className="flex items-center gap-3 mb-4">
                                    <div className={`w-12 h-12 rounded-xl ${template.color} flex items-center justify-center text-white text-2xl`}>
                                        {template.icon}
                                    </div>
                                    <div>
                                        <h3 className="font-semibold text-gray-900">{template.name}</h3>
                                        <p className="text-sm text-gray-600">{template.type}</p>
                                    </div>
                                </div>
                                <p className="text-sm text-gray-600 mb-4">{template.description}</p>
                                <div className="flex flex-wrap gap-1">
                                    {template.features.slice(0, 3).map((feature) => (
                                        <Badge key={feature} variant="outline" className="text-xs">
                                            {feature}
                                        </Badge>
                                    ))}
                                    {template.features.length > 3 && (
                                        <Badge variant="outline" className="text-xs">
                                            +{template.features.length - 3} more
                                        </Badge>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </TabsContent>

                <TabsContent value="config" className="space-y-6">
                    {selectedTemplate && (
                        <>
                            <Alert>
                                <Sparkles className="h-4 w-4" />
                                <AlertDescription>
                                    Setting up <strong>{selectedTemplate.name}</strong>. Follow the steps below to configure your provider.
                                </AlertDescription>
                            </Alert>

                            <div className="space-y-4">
                                <div>
                                    <Label htmlFor="name">Provider Name</Label>
                                    <Input
                                        id="name"
                                        value={config.name}
                                        onChange={(e) => setConfig(prev => ({ ...prev, name: e.target.value }))}
                                        placeholder="Enter a name for this provider"
                                    />
                                </div>

                                <div>
                                    <Label htmlFor="apiKey">API Key</Label>
                                    <div className="relative">
                                        <Input
                                            id="apiKey"
                                            type={showApiKey ? 'text' : 'password'}
                                            value={config.apiKey}
                                            onChange={(e) => setConfig(prev => ({ ...prev, apiKey: e.target.value }))}
                                            placeholder="Enter your API key"
                                        />
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="sm"
                                            className="absolute right-0 top-0 h-full px-3"
                                            onClick={() => setShowApiKey(!showApiKey)}
                                        >
                                            {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                        </Button>
                                    </div>
                                </div>

                                {config.type === 'CUSTOM' && (
                                    <div>
                                        <Label htmlFor="baseUrl">Base URL (Optional)</Label>
                                        <Input
                                            id="baseUrl"
                                            value={config.baseUrl}
                                            onChange={(e) => setConfig(prev => ({ ...prev, baseUrl: e.target.value }))}
                                            placeholder="https://api.custom-provider.com"
                                        />
                                    </div>
                                )}

                                <div>
                                    <Label htmlFor="timeout">Timeout (ms)</Label>
                                    <Input
                                        id="timeout"
                                        type="number"
                                        value={config.timeout}
                                        onChange={(e) => setConfig(prev => ({ ...prev, timeout: parseInt(e.target.value) }))}
                                        placeholder="30000"
                                    />
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Switch
                                        id="isActive"
                                        checked={config.isActive}
                                        onCheckedChange={(checked) => setConfig(prev => ({ ...prev, isActive: checked }))}
                                    />
                                    <Label htmlFor="isActive">Active Provider</Label>
                                </div>
                            </div>

                            <Separator />

                            <div>
                                <h4 className="font-medium text-gray-900 mb-3">Setup Steps</h4>
                                <div className="space-y-2">
                                    {selectedTemplate.setupSteps.map((step, index) => (
                                        <div key={index} className="flex items-center gap-3">
                                            <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                                                <span className="text-xs font-medium text-blue-600">{index + 1}</span>
                                            </div>
                                            <span className="text-sm text-gray-700">{step}</span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </>
                    )}
                </TabsContent>

                <TabsContent value="test" className="space-y-6">
                    <div className="space-y-4">
                        <Alert>
                            <TestTube className="h-4 w-4" />
                            <AlertDescription>
                                Test your provider configuration before saving to ensure everything works correctly.
                            </AlertDescription>
                        </Alert>

                        <div className="flex gap-3">
                            <Button
                                onClick={handleTestConnection}
                                disabled={isTesting || !config.apiKey}
                                className="flex-1"
                            >
                                {isTesting ? (
                                    <>
                                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                        Testing Connection...
                                    </>
                                ) : (
                                    <>
                                        <TestTube className="h-4 w-4 mr-2" />
                                        Test Connection
                                    </>
                                )}
                            </Button>
                            <Button
                                onClick={handleSave}
                                disabled={!config.name || !config.apiKey}
                                className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                            >
                                <CheckCircle className="h-4 w-4 mr-2" />
                                Save Provider
                            </Button>
                        </div>

                        {testResult && (
                            <div className={`p-4 rounded-lg border ${testResult.success
                                ? 'border-green-200 bg-green-50'
                                : 'border-red-200 bg-red-50'
                                }`}>
                                <div className="flex items-center gap-2 mb-2">
                                    {testResult.success ? (
                                        <CheckCircle className="h-4 w-4 text-green-600" />
                                    ) : (
                                        <AlertCircle className="h-4 w-4 text-red-600" />
                                    )}
                                    <span className={`font-medium ${testResult.success ? 'text-green-800' : 'text-red-800'
                                        }`}>
                                        {testResult.success ? 'Connection Successful' : 'Connection Failed'}
                                    </span>
                                </div>
                                <p className={`text-sm ${testResult.success ? 'text-green-700' : 'text-red-700'
                                    }`}>
                                    {testResult.success
                                        ? 'Your provider is configured correctly and ready to use.'
                                        : testResult.error || 'Failed to connect to the provider.'
                                    }
                                </p>
                                {testResult.success && testResult.data && (
                                    <div className="mt-3 text-sm text-green-700">
                                        <div>Provider: {testResult.data.provider}</div>
                                        <div>Models: {testResult.data.models?.length || 0} available</div>
                                        <div>Latency: {testResult.data.latency}ms</div>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                </TabsContent>
            </Tabs>
        </DialogContent>
    </Dialog>
);
} 