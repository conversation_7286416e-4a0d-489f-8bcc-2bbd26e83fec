import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToOne, BaseEntity } from 'typeorm';
import { User } from './user.entity';
import { Agent } from './agent.entity';
import { Tool } from './tool.entity';
import { Session } from './session.entity';
import { Tenant } from '../../tenants/entities/tenant.entity';

@Entity('organizations')
export class Organization extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ unique: true })
  name!: string;

  @Column({ unique: true })
  slug!: string;

  @Column({ type: 'jsonb', default: {} })
  settings!: Record<string, any>;

  @Column({ type: 'jsonb', default: {} })
  quotas!: {
    agents: number;
    tools: number;
    executions: number;
    storage: number;
  };

  @Column({ type: 'jsonb', default: {} })
  billing!: {
    plan: string;
    status: string;
    usage: Record<string, number>;
  };

  @Column({ default: true })
  isActive!: boolean;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  @OneToMany(() => User, user => user.organization)
  users!: User[];

  @OneToMany(() => Agent, agent => agent.organization)
  agents!: Agent[];

  @OneToMany(() => Tool, tool => tool.organization)
  tools!: Tool[];

  @OneToMany(() => Session, session => session.organization)
  sessions!: Session[];

  @ManyToOne(() => Tenant, tenant => tenant.organizations)
  tenant!: Tenant;

}