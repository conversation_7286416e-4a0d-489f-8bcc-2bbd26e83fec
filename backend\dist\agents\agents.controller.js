"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const agent_orchestrator_service_1 = require("./agent-orchestrator.service");
const agent_dto_1 = require("./dto/agent.dto");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const permission_guard_1 = require("../auth/permission.guard");
const permissions_decorator_1 = require("../auth/permissions.decorator");
let AgentsController = class AgentsController {
    constructor(agentOrchestratorService) {
        this.agentOrchestratorService = agentOrchestratorService;
    }
    async createAgent(createAgentDto, req) {
        return this.agentOrchestratorService.createAgent(createAgentDto, req.user.organizationId, req.user.id);
    }
    async createAgentFromWizard(wizardData, req) {
        return this.agentOrchestratorService.createAgentFromWizard(wizardData, req.user.organizationId, req.user.id);
    }
    async getAgentWizardSteps() {
        return this.agentOrchestratorService.getAgentWizardSteps();
    }
    async getAgents(req) {
        return this.agentOrchestratorService.getAgentsByOrganization(req.user.organizationId);
    }
    async getAgent(id, req) {
        return this.agentOrchestratorService.getAgentById(id, req.user.organizationId);
    }
    async updateAgent(id, updateAgentDto, req) {
        return this.agentOrchestratorService.updateAgent(id, updateAgentDto, req.user.organizationId);
    }
    async deleteAgent(id, req) {
        return this.agentOrchestratorService.deleteAgent(id, req.user.organizationId);
    }
    async executeAgent(id, executeDto, req) {
        return this.agentOrchestratorService.executeAgent(id, executeDto, req.user.organizationId);
    }
    async testAgent(id, body, req) {
        return this.agentOrchestratorService.testAgent(id, body.message, req.user.organizationId);
    }
    async duplicateAgent(id, req) {
        return this.agentOrchestratorService.duplicateAgent(id, req.user.organizationId, req.user.id);
    }
    async getAgentExecutions(id, req) {
        return this.agentOrchestratorService.getAgentExecutions(id, req.user.organizationId);
    }
    async getAgentAnalytics(id, req) {
        return this.agentOrchestratorService.getAgentAnalytics(id, req.user.organizationId);
    }
    async createCollaboration(body, req) {
        return this.agentOrchestratorService.createCollaboration(body.name, body.agentIds, body.coordinatorId, body.workflow, req.user.organizationId, req.user.id);
    }
    async executeCollaboration(id, body, req) {
        return this.agentOrchestratorService.executeCollaboration(id, body.input, req.user.organizationId);
    }
    async getCollaborations(req) {
        return this.agentOrchestratorService.getCollaborationsByOrganization(req.user.organizationId);
    }
    async getAgentsOverview(req) {
        const agents = await this.agentOrchestratorService.getAgentsByOrganization(req.user.organizationId);
        const stats = {
            totalAgents: agents.length,
            activeAgents: agents.filter(a => a.status === 'ACTIVE').length,
            inactiveAgents: agents.filter(a => a.status === 'INACTIVE').length,
            errorAgents: agents.filter(a => a.status === 'ERROR').length,
            totalExecutions: agents.reduce((sum, agent) => { var _a; return sum + (((_a = agent.performanceMetrics) === null || _a === void 0 ? void 0 : _a.totalExecutions) || 0); }, 0),
            averageSuccessRate: agents.length > 0
                ? agents.reduce((sum, agent) => { var _a; return sum + (((_a = agent.performanceMetrics) === null || _a === void 0 ? void 0 : _a.successRate) || 0); }, 0) / agents.length
                : 0,
            averageResponseTime: agents.length > 0
                ? agents.reduce((sum, agent) => { var _a; return sum + (((_a = agent.performanceMetrics) === null || _a === void 0 ? void 0 : _a.averageResponseTime) || 0); }, 0) / agents.length
                : 0,
            agentTypes: agents.reduce((acc, agent) => {
                acc[agent.type] = (acc[agent.type] || 0) + 1;
                return acc;
            }, {}),
            providers: agents.reduce((acc, agent) => {
                acc[agent.primaryProvider] = (acc[agent.primaryProvider] || 0) + 1;
                return acc;
            }, {}),
        };
        return stats;
    }
    async getAgentsPerformance(req) {
        const agents = await this.agentOrchestratorService.getAgentsByOrganization(req.user.organizationId);
        const performanceStats = agents.map(agent => {
            var _a, _b, _c, _d;
            return ({
                id: agent.id,
                name: agent.name,
                type: agent.type,
                status: agent.status,
                totalExecutions: ((_a = agent.performanceMetrics) === null || _a === void 0 ? void 0 : _a.totalExecutions) || 0,
                successRate: ((_b = agent.performanceMetrics) === null || _b === void 0 ? void 0 : _b.successRate) || 0,
                averageResponseTime: ((_c = agent.performanceMetrics) === null || _c === void 0 ? void 0 : _c.averageResponseTime) || 0,
                lastExecuted: (_d = agent.performanceMetrics) === null || _d === void 0 ? void 0 : _d.lastExecuted,
                primaryProvider: agent.primaryProvider,
            });
        });
        return {
            agents: performanceStats,
            summary: {
                totalExecutions: performanceStats.reduce((sum, agent) => sum + agent.totalExecutions, 0),
                averageSuccessRate: performanceStats.length > 0
                    ? performanceStats.reduce((sum, agent) => sum + agent.successRate, 0) / performanceStats.length
                    : 0,
                averageResponseTime: performanceStats.length > 0
                    ? performanceStats.reduce((sum, agent) => sum + agent.averageResponseTime, 0) / performanceStats.length
                    : 0,
            },
        };
    }
    async searchAgents(query, type, status, provider, req) {
        const agents = await this.agentOrchestratorService.getAgentsByOrganization(req.user.organizationId);
        let filteredAgents = agents;
        if (query) {
            const searchTerm = query.toLowerCase();
            filteredAgents = filteredAgents.filter(agent => agent.name.toLowerCase().includes(searchTerm) ||
                agent.type.toLowerCase().includes(searchTerm) ||
                agent.primaryProvider.toLowerCase().includes(searchTerm));
        }
        if (type) {
            filteredAgents = filteredAgents.filter(agent => agent.type === type);
        }
        if (status) {
            filteredAgents = filteredAgents.filter(agent => agent.status === status);
        }
        if (provider) {
            filteredAgents = filteredAgents.filter(agent => agent.primaryProvider === provider);
        }
        return filteredAgents;
    }
    async enableAgent(id, req) {
        return this.agentOrchestratorService.updateAgent(id, { status: 'ACTIVE' }, req.user.organizationId);
    }
    async disableAgent(id, req) {
        return this.agentOrchestratorService.updateAgent(id, { status: 'INACTIVE' }, req.user.organizationId);
    }
    async previewTemplate(templateId, req) {
        return {
            templateId,
            preview: {
                name: 'Template Preview',
                description: 'This is a preview of the selected template',
                capabilities: ['chat', 'analysis'],
                estimatedCost: '$0.01 per message',
                setupTime: '2-3 minutes',
            },
        };
    }
    async bulkActions(body, req) {
        const { agentIds, action } = body;
        const results = [];
        for (const agentId of agentIds) {
            try {
                switch (action) {
                    case 'enable':
                        await this.agentOrchestratorService.updateAgent(agentId, { status: 'ACTIVE' }, req.user.organizationId);
                        break;
                    case 'disable':
                        await this.agentOrchestratorService.updateAgent(agentId, { status: 'INACTIVE' }, req.user.organizationId);
                        break;
                    case 'delete':
                        await this.agentOrchestratorService.deleteAgent(agentId, req.user.organizationId);
                        break;
                }
                results.push({ agentId, success: true });
            }
            catch (error) {
                results.push({ agentId, success: false, error: error.message });
            }
        }
        return {
            action,
            totalAgents: agentIds.length,
            successful: results.filter(r => r.success).length,
            failed: results.filter(r => !r.success).length,
            results,
        };
    }
};
exports.AgentsController = AgentsController;
__decorate([
    (0, common_1.Post)(),
    (0, permissions_decorator_1.RequirePermissions)(['agents:create']),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new agent' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Agent created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input data' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Insufficient permissions' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [agent_dto_1.CreateAgentInstanceDto, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "createAgent", null);
__decorate([
    (0, common_1.Post)('wizard'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:create']),
    (0, swagger_1.ApiOperation)({ summary: 'Create agent using wizard' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Agent created successfully from wizard' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid wizard data' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "createAgentFromWizard", null);
__decorate([
    (0, common_1.Get)('wizard/steps'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:read']),
    (0, swagger_1.ApiOperation)({ summary: 'Get agent wizard steps' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Wizard steps retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "getAgentWizardSteps", null);
__decorate([
    (0, common_1.Get)(),
    (0, permissions_decorator_1.RequirePermissions)(['agents:read']),
    (0, swagger_1.ApiOperation)({ summary: 'Get all agents for organization' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Agents retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "getAgents", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:read']),
    (0, swagger_1.ApiOperation)({ summary: 'Get agent by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Agent retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Agent not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "getAgent", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:update']),
    (0, swagger_1.ApiOperation)({ summary: 'Update agent' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Agent updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Agent not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, agent_dto_1.UpdateAgentInstanceDto, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "updateAgent", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:delete']),
    (0, swagger_1.ApiOperation)({ summary: 'Delete agent' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Agent deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Agent not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "deleteAgent", null);
__decorate([
    (0, common_1.Post)(':id/execute'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:execute']),
    (0, swagger_1.ApiOperation)({ summary: 'Execute agent' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Agent executed successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Agent not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, agent_dto_1.ExecuteAgentDto, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "executeAgent", null);
__decorate([
    (0, common_1.Post)(':id/test'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:execute']),
    (0, swagger_1.ApiOperation)({ summary: 'Test agent without saving execution' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Agent test completed' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Agent not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "testAgent", null);
__decorate([
    (0, common_1.Post)(':id/duplicate'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:create']),
    (0, swagger_1.ApiOperation)({ summary: 'Duplicate agent' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Agent duplicated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Agent not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "duplicateAgent", null);
__decorate([
    (0, common_1.Get)(':id/executions'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:read']),
    (0, swagger_1.ApiOperation)({ summary: 'Get agent executions' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Executions retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Agent not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "getAgentExecutions", null);
__decorate([
    (0, common_1.Get)(':id/analytics'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:read']),
    (0, swagger_1.ApiOperation)({ summary: 'Get agent analytics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Analytics retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Agent not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "getAgentAnalytics", null);
__decorate([
    (0, common_1.Post)('collaborations'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:create']),
    (0, swagger_1.ApiOperation)({ summary: 'Create agent collaboration' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Collaboration created successfully' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "createCollaboration", null);
__decorate([
    (0, common_1.Post)('collaborations/:id/execute'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:execute']),
    (0, swagger_1.ApiOperation)({ summary: 'Execute agent collaboration' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Collaboration executed successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Collaboration not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "executeCollaboration", null);
__decorate([
    (0, common_1.Get)('collaborations'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:read']),
    (0, swagger_1.ApiOperation)({ summary: 'Get agent collaborations' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Collaborations retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "getCollaborations", null);
__decorate([
    (0, common_1.Get)('stats/overview'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:read']),
    (0, swagger_1.ApiOperation)({ summary: 'Get agents overview statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Statistics retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "getAgentsOverview", null);
__decorate([
    (0, common_1.Get)('stats/performance'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:read']),
    (0, swagger_1.ApiOperation)({ summary: 'Get agents performance statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Performance statistics retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "getAgentsPerformance", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:read']),
    (0, swagger_1.ApiOperation)({ summary: 'Search agents' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Search results retrieved successfully' }),
    __param(0, (0, common_1.Query)('q')),
    __param(1, (0, common_1.Query)('type')),
    __param(2, (0, common_1.Query)('status')),
    __param(3, (0, common_1.Query)('provider')),
    __param(4, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "searchAgents", null);
__decorate([
    (0, common_1.Post)(':id/enable'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:update']),
    (0, swagger_1.ApiOperation)({ summary: 'Enable agent' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Agent enabled successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Agent not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "enableAgent", null);
__decorate([
    (0, common_1.Post)(':id/disable'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:update']),
    (0, swagger_1.ApiOperation)({ summary: 'Disable agent' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Agent disabled successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Agent not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "disableAgent", null);
__decorate([
    (0, common_1.Get)('templates/:templateId/preview'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:read']),
    (0, swagger_1.ApiOperation)({ summary: 'Preview agent template' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Template preview retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Template not found' }),
    __param(0, (0, common_1.Param)('templateId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "previewTemplate", null);
__decorate([
    (0, common_1.Post)('bulk/actions'),
    (0, permissions_decorator_1.RequirePermissions)(['agents:update']),
    (0, swagger_1.ApiOperation)({ summary: 'Perform bulk actions on agents' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Bulk actions completed successfully' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AgentsController.prototype, "bulkActions", null);
exports.AgentsController = AgentsController = __decorate([
    (0, swagger_1.ApiTags)('Agents'),
    (0, common_1.Controller)('agents'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, permission_guard_1.PermissionGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [agent_orchestrator_service_1.AgentOrchestratorService])
], AgentsController);
//# sourceMappingURL=agents.controller.js.map