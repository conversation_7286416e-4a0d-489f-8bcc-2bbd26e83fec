import { Injectable, OnM<PERSON>uleD<PERSON>roy, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';
import { Logger } from '@nestjs/common';

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
    private readonly logger = new Logger(RedisService.name);
    private redisClient!: Redis;
    private redisSubscriber!: Redis;

    constructor(private configService: ConfigService) { }

    async onModuleInit() {
        this.redisClient = new Redis({
            host: this.configService.get('app.redis.host'),
            port: this.configService.get('app.redis.port'),
            password: this.configService.get('app.redis.password'),
            db: this.configService.get('app.redis.db'),
            maxRetriesPerRequest: 3,
            lazyConnect: true,
            keepAlive: 30000,
            family: 4,
        });

        this.redisSubscriber = new Redis({
            host: this.configService.get('app.redis.host'),
            port: this.configService.get('app.redis.port'),
            password: this.configService.get('app.redis.password'),
            db: this.configService.get('app.redis.db'),
            maxRetriesPerRequest: 3,
            lazyConnect: true,
            keepAlive: 30000,
            family: 4,
        });

        this.redisClient.on('connect', () => {
            this.logger.log('Redis client connected');
        });

        this.redisClient.on('error', (error) => {
            this.logger.error('Redis client error:', error);
        });

        this.redisSubscriber.on('connect', () => {
            this.logger.log('Redis subscriber connected');
        });

        this.redisSubscriber.on('error', (error) => {
            this.logger.error('Redis subscriber error:', error);
        });

        await this.redisClient.connect();
        await this.redisSubscriber.connect();
    }

    async onModuleDestroy() {
        if (this.redisClient) {
            await this.redisClient.quit();
        }
        if (this.redisSubscriber) {
            await this.redisSubscriber.quit();
        }
    }

    getClient(): Redis {
        return this.redisClient;
    }

    getSubscriber(): Redis {
        return this.redisSubscriber;
    }

    async set(key: string, value: string, ttl?: number): Promise<void> {
        const prefix = this.configService.get('app.redis.prefix');
        const fullKey = `${prefix}${key}`;

        if (ttl) {
            await this.redisClient.setex(fullKey, ttl, value);
        } else {
            await this.redisClient.set(fullKey, value);
        }
    }

    async get(key: string): Promise<string | null> {
        const prefix = this.configService.get('app.redis.prefix');
        const fullKey = `${prefix}${key}`;
        return await this.redisClient.get(fullKey);
    }

    async del(key: string): Promise<void> {
        const prefix = this.configService.get('app.redis.prefix');
        const fullKey = `${prefix}${key}`;
        await this.redisClient.del(fullKey);
    }

    async exists(key: string): Promise<boolean> {
        const prefix = this.configService.get('app.redis.prefix');
        const fullKey = `${prefix}${key}`;
        const result = await this.redisClient.exists(fullKey);
        return result === 1;
    }

    async expire(key: string, ttl: number): Promise<void> {
        const prefix = this.configService.get('app.redis.prefix');
        const fullKey = `${prefix}${key}`;
        await this.redisClient.expire(fullKey, ttl);
    }

    async hset(key: string, field: string, value: string): Promise<void> {
        const prefix = this.configService.get('app.redis.prefix');
        const fullKey = `${prefix}${key}`;
        await this.redisClient.hset(fullKey, field, value);
    }

    async hget(key: string, field: string): Promise<string | null> {
        const prefix = this.configService.get('app.redis.prefix');
        const fullKey = `${prefix}${key}`;
        return await this.redisClient.hget(fullKey, field);
    }

    async hgetall(key: string): Promise<Record<string, string>> {
        const prefix = this.configService.get('app.redis.prefix');
        const fullKey = `${prefix}${key}`;
        return await this.redisClient.hgetall(fullKey);
    }

    async hdel(key: string, field: string): Promise<void> {
        const prefix = this.configService.get('app.redis.prefix');
        const fullKey = `${prefix}${key}`;
        await this.redisClient.hdel(fullKey, field);
    }

    async publish(channel: string, message: string): Promise<void> {
        await this.redisClient.publish(channel, message);
    }

    async subscribe(channel: string, callback: (message: string) => void): Promise<void> {
        await this.redisSubscriber.subscribe(channel);
        this.redisSubscriber.on('message', (receivedChannel, message) => {
            if (receivedChannel === channel) {
                callback(message);
            }
        });
    }

    async unsubscribe(channel: string): Promise<void> {
        await this.redisSubscriber.unsubscribe(channel);
    }

    async flushdb(): Promise<void> {
        await this.redisClient.flushdb();
    }

    async ping(): Promise<string> {
        return await this.redisClient.ping();
    }

    async info(): Promise<string> {
        return await this.redisClient.info();
    }
} 