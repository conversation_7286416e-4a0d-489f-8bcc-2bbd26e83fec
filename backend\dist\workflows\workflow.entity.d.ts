import { BaseEntity } from 'typeorm';
import { User } from '../users/user.entity';
import { Agent } from '../agents/agent.entity';
export declare enum WorkflowStatus {
    DRAFT = "draft",
    ACTIVE = "active",
    PAUSED = "paused",
    COMPLETED = "completed",
    FAILED = "failed",
    CANCELLED = "cancelled"
}
export declare enum WorkflowTriggerType {
    MANUAL = "manual",
    SCHEDULED = "scheduled",
    WEBHOOK = "webhook",
    EVENT = "event",
    API = "api"
}
export declare class Workflow extends BaseEntity {
    id: string;
    name: string;
    description: string;
    definition: {
        version: string;
        steps: Array<{
            id: string;
            type: string;
            name: string;
            config: Record<string, any>;
            dependencies?: string[];
            conditions?: Record<string, any>;
        }>;
        triggers: Array<{
            type: WorkflowTriggerType;
            config: Record<string, any>;
        }>;
        variables: Record<string, any>;
        errorHandling: {
            retryCount: number;
            retryDelay: number;
            fallbackSteps?: string[];
        };
    };
    status: WorkflowStatus;
    executionHistory: Array<{
        stepId: string;
        status: 'pending' | 'running' | 'completed' | 'failed';
        startTime: Date;
        endTime?: Date;
        result?: any;
        error?: string;
    }>;
    metadata: {
        tags: string[];
        category: string;
        priority: 'low' | 'medium' | 'high' | 'critical';
        estimatedDuration: number;
        costEstimate: number;
    };
    permissions: {
        owners: string[];
        editors: string[];
        viewers: string[];
        public: boolean;
    };
    lastExecutedAt: Date;
    nextExecutionAt: Date;
    executionCount: number;
    successCount: number;
    failureCount: number;
    averageExecutionTime: number;
    totalCost: number;
    user: User;
    userId: string;
    organizationId: string;
    agents: Agent[];
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date;
}
