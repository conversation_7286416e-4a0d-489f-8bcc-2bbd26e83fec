"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AgentOrchestratorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentOrchestratorService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const agent_entity_1 = require("../database/entities/agent.entity");
const agent_execution_entity_1 = require("../database/entities/agent-execution.entity");
const session_memory_service_1 = require("./session-memory.service");
const agent_analytics_service_1 = require("./agent-analytics.service");
const apix_gateway_1 = require("../websocket/apix.gateway");
const ai_provider_integration_service_1 = require("../providers/ai-provider-integration.service");
const ai_provider_selector_service_1 = require("../providers/ai-provider-selector.service");
let AgentOrchestratorService = AgentOrchestratorService_1 = class AgentOrchestratorService {
    constructor(agentRepository, executionRepository, collaborationRepository, sessionMemoryService, analyticsService, apixGateway, aiProviderIntegration, aiProviderSelector) {
        this.agentRepository = agentRepository;
        this.executionRepository = executionRepository;
        this.collaborationRepository = collaborationRepository;
        this.sessionMemoryService = sessionMemoryService;
        this.analyticsService = analyticsService;
        this.apixGateway = apixGateway;
        this.aiProviderIntegration = aiProviderIntegration;
        this.aiProviderSelector = aiProviderSelector;
        this.logger = new common_1.Logger(AgentOrchestratorService_1.name);
    }
    async createAgent(createAgentDto, organizationId, userId) {
        try {
            await this.validateAgentConfiguration(createAgentDto);
            const agent = this.agentRepository.create(Object.assign(Object.assign({}, createAgentDto), { organizationId, createdBy: userId, performanceMetrics: {
                    totalExecutions: 0,
                    successRate: 0,
                    averageResponseTime: 0,
                    lastExecuted: new Date(),
                } }));
            const savedAgent = await this.agentRepository.save(agent);
            this.apixGateway.emitToOrganization(organizationId, 'agent_created', {
                agentId: savedAgent.id,
                name: savedAgent.name,
                type: savedAgent.type,
                timestamp: new Date(),
            });
            this.logger.log(`Agent created: ${savedAgent.id} for organization: ${organizationId}`);
            return savedAgent;
        }
        catch (error) {
            this.logger.error(`Failed to create agent: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async createAgentFromWizard(wizardData, organizationId, userId) {
        try {
            await this.validateWizardData(wizardData);
            const createAgentDto = {
                templateId: wizardData.templateId,
                name: wizardData.name,
                type: wizardData.type,
                primaryProvider: wizardData.primaryProvider,
                fallbackProviders: wizardData.fallbackProviders,
                config: wizardData.config,
                memoryConfig: wizardData.memoryConfig,
                skills: wizardData.config.skills,
            };
            return await this.createAgent(createAgentDto, organizationId, userId);
        }
        catch (error) {
            this.logger.error(`Failed to create agent from wizard: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async getAgentWizardSteps() {
        return [
            {
                step: 1,
                title: 'Basic Information',
                description: 'Set your agent\'s name and basic details',
                fields: ['name', 'description'],
                isCompleted: false,
                validation: {
                    name: { required: true, minLength: 2, maxLength: 100 },
                    description: { required: true, maxLength: 500 },
                },
            },
            {
                step: 2,
                title: 'Template Selection',
                description: 'Choose a template or start from scratch',
                fields: ['templateId', 'type'],
                isCompleted: false,
                validation: {
                    templateId: { required: false },
                    type: { required: true },
                },
            },
            {
                step: 3,
                title: 'Personality & Style',
                description: 'Define your agent\'s personality and response style',
                fields: ['personality', 'responseStyle', 'systemPrompt'],
                isCompleted: false,
                validation: {
                    personality: { required: true },
                    responseStyle: { required: true },
                    systemPrompt: { required: true, minLength: 10 },
                },
            },
            {
                step: 4,
                title: 'AI Provider',
                description: 'Select your preferred AI provider and models',
                fields: ['primaryProvider', 'fallbackProviders'],
                isCompleted: false,
                validation: {
                    primaryProvider: { required: true },
                },
            },
            {
                step: 5,
                title: 'Capabilities',
                description: 'Choose what your agent can do',
                fields: ['capabilities', 'skills'],
                isCompleted: false,
                validation: {
                    capabilities: { required: true, minLength: 1 },
                },
            },
            {
                step: 6,
                title: 'Memory Settings',
                description: 'Configure how your agent remembers conversations',
                fields: ['memoryConfig'],
                isCompleted: false,
                validation: {
                    memoryConfig: { required: true },
                },
            },
            {
                step: 7,
                title: 'Test & Validate',
                description: 'Test your agent with sample conversations',
                fields: ['testMessages'],
                isCompleted: false,
                validation: {
                    testMessages: { required: false },
                },
            },
        ];
    }
    async validateWizardData(wizardData) {
        var _a, _b;
        const errors = [];
        if (!((_a = wizardData.name) === null || _a === void 0 ? void 0 : _a.trim())) {
            errors.push('Agent name is required');
        }
        if (!wizardData.type) {
            errors.push('Agent type is required');
        }
        if (!wizardData.primaryProvider) {
            errors.push('Primary provider is required');
        }
        if (!((_b = wizardData.config.systemPrompt) === null || _b === void 0 ? void 0 : _b.trim())) {
            errors.push('System prompt is required');
        }
        if (wizardData.config.capabilities.length === 0) {
            errors.push('At least one capability must be selected');
        }
        if (errors.length > 0) {
            throw new common_1.BadRequestException(`Validation failed: ${errors.join(', ')}`);
        }
    }
    async validateAgentConfiguration(createAgentDto) {
        var _a, _b, _c;
        const errors = [];
        if (!((_a = createAgentDto.name) === null || _a === void 0 ? void 0 : _a.trim())) {
            errors.push('Agent name is required');
        }
        if (!createAgentDto.type) {
            errors.push('Agent type is required');
        }
        if (!createAgentDto.primaryProvider) {
            errors.push('Primary provider is required');
        }
        if (!((_c = (_b = createAgentDto.config) === null || _b === void 0 ? void 0 : _b.systemPrompt) === null || _c === void 0 ? void 0 : _c.trim())) {
            errors.push('System prompt is required');
        }
        if (errors.length > 0) {
            throw new common_1.BadRequestException(`Validation failed: ${errors.join(', ')}`);
        }
    }
    async updateAgent(agentId, updateAgentDto, organizationId) {
        try {
            const agent = await this.getAgentById(agentId, organizationId);
            if (updateAgentDto.config) {
                await this.validateAgentConfiguration(Object.assign(Object.assign({}, agent), { config: updateAgentDto.config }));
            }
            Object.assign(agent, updateAgentDto);
            const updatedAgent = await this.agentRepository.save(agent);
            this.apixGateway.emitToOrganization(organizationId, 'agent_updated', {
                agentId: updatedAgent.id,
                changes: updateAgentDto,
                timestamp: new Date(),
            });
            return updatedAgent;
        }
        catch (error) {
            this.logger.error(`Failed to update agent ${agentId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async deleteAgent(agentId, organizationId) {
        try {
            const agent = await this.getAgentById(agentId, organizationId);
            agent.status = agent_entity_1.AgentStatus.INACTIVE;
            await this.agentRepository.save(agent);
            this.apixGateway.emitToOrganization(organizationId, 'agent_deleted', {
                agentId,
                timestamp: new Date(),
            });
        }
        catch (error) {
            this.logger.error(`Failed to delete agent ${agentId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async executeAgent(agentId, executeDto, organizationId) {
        try {
            const agent = await this.getAgentById(agentId, organizationId);
            if (agent.status !== agent_entity_1.AgentStatus.ACTIVE) {
                throw new common_1.BadRequestException(`Agent is not active. Current status: ${agent.status}`);
            }
            const startTime = Date.now();
            let execution;
            try {
                const sessionId = executeDto.sessionId || `session_${Date.now()}`;
                const sessionMemory = await this.sessionMemoryService.createSession(sessionId, organizationId);
                const result = await this.executeAgentLogic(agent, executeDto.message, sessionMemory, executeDto.context);
                execution = this.executionRepository.create({
                    agentId: agent.id,
                    sessionId,
                    input: executeDto.message,
                    output: result.output,
                    status: agent_execution_entity_1.ExecutionStatus.COMPLETED,
                    duration: Date.now() - startTime,
                    cost: result.cost,
                    metadata: {
                        provider: result.provider,
                        model: result.model,
                        tokens: result.tokens,
                        retryCount: result.retryCount || 0,
                    },
                });
                await this.executionRepository.save(execution);
                await this.sessionMemoryService.addMessage(sessionId, {
                    role: 'user',
                    content: executeDto.message,
                    timestamp: new Date(),
                });
                await this.sessionMemoryService.addMessage(sessionId, {
                    role: 'assistant',
                    content: result.output,
                    timestamp: new Date(),
                });
                await this.updateAgentMetrics(agentId, execution.duration, true);
                this.apixGateway.emitToOrganization(organizationId, 'agent_executed', {
                    agentId: agent.id,
                    executionId: execution.id,
                    duration: execution.duration,
                    cost: execution.cost,
                    timestamp: new Date(),
                });
                return execution;
            }
            catch (error) {
                execution = this.executionRepository.create({
                    agentId: agent.id,
                    sessionId: executeDto.sessionId || `session_${Date.now()}`,
                    input: executeDto.message,
                    output: '',
                    status: agent_execution_entity_1.ExecutionStatus.FAILED,
                    duration: Date.now() - startTime,
                    cost: 0,
                    metadata: {
                        provider: '',
                        model: '',
                        tokens: { input: 0, output: 0, total: 0 },
                        duration: 0,
                        cost: 0,
                    },
                });
                await this.executionRepository.save(execution);
                await this.updateAgentMetrics(agentId, execution.duration, false);
                throw error;
            }
        }
        catch (error) {
            this.logger.error(`Failed to execute agent ${agentId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async testAgent(agentId, testMessage, organizationId) {
        try {
            const agent = await this.getAgentById(agentId, organizationId);
            const startTime = Date.now();
            const result = await this.executeAgentLogic(agent, testMessage, {}, { testing: true });
            return {
                success: true,
                output: result.output,
                metadata: {
                    provider: result.provider,
                    model: result.model,
                    tokens: result.tokens,
                    duration: Date.now() - startTime,
                    cost: result.cost,
                },
            };
        }
        catch (error) {
            this.logger.error(`Agent test failed for ${agentId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            return {
                success: false,
                output: '',
                metadata: {
                    provider: '',
                    model: '',
                    tokens: { input: 0, output: 0, total: 0 },
                    duration: 0,
                    cost: 0,
                },
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    async getAgentById(agentId, organizationId) {
        const agent = await this.agentRepository.findOne({
            where: { id: agentId, organizationId },
            relations: ['template', 'creator', 'organization'],
        });
        if (!agent) {
            throw new common_1.NotFoundException(`Agent with ID ${agentId} not found`);
        }
        return agent;
    }
    async getAgentsByOrganization(organizationId) {
        return await this.agentRepository.find({
            where: { organizationId },
            relations: ['template', 'creator'],
            order: { createdAt: 'DESC' },
        });
    }
    async getAgentExecutions(agentId, organizationId) {
        return await this.executionRepository.find({
            where: { agentId },
            order: { createdAt: 'DESC' },
            take: 100,
        });
    }
    async getAgentAnalytics(agentId, organizationId) {
        const agent = await this.getAgentById(agentId, organizationId);
        return await this.analyticsService.getAgentAnalytics(agent, organizationId);
    }
    async duplicateAgent(agentId, organizationId, userId) {
        try {
            const originalAgent = await this.getAgentById(agentId, organizationId);
            const duplicatedAgent = this.agentRepository.create({
                name: `${originalAgent.name} (Copy)`,
                templateId: originalAgent.templateId,
                config: originalAgent.config,
                type: originalAgent.type,
                status: agent_entity_1.AgentStatus.ACTIVE,
                primaryProvider: originalAgent.primaryProvider,
                fallbackProviders: originalAgent.fallbackProviders,
                memoryConfig: originalAgent.memoryConfig,
                skills: originalAgent.skills,
                performanceMetrics: {
                    totalExecutions: 0,
                    successRate: 0,
                    averageResponseTime: 0,
                    lastExecuted: new Date(),
                },
                organizationId,
                createdBy: userId,
            });
            const savedAgent = await this.agentRepository.save(duplicatedAgent);
            this.apixGateway.emitToOrganization(organizationId, 'agent_duplicated', {
                originalAgentId: agentId,
                newAgentId: savedAgent.id,
                timestamp: new Date(),
            });
            return savedAgent;
        }
        catch (error) {
            this.logger.error(`Failed to duplicate agent ${agentId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async executeAgentLogic(agent, message, sessionMemory, context = {}) {
        var _a;
        try {
            const selectedProvider = await this.aiProviderSelector.selectOptimalProvider({
                organizationId: agent.organizationId,
                capabilities: agent.config.capabilities,
                maxCost: agent.config.maxCost,
                maxLatency: agent.config.maxLatency,
                preferredProviders: agent.config.preferredProviders,
                excludeProviders: agent.config.excludeProviders,
            });
            const systemPrompt = this.buildSystemPrompt(((_a = agent.template) === null || _a === void 0 ? void 0 : _a.promptTemplate) || '', agent, context);
            const conversationHistory = (sessionMemory === null || sessionMemory === void 0 ? void 0 : sessionMemory.messages) || [];
            const result = await this.aiProviderIntegration.executeWithProvider(selectedProvider, {
                messages: [
                    { role: 'system', content: systemPrompt },
                    ...conversationHistory,
                    { role: 'user', content: message },
                ],
                model: agent.config.model || 'gpt-3.5-turbo',
                temperature: agent.config.temperature || 0.7,
                maxTokens: agent.config.maxTokens || 1000,
            });
            return {
                output: result.output,
                provider: selectedProvider,
                model: result.model,
                tokens: result.tokens,
                cost: result.cost,
            };
        }
        catch (error) {
            this.logger.error(`Agent execution failed: ${error.message}`, error.stack);
            throw error;
        }
    }
    buildSystemPrompt(template, agent, context) {
        let systemPrompt = template || agent.config.systemPrompt || '';
        if (agent.config.personality) {
            systemPrompt += `\n\nPersonality: ${agent.config.personality}`;
        }
        if (agent.config.responseStyle) {
            systemPrompt += `\n\nResponse Style: ${agent.config.responseStyle}`;
        }
        if (Object.keys(context).length > 0) {
            systemPrompt += `\n\nContext: ${JSON.stringify(context)}`;
        }
        return systemPrompt;
    }
    async updateAgentMetrics(agentId, duration, success) {
        try {
            const agent = await this.agentRepository.findOne({ where: { id: agentId } });
            if (!agent)
                return;
            const metrics = agent.performanceMetrics || {
                totalExecutions: 0,
                successRate: 0,
                averageResponseTime: 0,
                lastExecuted: new Date(),
            };
            metrics.totalExecutions += 1;
            metrics.lastExecuted = new Date();
            const successfulExecutions = await this.executionRepository.count({
                where: { agentId, status: agent_execution_entity_1.ExecutionStatus.COMPLETED },
            });
            metrics.successRate = successfulExecutions / metrics.totalExecutions;
            const avgTime = await this.executionRepository
                .createQueryBuilder('execution')
                .select('AVG(execution.duration)', 'avgDuration')
                .where('execution.agentId = :agentId', { agentId })
                .andWhere('execution.status = :status', { status: agent_execution_entity_1.ExecutionStatus.COMPLETED })
                .getRawOne();
            metrics.averageResponseTime = parseFloat(avgTime.avgDuration) || 0;
            agent.performanceMetrics = metrics;
            await this.agentRepository.save(agent);
        }
        catch (error) {
            this.logger.error(`Failed to update agent metrics: ${error.message}`, error.stack);
        }
    }
    async createCollaboration(name, agentIds, coordinatorId, workflow, organizationId, userId) {
        try {
            const collaboration = this.collaborationRepository.create({
                name,
                agentIds,
                coordinatorId,
                workflow,
                organizationId,
                createdBy: userId,
            });
            const savedCollaboration = await this.collaborationRepository.save(collaboration);
            this.apixGateway.emitToOrganization(organizationId, 'collaboration_created', {
                collaborationId: savedCollaboration.id,
                name: savedCollaboration.name,
                timestamp: new Date(),
            });
            return savedCollaboration;
        }
        catch (error) {
            this.logger.error(`Failed to create collaboration: ${error.message}`, error.stack);
            throw error;
        }
    }
    async executeCollaboration(collaborationId, input, organizationId) {
        try {
            const collaboration = await this.collaborationRepository.findOne({
                where: { id: collaborationId, organizationId },
            });
            if (!collaboration) {
                throw new common_1.NotFoundException(`Collaboration with ID ${collaborationId} not found`);
            }
            const results = await this.executeCollaborationWorkflow(collaboration, input, organizationId);
            const coordinatorAgent = await this.getAgentById(collaboration.coordinatorId, organizationId);
            const finalResult = await this.executeAgentLogic(coordinatorAgent, input, {}, {
                collaborationResults: results,
            });
            return {
                collaborationId,
                results,
                finalOutput: finalResult.output,
            };
        }
        catch (error) {
            this.logger.error(`Failed to execute collaboration ${collaborationId}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async executeCollaborationWorkflow(collaboration, input, organizationId) {
        const results = [];
        for (const agentId of collaboration.agentIds) {
            try {
                const agent = await this.getAgentById(agentId, organizationId);
                const result = await this.executeAgentLogic(agent, input, {}, {
                    collaboration: true,
                    workflow: collaboration.workflow,
                });
                results.push({
                    agentId,
                    output: result.output,
                    executionId: `exec_${Date.now()}_${agentId}`,
                });
            }
            catch (error) {
                this.logger.error(`Failed to execute agent ${agentId} in collaboration: ${error.message}`);
                results.push({
                    agentId,
                    output: `Error: ${error.message}`,
                    executionId: `exec_${Date.now()}_${agentId}`,
                });
            }
        }
        return results;
    }
    async getCollaborationsByOrganization(organizationId) {
        return await this.collaborationRepository.find({
            where: { organizationId },
            order: { createdAt: 'DESC' },
        });
    }
};
exports.AgentOrchestratorService = AgentOrchestratorService;
exports.AgentOrchestratorService = AgentOrchestratorService = AgentOrchestratorService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(agent_entity_1.Agent)),
    __param(1, (0, typeorm_1.InjectRepository)(agent_execution_entity_1.AgentExecution)),
    __param(2, (0, typeorm_1.InjectRepository)(agent_entity_1.AgentCollaboration)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        session_memory_service_1.SessionMemoryService,
        agent_analytics_service_1.AgentAnalyticsService,
        apix_gateway_1.ApixGateway,
        ai_provider_integration_service_1.AIProviderIntegrationService,
        ai_provider_selector_service_1.AIProviderSelectorService])
], AgentOrchestratorService);
//# sourceMappingURL=agent-orchestrator.service.js.map