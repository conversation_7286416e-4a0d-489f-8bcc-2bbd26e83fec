"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggerService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const winston = require("winston");
let LoggerService = class LoggerService {
    constructor(configService) {
        this.configService = configService;
        this.logger = winston.createLogger({
            level: this.configService.get('app.monitoring.logLevel', 'info'),
            format: winston.format.combine(winston.format.timestamp(), winston.format.errors({ stack: true }), winston.format.json()),
            defaultMeta: {
                service: 'synapseai-backend',
                environment: this.configService.get('app.server.nodeEnv'),
            },
            transports: [
                new winston.transports.Console({
                    format: winston.format.combine(winston.format.colorize(), winston.format.simple()),
                }),
            ],
        });
        if (this.configService.get('app.server.nodeEnv') === 'production') {
            this.logger.add(new winston.transports.File({
                filename: 'logs/error.log',
                level: 'error',
            }));
            this.logger.add(new winston.transports.File({
                filename: 'logs/combined.log',
            }));
        }
    }
    log(message, context) {
        this.logger.info(message, context);
    }
    error(message, trace, context) {
        this.logger.error(message, Object.assign({ trace }, context));
    }
    warn(message, context) {
        this.logger.warn(message, context);
    }
    debug(message, context) {
        this.logger.debug(message, context);
    }
    verbose(message, context) {
        this.logger.verbose(message, context);
    }
    audit(event, details, context) {
        this.logger.info('AUDIT', Object.assign(Object.assign({ event,
            details }, context), { audit: true }));
    }
    performance(operation, duration, context) {
        this.logger.info('PERFORMANCE', Object.assign(Object.assign({ operation,
            duration }, context), { performance: true }));
    }
    business(event, details, context) {
        this.logger.info('BUSINESS', Object.assign(Object.assign({ event,
            details }, context), { business: true }));
    }
    security(event, details, context) {
        this.logger.warn('SECURITY', Object.assign(Object.assign({ event,
            details }, context), { security: true }));
    }
};
exports.LoggerService = LoggerService;
exports.LoggerService = LoggerService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], LoggerService);
//# sourceMappingURL=logger.service.js.map