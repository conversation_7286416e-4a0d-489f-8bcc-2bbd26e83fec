import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { LoggerService } from '../common/services/logger.service';
import { ConfigService } from '@nestjs/config';

export interface ProviderMetrics {
  providerId: string;
  averageLatency: number;
  successRate: number;
  costPerToken: number;
  availability: number;
  lastUpdated: Date;
}

export interface RoutingRequest {
  organizationId: string;
  modelType?: string;
  maxLatency?: number;
  maxCost?: number;
  requiresStreaming?: boolean;
  priority: 'cost' | 'speed' | 'quality' | 'balanced';
}

export interface RoutingResult {
  providerId: string;
  modelId: string;
  confidence: number;
  reasoning: string;
  fallbacks: Array<{
    providerId: string;
    modelId: string;
    confidence: number;
  }>;
}

@Injectable()
export class SmartRoutingService {
  private readonly logger = new Logger(SmartRoutingService.name);
  private metricsCache = new Map<string, ProviderMetrics>();
  private cacheExpiry = 5 * 60 * 1000; // 5 minutes

  constructor(
    private prisma: PrismaService,
    private loggerService: LoggerService,
    private configService: ConfigService,
  ) {
    // Update metrics every 5 minutes
    setInterval(() => this.updateMetricsCache(), this.cacheExpiry);
    this.updateMetricsCache(); // Initial load
  }

  async selectOptimalProvider(request: RoutingRequest): Promise<RoutingResult> {
    try {
      // Get available providers for organization
      const providers = await this.getAvailableProviders(request.organizationId);
      
      if (providers.length === 0) {
        throw new Error('No providers available for organization');
      }

      // Get current metrics for all providers
      const providerMetrics = await this.getProviderMetrics(providers.map(p => p.id));

      // Score providers based on request criteria
      const scoredProviders = this.scoreProviders(providers, providerMetrics, request);

      // Sort by score (highest first)
      scoredProviders.sort((a, b) => b.score - a.score);

      const best = scoredProviders[0];
      const fallbacks = scoredProviders.slice(1, 4).map(p => ({
        providerId: p.provider.id,
        modelId: p.model.id,
        confidence: p.score,
      }));

      this.loggerService.business('provider_selected', {
        organizationId: request.organizationId,
        selectedProvider: best.provider.id,
        selectedModel: best.model.id,
        score: best.score,
        priority: request.priority,
        fallbackCount: fallbacks.length,
      });

      return {
        providerId: best.provider.id,
        modelId: best.model.id,
        confidence: best.score,
        reasoning: best.reasoning,
        fallbacks,
      };

    } catch (error) {
      this.logger.error(`Provider selection failed: ${error}`);
      throw error;
    }
  }

  private async getAvailableProviders(organizationId: string) {
    return await this.prisma.aIProvider.findMany({
      where: {
        organizationId,
        isActive: true,
      },
      include: {
        aiModels: {
          where: { isActive: true },
        },
      },
    });
  }

  private async getProviderMetrics(providerIds: string[]): Promise<Map<string, ProviderMetrics>> {
    const metrics = new Map<string, ProviderMetrics>();

    for (const providerId of providerIds) {
      // Check cache first
      const cached = this.metricsCache.get(providerId);
      if (cached && Date.now() - cached.lastUpdated.getTime() < this.cacheExpiry) {
        metrics.set(providerId, cached);
        continue;
      }

      // Calculate metrics from recent executions
      const calculated = await this.calculateProviderMetrics(providerId);
      metrics.set(providerId, calculated);
      this.metricsCache.set(providerId, calculated);
    }

    return metrics;
  }

  private async calculateProviderMetrics(providerId: string): Promise<ProviderMetrics> {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

    // Get recent executions for this provider
    const executions = await this.prisma.agentExecution.findMany({
      where: {
        provider: providerId,
        createdAt: { gte: oneHourAgo },
      },
      select: {
        duration: true,
        status: true,
        cost: true,
        tokens: true,
      },
    });

    if (executions.length === 0) {
      // Default metrics for providers with no recent data
      return {
        providerId,
        averageLatency: 2000, // 2 seconds default
        successRate: 0.95, // 95% default
        costPerToken: 0.000002, // Default cost
        availability: 1.0,
        lastUpdated: new Date(),
      };
    }

    const successful = executions.filter(e => e.status === 'COMPLETED');
    const totalTokens = executions.reduce((sum, e) => sum + (e.tokens as any)?.total || 0, 0);
    const totalCost = executions.reduce((sum, e) => sum + e.cost, 0);

    return {
      providerId,
      averageLatency: successful.reduce((sum, e) => sum + e.duration, 0) / successful.length,
      successRate: successful.length / executions.length,
      costPerToken: totalTokens > 0 ? totalCost / totalTokens : 0.000002,
      availability: this.calculateAvailability(executions),
      lastUpdated: new Date(),
    };
  }

  private calculateAvailability(executions: any[]): number {
    // Simple availability calculation based on recent failures
    const recentFailures = executions.filter(e => 
      e.status === 'FAILED' && 
      Date.now() - new Date(e.createdAt).getTime() < 15 * 60 * 1000 // Last 15 minutes
    );

    if (recentFailures.length === 0) return 1.0;
    if (recentFailures.length >= 5) return 0.5; // Degraded
    return 0.9; // Slightly degraded
  }

  private scoreProviders(providers: any[], metrics: Map<string, ProviderMetrics>, request: RoutingRequest) {
    const scored: Array<{
      provider: any;
      model: any;
      score: number;
      reasoning: string;
    }> = [];

    for (const provider of providers) {
      const providerMetrics = metrics.get(provider.id);
      if (!providerMetrics) continue;

      for (const model of provider.aiModels) {
        // Filter by requirements
        if (request.requiresStreaming && !model.capabilities?.streaming) continue;
        if (request.maxLatency && providerMetrics.averageLatency > request.maxLatency) continue;
        if (request.maxCost && providerMetrics.costPerToken > request.maxCost) continue;

        const score = this.calculateScore(providerMetrics, request, model);
        const reasoning = this.generateReasoning(providerMetrics, request, score);

        scored.push({
          provider,
          model,
          score,
          reasoning,
        });
      }
    }

    return scored;
  }

  private calculateScore(metrics: ProviderMetrics, request: RoutingRequest, model: any): number {
    let score = 0;

    // Base score from availability and success rate
    score += metrics.availability * 30;
    score += metrics.successRate * 30;

    // Priority-based scoring
    switch (request.priority) {
      case 'speed':
        score += (5000 - Math.min(metrics.averageLatency, 5000)) / 5000 * 40;
        break;
      case 'cost':
        score += (0.00001 - Math.min(metrics.costPerToken, 0.00001)) / 0.00001 * 40;
        break;
      case 'quality':
        // Quality score based on model capabilities and reputation
        score += (model.qualityScore || 70) / 100 * 40;
        break;
      case 'balanced':
        score += (5000 - Math.min(metrics.averageLatency, 5000)) / 5000 * 15;
        score += (0.00001 - Math.min(metrics.costPerToken, 0.00001)) / 0.00001 * 15;
        score += (model.qualityScore || 70) / 100 * 10;
        break;
    }

    return Math.min(score, 100); // Cap at 100
  }

  private generateReasoning(metrics: ProviderMetrics, request: RoutingRequest, score: number): string {
    const reasons: string[] = [];

    if (metrics.successRate > 0.98) reasons.push('high reliability');
    if (metrics.averageLatency < 1000) reasons.push('fast response');
    if (metrics.costPerToken < 0.000001) reasons.push('low cost');
    if (metrics.availability === 1.0) reasons.push('full availability');

    const priority = request.priority === 'balanced' ? 'balanced performance' : `optimized for ${request.priority}`;
    
    return `Selected for ${priority}. Score: ${score.toFixed(1)}. ${reasons.join(', ')}.`;
  }

  private async updateMetricsCache() {
    try {
      const providers = await this.prisma.aIProvider.findMany({
        where: { isActive: true },
        select: { id: true },
      });

      for (const provider of providers) {
        const metrics = await this.calculateProviderMetrics(provider.id);
        this.metricsCache.set(provider.id, metrics);
      }

      this.logger.log(`Updated metrics cache for ${providers.length} providers`);
    } catch (error) {
      this.logger.error(`Failed to update metrics cache: ${error}`);
    }
  }

  // Public method to get current metrics for monitoring
  async getProviderMetricsForOrganization(organizationId: string): Promise<ProviderMetrics[]> {
    const providers = await this.getAvailableProviders(organizationId);
    const metrics = await this.getProviderMetrics(providers.map(p => p.id));
    
    return Array.from(metrics.values());
  }

  // Method to manually trigger provider health check
  async checkProviderHealth(providerId: string): Promise<{
    healthy: boolean;
    latency: number;
    error?: string;
  }> {
    try {
      const startTime = Date.now();
      
      // Make a simple test request to the provider
      // This would be implemented based on each provider's health check endpoint
      
      const latency = Date.now() - startTime;
      
      return {
        healthy: true,
        latency,
      };

    } catch (error) {
      return {
        healthy: false,
        latency: -1,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}
