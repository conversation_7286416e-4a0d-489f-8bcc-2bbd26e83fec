"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiRateLimit = exports.RateLimitGuard = exports.RateLimit = exports.RATE_LIMIT_KEY = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const core_1 = require("@nestjs/core");
const ioredis_1 = require("ioredis");
exports.RATE_LIMIT_KEY = 'rate-limit';
const RateLimit = (options) => {
    return (target, propertyKey, descriptor) => {
        if (descriptor && propertyKey) {
            core_1.Reflector.createDecorator()(options)(target, propertyKey, descriptor);
        }
        else {
            core_1.Reflector.createDecorator()(options)(target);
        }
    };
};
exports.RateLimit = RateLimit;
let RateLimitGuard = class RateLimitGuard {
    constructor(reflector, configService) {
        this.reflector = reflector;
        this.configService = configService;
        const redisUrl = this.configService.get('app.redis.url');
        if (!redisUrl) {
            throw new Error('Redis URL is required for rate limiting');
        }
        this.redis = new ioredis_1.Redis(redisUrl);
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const options = this.reflector.getAllAndOverride(exports.RATE_LIMIT_KEY, [
            context.getHandler(),
            context.getClass(),
        ]) || {
            ttl: this.configService.get('app.rateLimit.ttl', 60),
            limit: this.configService.get('app.rateLimit.max', 100),
        };
        if (options.skipIf && options.skipIf(request)) {
            return true;
        }
        const key = options.keyGenerator
            ? options.keyGenerator(request)
            : this.getDefaultKey(request);
        const redisKey = `rate-limit:${key}`;
        try {
            const current = await this.redis.get(redisKey);
            const count = current ? parseInt(current, 10) : 0;
            if (count >= options.limit) {
                throw new common_1.HttpException({
                    statusCode: common_1.HttpStatus.TOO_MANY_REQUESTS,
                    message: 'Too many requests',
                    error: 'Rate limit exceeded',
                }, common_1.HttpStatus.TOO_MANY_REQUESTS);
            }
            const pipeline = this.redis.pipeline();
            pipeline.incr(redisKey);
            if (count === 0) {
                pipeline.expire(redisKey, options.ttl);
            }
            await pipeline.exec();
            return true;
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            console.error('Rate limiting error:', error);
            return true;
        }
    }
    getDefaultKey(request) {
        var _a;
        const ip = request.ip || request.connection.remoteAddress || 'unknown';
        const userId = ((_a = request.user) === null || _a === void 0 ? void 0 : _a.id) || 'anonymous';
        return `${ip}:${userId}`;
    }
};
exports.RateLimitGuard = RateLimitGuard;
exports.RateLimitGuard = RateLimitGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector,
        config_1.ConfigService])
], RateLimitGuard);
const ApiRateLimit = (options = {}) => {
    const defaultOptions = Object.assign({ ttl: 60, limit: 100 }, options);
    return (0, exports.RateLimit)(defaultOptions);
};
exports.ApiRateLimit = ApiRateLimit;
//# sourceMappingURL=rate-limit.guard.js.map