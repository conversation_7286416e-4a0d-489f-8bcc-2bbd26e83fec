"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AgentRuntimeService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentRuntimeService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const prisma_service_1 = require("../../prisma/prisma.service");
const logger_service_1 = require("../../common/services/logger.service");
let AgentRuntimeService = AgentRuntimeService_1 = class AgentRuntimeService {
    constructor(prisma, configService, loggerService) {
        this.prisma = prisma;
        this.configService = configService;
        this.loggerService = loggerService;
        this.logger = new common_1.Logger(AgentRuntimeService_1.name);
    }
    async executeAgent(context) {
        const startTime = Date.now();
        try {
            const agent = await this.loadAgent(context.agentId);
            if (!agent) {
                throw new Error(`Agent ${context.agentId} not found`);
            }
            const executionContext = await this.prepareExecutionContext(agent, context);
            const memory = await this.loadAgentMemory(context.agentId, context.sessionId);
            const result = await this.runAgentLogic(agent, executionContext, memory);
            await this.updateAgentMemory(context.agentId, context.sessionId, {
                input: context.input,
                output: result.output,
                context: executionContext.variables || {},
            });
            const duration = Date.now() - startTime;
            await this.logExecution(context.agentId, context, result, duration);
            return Object.assign(Object.assign({}, result), { duration });
        }
        catch (error) {
            const duration = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.loggerService.error(`Agent execution failed: ${errorMessage}`, {
                agentId: context.agentId,
                sessionId: context.sessionId,
                error: errorMessage,
                duration,
            });
            return {
                success: false,
                output: `Error: ${errorMessage}`,
                tokens: { input: 0, output: 0, total: 0 },
                cost: 0,
                duration,
                provider: 'unknown',
                model: 'unknown',
                error: errorMessage,
            };
        }
    }
    async loadAgent(agentId) {
        return await this.prisma.agent.findUnique({
            where: { id: agentId },
            include: {
                template: true,
            },
        });
    }
    async prepareExecutionContext(agent, context) {
        var _a, _b;
        const baseConfig = ((_a = agent.template) === null || _a === void 0 ? void 0 : _a.config) || agent.config || {};
        return {
            agentId: agent.id,
            name: agent.name,
            type: agent.type,
            provider: agent.primaryProvider,
            model: baseConfig.model || 'gpt-3.5-turbo',
            temperature: baseConfig.temperature || 0.7,
            maxTokens: baseConfig.maxTokens || 1000,
            promptTemplate: ((_b = agent.template) === null || _b === void 0 ? void 0 : _b.promptTemplate) || agent.promptTemplate,
            variables: Object.assign(Object.assign(Object.assign({}, baseConfig.variables), context.variables), { input: context.input, agent_name: agent.name, timestamp: new Date().toISOString() }),
            tools: context.tools || [],
        };
    }
    async loadAgentMemory(agentId, sessionId) {
        if (!sessionId) {
            return {
                shortTerm: [],
                longTerm: [],
                context: {},
            };
        }
        try {
            const session = await this.prisma.agentSession.findUnique({
                where: { id: sessionId },
                include: {
                    messages: {
                        orderBy: { createdAt: 'desc' },
                        take: 10,
                    },
                },
            });
            if (!session) {
                return {
                    shortTerm: [],
                    longTerm: [],
                    context: {},
                };
            }
            return {
                shortTerm: session.messages.map(msg => ({
                    role: msg.role,
                    content: msg.content,
                    timestamp: msg.createdAt,
                })),
                longTerm: session.longTermMemory || [],
                context: session.context || {},
            };
        }
        catch (error) {
            this.logger.warn(`Failed to load memory for agent ${agentId}: ${error}`);
            return {
                shortTerm: [],
                longTerm: [],
                context: {},
            };
        }
    }
    async runAgentLogic(agent, context, memory) {
        const prompt = this.buildPrompt(context.promptTemplate, context.variables, memory);
        const provider = this.getProviderService(context.provider);
        const result = await provider.complete({
            model: context.model,
            prompt,
            temperature: context.temperature,
            maxTokens: context.maxTokens,
            tools: context.tools,
        });
        return {
            success: true,
            output: result.content,
            tokens: result.tokens,
            cost: result.cost,
            provider: context.provider,
            model: context.model,
            metadata: {
                promptLength: prompt.length,
                memoryItems: memory.shortTerm.length,
            },
        };
    }
    buildPrompt(template, variables, memory) {
        let prompt = template;
        Object.entries(variables).forEach(([key, value]) => {
            const placeholder = `{{${key}}}`;
            prompt = prompt.replace(new RegExp(placeholder, 'g'), String(value));
        });
        if (memory.shortTerm.length > 0) {
            const memoryContext = memory.shortTerm
                .slice(-5)
                .map(msg => `${msg.role}: ${msg.content}`)
                .join('\n');
            prompt = `Previous conversation:\n${memoryContext}\n\n${prompt}`;
        }
        return prompt;
    }
    getProviderService(provider) {
        return {
            complete: async (params) => ({
                content: `Mock response for: ${params.prompt.substring(0, 100)}...`,
                tokens: { input: 100, output: 50, total: 150 },
                cost: 0.001,
            }),
        };
    }
    async updateAgentMemory(agentId, sessionId, data) {
        if (!sessionId)
            return;
        try {
            await this.prisma.agentSession.update({
                where: { id: sessionId },
                data: {
                    messages: {
                        create: [
                            {
                                role: 'user',
                                content: data.input,
                                metadata: data.context,
                            },
                            {
                                role: 'assistant',
                                content: data.output,
                                metadata: { agentId },
                            },
                        ],
                    },
                    context: data.context,
                    updatedAt: new Date(),
                },
            });
        }
        catch (error) {
            this.logger.warn(`Failed to update memory: ${error}`);
        }
    }
    async logExecution(agentId, context, result, duration) {
        try {
            await this.prisma.agentExecution.create({
                data: {
                    agentId,
                    sessionId: context.sessionId,
                    input: context.input,
                    output: result.output,
                    status: result.success ? 'COMPLETED' : 'FAILED',
                    provider: result.provider,
                    model: result.model,
                    tokens: result.tokens,
                    cost: result.cost,
                    duration,
                    metadata: result.metadata || {},
                    errorMessage: result.error,
                },
            });
            this.loggerService.business('agent_execution', {
                agentId,
                sessionId: context.sessionId,
                success: result.success,
                duration,
                tokens: result.tokens.total,
                cost: result.cost,
            });
        }
        catch (error) {
            this.logger.warn(`Failed to log execution: ${error}`);
        }
    }
};
exports.AgentRuntimeService = AgentRuntimeService;
exports.AgentRuntimeService = AgentRuntimeService = AgentRuntimeService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        config_1.ConfigService,
        logger_service_1.LoggerService])
], AgentRuntimeService);
//# sourceMappingURL=agent-runtime.service.js.map