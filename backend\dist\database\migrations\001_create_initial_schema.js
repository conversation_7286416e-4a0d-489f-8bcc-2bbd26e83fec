"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateInitialSchema1700000000001 = void 0;
const typeorm_1 = require("typeorm");
class CreateInitialSchema1700000000001 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'tenants',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()',
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '255',
                },
                {
                    name: 'domain',
                    type: 'varchar',
                    length: '255',
                },
                {
                    name: 'settings',
                    type: 'jsonb',
                    default: "'{}'",
                },
                {
                    name: 'status',
                    type: 'varchar',
                    length: '20',
                    default: "'active'",
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                },
            ],
        }), true);
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'permissions',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()',
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '255',
                },
                {
                    name: 'description',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'resource',
                    type: 'varchar',
                    length: '100',
                },
                {
                    name: 'action',
                    type: 'varchar',
                    length: '100',
                },
                {
                    name: 'is_active',
                    type: 'boolean',
                    default: true,
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                },
            ],
        }), true);
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'roles',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()',
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '255',
                },
                {
                    name: 'description',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'is_active',
                    type: 'boolean',
                    default: true,
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                },
            ],
        }), true);
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'users',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()',
                },
                {
                    name: 'email',
                    type: 'varchar',
                    length: '255',
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '100',
                },
                {
                    name: 'password',
                    type: 'varchar',
                    length: '255',
                },
                {
                    name: 'is_active',
                    type: 'boolean',
                    default: true,
                },
                {
                    name: 'last_login_at',
                    type: 'timestamp',
                    isNullable: true,
                },
                {
                    name: 'tenant_id',
                    type: 'uuid',
                    isNullable: true,
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                },
            ],
        }), true);
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'refresh_tokens',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()',
                },
                {
                    name: 'user_id',
                    type: 'uuid',
                },
                {
                    name: 'token',
                    type: 'text',
                },
                {
                    name: 'expires_at',
                    type: 'timestamp',
                },
                {
                    name: 'is_revoked',
                    type: 'boolean',
                    default: false,
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                },
            ],
        }), true);
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'user_roles',
            columns: [
                {
                    name: 'user_id',
                    type: 'uuid',
                },
                {
                    name: 'role_id',
                    type: 'uuid',
                },
            ],
        }), true);
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'role_permissions',
            columns: [
                {
                    name: 'role_id',
                    type: 'uuid',
                },
                {
                    name: 'permission_id',
                    type: 'uuid',
                },
            ],
        }), true);
        await queryRunner.createIndex('tenants', new typeorm_1.TableIndex({ name: 'IDX_TENANTS_DOMAIN', columnNames: ['domain'] }));
        await queryRunner.createIndex('tenants', new typeorm_1.TableIndex({ name: 'IDX_TENANTS_STATUS', columnNames: ['status'] }));
        await queryRunner.createIndex('permissions', new typeorm_1.TableIndex({ name: 'IDX_PERMISSIONS_RESOURCE_ACTION', columnNames: ['resource', 'action'] }));
        await queryRunner.createIndex('roles', new typeorm_1.TableIndex({ name: 'IDX_ROLES_NAME', columnNames: ['name'] }));
        await queryRunner.createIndex('users', new typeorm_1.TableIndex({ name: 'IDX_USERS_EMAIL', columnNames: ['email'] }));
        await queryRunner.createIndex('users', new typeorm_1.TableIndex({ name: 'IDX_USERS_TENANT_ID', columnNames: ['tenant_id'] }));
        await queryRunner.createIndex('users', new typeorm_1.TableIndex({ name: 'IDX_USERS_IS_ACTIVE', columnNames: ['is_active'] }));
        await queryRunner.createIndex('refresh_tokens', new typeorm_1.TableIndex({ name: 'IDX_REFRESH_TOKENS_TOKEN', columnNames: ['token'] }));
        await queryRunner.createIndex('refresh_tokens', new typeorm_1.TableIndex({ name: 'IDX_REFRESH_TOKENS_USER_ID', columnNames: ['user_id'] }));
        await queryRunner.query(`
            ALTER TABLE "users" 
            ADD CONSTRAINT "FK_users_tenant_id" 
            FOREIGN KEY ("tenant_id") 
            REFERENCES "tenants"("id") 
            ON DELETE SET NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "refresh_tokens" 
            ADD CONSTRAINT "FK_refresh_tokens_user_id" 
            FOREIGN KEY ("user_id") 
            REFERENCES "users"("id") 
            ON DELETE CASCADE
        `);
        await queryRunner.query(`
            ALTER TABLE "user_roles" 
            ADD CONSTRAINT "FK_user_roles_user_id" 
            FOREIGN KEY ("user_id") 
            REFERENCES "users"("id") 
            ON DELETE CASCADE
        `);
        await queryRunner.query(`
            ALTER TABLE "user_roles" 
            ADD CONSTRAINT "FK_user_roles_role_id" 
            FOREIGN KEY ("role_id") 
            REFERENCES "roles"("id") 
            ON DELETE CASCADE
        `);
        await queryRunner.query(`
            ALTER TABLE "role_permissions" 
            ADD CONSTRAINT "FK_role_permissions_role_id" 
            FOREIGN KEY ("role_id") 
            REFERENCES "roles"("id") 
            ON DELETE CASCADE
        `);
        await queryRunner.query(`
            ALTER TABLE "role_permissions" 
            ADD CONSTRAINT "FK_role_permissions_permission_id" 
            FOREIGN KEY ("permission_id") 
            REFERENCES "permissions"("id") 
            ON DELETE CASCADE
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT IF EXISTS "FK_users_tenant_id"`);
        await queryRunner.query(`ALTER TABLE "refresh_tokens" DROP CONSTRAINT IF EXISTS "FK_refresh_tokens_user_id"`);
        await queryRunner.query(`ALTER TABLE "user_roles" DROP CONSTRAINT IF EXISTS "FK_user_roles_user_id"`);
        await queryRunner.query(`ALTER TABLE "user_roles" DROP CONSTRAINT IF EXISTS "FK_user_roles_role_id"`);
        await queryRunner.query(`ALTER TABLE "role_permissions" DROP CONSTRAINT IF EXISTS "FK_role_permissions_role_id"`);
        await queryRunner.query(`ALTER TABLE "role_permissions" DROP CONSTRAINT IF EXISTS "FK_role_permissions_permission_id"`);
        await queryRunner.dropTable('role_permissions');
        await queryRunner.dropTable('user_roles');
        await queryRunner.dropTable('refresh_tokens');
        await queryRunner.dropTable('users');
        await queryRunner.dropTable('roles');
        await queryRunner.dropTable('permissions');
        await queryRunner.dropTable('tenants');
    }
}
exports.CreateInitialSchema1700000000001 = CreateInitialSchema1700000000001;
//# sourceMappingURL=001_create_initial_schema.js.map