import { Column, <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, BaseEntity, CreateDateColumn, UpdateDateColumn, ManyToOne } from 'typeorm';
import { Agent } from '../database/entities/agent.entity';
import { Organization } from '../database/entities/organization.entity';

export interface SessionMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

@Entity('session_memories')
export class SessionMemory extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  agentId!: string;

  @ManyToOne(() => Agent)
  agent!: Agent;

  @Column()
  organizationId!: string;

  @ManyToOne(() => Organization)
  organization!: Organization;

  @Column({ type: 'jsonb', default: {} })
  messages!: SessionMessage[];

  @Column({ type: 'jsonb', default: {} })
  context!: Record<string, any>;

  @Column({ type: 'jsonb', default: {} })
  metadata!:     Record<string, any>;

  @Column({ type: 'timestamp' })
  expiresAt!: Date;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}