{"version": 3, "file": "role.entity.js", "sourceRoot": "", "sources": ["../../../src/roles/entities/role.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAA4H;AAC5H,kEAAwD;AACxD,oFAA0E;AAGnE,IAAM,IAAI,GAAV,MAAM,IAAI;CA6BhB,CAAA;AA7BY,oBAAI;AAEb;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;gCACnB;AAGZ;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;kCACX;AAGd;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yCACpB;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;sCACP;AAGnB;IADC,IAAA,0BAAgB,GAAE;8BACP,IAAI;uCAAC;AAGjB;IADC,IAAA,0BAAgB,GAAE;8BACP,IAAI;uCAAC;AAGjB;IADC,IAAA,oBAAU,EAAC,GAAG,EAAE,CAAC,kBAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;;mCAC5B;AAQf;IANC,IAAA,oBAAU,EAAC,GAAG,EAAE,CAAC,8BAAU,EAAE,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC;IAC5D,IAAA,mBAAS,EAAC;QACP,IAAI,EAAE,kBAAkB;QACxB,UAAU,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,oBAAoB,EAAE,IAAI,EAAE;QAC1D,iBAAiB,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE,oBAAoB,EAAE,IAAI,EAAE;KAC1E,CAAC;;yCACyB;eA5BlB,IAAI;IADhB,IAAA,gBAAM,EAAC,OAAO,CAAC;GACH,IAAI,CA6BhB"}