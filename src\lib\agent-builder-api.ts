
import { apiClient } from './api-client';

export interface AgentTemplate {
  id: string;
  name: string;
  category: string;
  description: string;
  config: Record<string, any>;
  skills: string[];
  isPublic: boolean;
  usage: number;
  rating?: number;
  tags: string[];
  version: number;
  parentId?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: {
    id: string;
    name: string;
    email: string;
  };
  promptTemplates?: PromptTemplate[];
  reviews?: TemplateReview[];
  analytics?: TemplateAnalytics[];
}

export interface PromptTemplate {
  id: string;
  name: string;
  description?: string;
  content: string;
  variables: PromptVariable[];
  category: string;
  tags: string[];
  isPublic: boolean;
  usage: number;
  rating?: number;
  version: number;
  parentId?: string;
  agentTemplateId?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: {
    id: string;
    name: string;
    email: string;
  };
  reviews?: PromptReview[];
  analytics?: PromptAnalytics[];
  optimizations?: PromptOptimization[];
}

export interface PromptVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  required: boolean;
  description?: string;
  defaultValue?: any;
}

export interface AgentInstance {
  id: string;
  name: string;
  description?: string;
  config: Record<string, any>;
  status: 'DRAFT' | 'ACTIVE' | 'PAUSED' | 'ARCHIVED';
  version: number;
  createdAt: string;
  updatedAt: string;
}

export interface AgentExecution {
  id: string;
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'TIMEOUT';
  input: string;
  output?: string;
  metadata: Record<string, any>;
  error?: string;
  startedAt?: string;
  completedAt?: string;
  createdAt: string;
}

export interface TemplateReview {
  id: string;
  rating: number;
  comment?: string;
  createdAt: string;
  user: {
    id: string;
    name: string;
  };
}

export interface PromptReview {
  id: string;
  rating: number;
  comment?: string;
  createdAt: string;
  user: {
    id: string;
    name: string;
  };
}

export interface TemplateAnalytics {
  id: string;
  date: string;
  usage: number;
  successRate: number;
  avgResponseTime: number;
  tokenUsage: Record<string, number>;
  errorRate: number;
}

export interface PromptAnalytics {
  id: string;
  date: string;
  usage: number;
  successRate: number;
  avgResponseTime: number;
  tokenUsage: Record<string, number>;
  errorRate: number;
}

export interface PromptOptimization {
  id: string;
  originalContent: string;
  optimizedContent: string;
  improvement: Record<string, number>;
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'APPLIED';
  createdAt: string;
  updatedAt: string;
}

export interface CreateAgentTemplateDto {
  name: string;
  category: string;
  description: string;
  config: Record<string, any>;
  skills?: string[];
  isPublic?: boolean;
  tags?: string[];
  parentId?: string;
}

export interface CreatePromptTemplateDto {
  name: string;
  description?: string;
  content: string;
  variables?: PromptVariable[];
  category: string;
  tags?: string[];
  isPublic?: boolean;
  parentId?: string;
  agentTemplateId?: string;
}

export interface CreateAgentInstanceDto {
  name: string;
  description?: string;
  config: Record<string, any>;
  status?: 'DRAFT' | 'ACTIVE' | 'PAUSED' | 'ARCHIVED';
}

export interface CreateAgentExecutionDto {
  input: string;
  sessionId: string;
  metadata?: Record<string, any>;
}

export interface CreateTemplateReviewDto {
  rating: number;
  comment?: string;
}

export interface CreatePromptReviewDto {
  rating: number;
  comment?: string;
}

class AgentBuilderApiClient {
  // Agent Templates
  async getAgentTemplates(params?: {
    category?: string;
    isPublic?: boolean;
    search?: string;
    page?: number;
    limit?: number;
    sortBy?: 'name' | 'usage' | 'rating' | 'createdAt';
    sortOrder?: 'asc' | 'desc';
  }) {
    return apiClient.get('/api/v1/agent-templates', params);
  }

  async getAgentTemplate(id: string) {
    return apiClient.get(`/api/v1/agent-templates/${id}`);
  }

  async createAgentTemplate(data: CreateAgentTemplateDto) {
    return apiClient.post('/api/v1/agent-templates', data);
  }

  async updateAgentTemplate(id: string, data: Partial<CreateAgentTemplateDto>) {
    return apiClient.put(`/api/v1/agent-templates/${id}`, data);
  }

  async deleteAgentTemplate(id: string) {
    return apiClient.delete(`/api/v1/agent-templates/${id}`);
  }

  async duplicateAgentTemplate(id: string, name?: string) {
    return apiClient.post(`/api/v1/agent-templates/${id}/duplicate`, { name });
  }

  async reviewAgentTemplate(id: string, review: CreateTemplateReviewDto) {
    return apiClient.post(`/api/v1/agent-templates/${id}/reviews`, review);
  }

  async getAgentTemplateCategories() {
    return apiClient.get('/api/v1/agent-templates/categories');
  }

  // Prompt Templates
  async getPromptTemplates(params?: {
    category?: string;
    isPublic?: boolean;
    search?: string;
    agentTemplateId?: string;
    page?: number;
    limit?: number;
    sortBy?: 'name' | 'usage' | 'rating' | 'createdAt';
    sortOrder?: 'asc' | 'desc';
  }) {
    return apiClient.get('/api/v1/prompt-templates', params);
  }

  async getPromptTemplate(id: string) {
    return apiClient.get(`/api/v1/prompt-templates/${id}`);
  }

  async createPromptTemplate(data: CreatePromptTemplateDto) {
    return apiClient.post('/api/v1/prompt-templates', data);
  }

  async updatePromptTemplate(id: string, data: Partial<CreatePromptTemplateDto>) {
    return apiClient.put(`/api/v1/prompt-templates/${id}`, data);
  }

  async deletePromptTemplate(id: string) {
    return apiClient.delete(`/api/v1/prompt-templates/${id}`);
  }

  async duplicatePromptTemplate(id: string, name?: string) {
    return apiClient.post(`/api/v1/prompt-templates/${id}/duplicate`, { name });
  }

  async reviewPromptTemplate(id: string, review: CreatePromptReviewDto) {
    return apiClient.post(`/api/v1/prompt-templates/${id}/reviews`, review);
  }

  async optimizePromptTemplate(id: string) {
    return apiClient.post(`/api/v1/prompt-templates/${id}/optimize`);
  }

  async validatePromptVariables(id: string, content: string, variables: PromptVariable[]) {
    return apiClient.post(`/api/v1/prompt-templates/${id}/validate`, { content, variables }) as Promise<{
      isValid: boolean;
      usedVariables: string[];
      definedVariables: string[];
      missingVariables: string[];
      unusedVariables: string[];
    }>;
  }

  // Agent Instances
  async getAgents(params?: {
    status?: 'DRAFT' | 'ACTIVE' | 'PAUSED' | 'ARCHIVED';
    search?: string;
    page?: number;
    limit?: number;
  }) {
    return apiClient.get('/api/v1/agents', params);
  }

  async getAgent(id: string) {
    return apiClient.get(`/api/v1/agents/${id}`);
  }

  async createAgent(data: CreateAgentInstanceDto) {
    return apiClient.post('/api/v1/agents', data);
  }

  async updateAgent(id: string, data: Partial<CreateAgentInstanceDto>) {
    return apiClient.put(`/api/v1/agents/${id}`, data);
  }

  async deleteAgent(id: string) {
    return apiClient.delete(`/api/v1/agents/${id}`);
  }

  // Agent Execution
  async executeAgent(agentId: string, data: CreateAgentExecutionDto) {
    return apiClient.post(`/api/v1/agents/${agentId}/execute`, data);
  }

  async getAgentExecutions(agentId: string, limit?: number) {
    return apiClient.get(`/api/v1/agents/${agentId}/executions`, { limit });
  }

  async getAgentMetrics(agentId: string) {
    return apiClient.get(`/api/v1/agents/${agentId}/metrics`);
  }
}

export const agentBuilderApi = new AgentBuilderApiClient();