import { Controller, Post, Body, UseGuards, Request, Get, Param, Query } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AgentRuntimeService, AgentExecutionContext } from '../agents/execution/agent-runtime.service';
import { ToolRuntimeService, ToolExecutionContext } from '../tools/execution/tool-runtime.service';
import { WorkflowRuntimeService, WorkflowExecutionContext } from '../workflows/execution/workflow-runtime.service';
import { AIProviderIntegrationService } from '../providers/ai-provider-integration.service';
import { LoggerService } from '../common/services/logger.service';

@ApiTags('execution')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('execution')
export class ExecutionController {
  constructor(
    private agentRuntime: AgentRuntimeService,
    private toolRuntime: ToolRuntimeService,
    private workflowRuntime: WorkflowRuntimeService,
    private aiProvider: AIProviderIntegrationService,
    private logger: LoggerService,
  ) {}

  @Post('agents/:agentId/execute')
  @ApiOperation({ summary: 'Execute an AI agent' })
  @ApiResponse({ status: 200, description: 'Agent executed successfully' })
  async executeAgent(
    @Param('agentId') agentId: string,
    @Body() body: {
      input: string;
      sessionId?: string;
      variables?: Record<string, any>;
      tools?: string[];
    },
    @Request() req: any,
  ) {
    try {
      const context: AgentExecutionContext = {
        agentId,
        input: body.input,
        sessionId: body.sessionId,
        variables: body.variables,
        tools: body.tools,
      };

      const result = await this.agentRuntime.executeAgent(context);

      this.logger.business('agent_executed', {
        agentId,
        userId: req.user.id,
        organizationId: req.user.organizationId,
        success: result.success,
        duration: result.duration,
      });

      return {
        success: true,
        data: result,
      };

    } catch (error) {
      this.logger.error('Agent execution failed', {
        agentId,
        userId: req.user.id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  @Post('tools/:toolId/execute')
  @ApiOperation({ summary: 'Execute a tool' })
  @ApiResponse({ status: 200, description: 'Tool executed successfully' })
  async executeTool(
    @Param('toolId') toolId: string,
    @Body() body: {
      parameters: Record<string, any>;
      agentId?: string;
      sessionId?: string;
      timeout?: number;
    },
    @Request() req: any,
  ) {
    try {
      const context: ToolExecutionContext = {
        toolId,
        parameters: body.parameters,
        agentId: body.agentId,
        sessionId: body.sessionId,
        timeout: body.timeout,
      };

      const result = await this.toolRuntime.executeTool(context);

      this.logger.business('tool_executed', {
        toolId,
        userId: req.user.id,
        organizationId: req.user.organizationId,
        success: result.success,
        duration: result.duration,
      });

      return {
        success: true,
        data: result,
      };

    } catch (error) {
      this.logger.error('Tool execution failed', {
        toolId,
        userId: req.user.id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  @Post('workflows/:workflowId/execute')
  @ApiOperation({ summary: 'Execute a workflow' })
  @ApiResponse({ status: 200, description: 'Workflow executed successfully' })
  async executeWorkflow(
    @Param('workflowId') workflowId: string,
    @Body() body: {
      input: Record<string, any>;
      variables?: Record<string, any>;
      sessionId?: string;
    },
    @Request() req: any,
  ) {
    try {
      const context: WorkflowExecutionContext = {
        workflowId,
        input: body.input,
        variables: body.variables,
        sessionId: body.sessionId,
        userId: req.user.id,
        organizationId: req.user.organizationId,
      };

      const result = await this.workflowRuntime.executeWorkflow(context);

      this.logger.business('workflow_executed', {
        workflowId,
        userId: req.user.id,
        organizationId: req.user.organizationId,
        success: result.success,
        duration: result.duration,
        steps: result.steps.length,
      });

      return {
        success: true,
        data: result,
      };

    } catch (error) {
      this.logger.error('Workflow execution failed', {
        workflowId,
        userId: req.user.id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  @Post('ai/complete')
  @ApiOperation({ summary: 'Direct AI completion' })
  @ApiResponse({ status: 200, description: 'AI completion successful' })
  async aiComplete(
    @Body() body: {
      provider?: string;
      model: string;
      prompt: string;
      temperature?: number;
      maxTokens?: number;
      stream?: boolean;
    },
    @Request() req: any,
  ) {
    try {
      const request = {
        requestId: `req_${Date.now()}`,
        providerId: body.provider,
        modelId: body.model,
        messages: [{ role: 'user' as const, content: body.prompt }],
        temperature: body.temperature,
        maxTokens: body.maxTokens,
        stream: body.stream,
        organizationId: req.user.organizationId,
      };

      const result = await this.aiProvider.processRequest(request);

      this.logger.business('ai_completion', {
        provider: result.providerId,
        model: result.modelId,
        userId: req.user.id,
        organizationId: req.user.organizationId,
        tokens: result.usage.totalTokens,
        cost: result.cost,
      });

      return {
        success: true,
        data: result,
      };

    } catch (error) {
      this.logger.error('AI completion failed', {
        userId: req.user.id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  @Get('agents/:agentId/tools')
  @ApiOperation({ summary: 'Get available tools for an agent' })
  @ApiResponse({ status: 200, description: 'Tools retrieved successfully' })
  async getAgentTools(
    @Param('agentId') agentId: string,
    @Request() req: any,
  ) {
    try {
      const tools = await this.toolRuntime.getAvailableTools(agentId);

      return {
        success: true,
        data: tools,
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  @Get('providers')
  @ApiOperation({ summary: 'Get available AI providers' })
  @ApiResponse({ status: 200, description: 'Providers retrieved successfully' })
  async getProviders(@Request() req: any) {
    try {
      const providers = this.aiProvider.getAvailableProviders();

      return {
        success: true,
        data: providers,
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  @Get('health')
  @ApiOperation({ summary: 'Check execution engine health' })
  @ApiResponse({ status: 200, description: 'Health check successful' })
  async healthCheck() {
    return {
      success: true,
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        services: {
          agentRuntime: 'operational',
          toolRuntime: 'operational',
          workflowRuntime: 'operational',
          aiProviders: 'operational',
        },
      },
    };
  }
}
