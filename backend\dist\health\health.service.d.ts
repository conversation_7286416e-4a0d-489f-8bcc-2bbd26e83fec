import { ConfigService } from '@nestjs/config';
import { RedisService } from '../cache/redis.service';
import { CustomLoggerService } from '../logging/logger.service';
import { DataSource } from 'typeorm';
export interface HealthCheckResult {
    status: 'healthy' | 'unhealthy' | 'degraded';
    timestamp: string;
    uptime: number;
    version: string;
    environment: string;
    checks: {
        database: HealthCheck;
        redis: HealthCheck;
        memory: HealthCheck;
        disk: HealthCheck;
    };
}
export interface HealthCheck {
    status: 'healthy' | 'unhealthy';
    responseTime: number;
    details?: Record<string, any>;
}
export declare class HealthService {
    private readonly configService;
    private readonly redisService;
    private readonly dataSource;
    private readonly logger;
    constructor(configService: ConfigService, redisService: RedisService, dataSource: DataSource, logger: CustomLoggerService);
    checkHealth(): Promise<HealthCheckResult>;
    private checkDatabase;
    private checkRedis;
    private checkMemory;
    private checkDisk;
    private determineOverallStatus;
    getDetailedHealth(): Promise<HealthCheckResult & {
        system: Record<string, any>;
    }>;
}
