import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Patch,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  HttpException,
  Logger
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AIProviderManagerService } from './ai-provider-manager.service';
import { AIProviderIntegrationService } from './ai-provider-integration.service';
import { AIProviderSelectorService } from './ai-provider-selector.service';
import { AIProviderStreamService } from './ai-provider-stream.service';
import {
  CreateProviderDto,
  UpdateProviderDto,
  TestProviderDto,
  ProviderRequestDto,
  ProviderSelectionDto,
  ProviderAnalyticsDto,
  ProviderUsageDto
} from './dto/ai-provider.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RequirePermissions } from '../auth/permissions.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { TenantId } from '../auth/decorators/tenant-id.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('AI Providers')
@Controller('api/v1/providers')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AIProviderController {
  private readonly logger = new Logger(AIProviderController.name);

  constructor(
    private readonly providerManagerService: AIProviderManagerService,
    private readonly providerIntegrationService: AIProviderIntegrationService,
    private readonly providerSelectorService: AIProviderSelectorService,
    private readonly providerStreamService: AIProviderStreamService
  ) { }

  @Post()
  @RequirePermissions(['providers:create'])
  @ApiOperation({ summary: 'Create a new AI provider' })
  @ApiResponse({ status: 201, description: 'Provider created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async createProvider(
    @Body() createProviderDto: CreateProviderDto,
    @TenantId() organizationId: string,
    @CurrentUser() user: User
  ) {
    try {
      const provider = await this.providerManagerService.createProvider(
        createProviderDto,
        organizationId,
        user.id
      );

      return {
        success: true,
        data: provider,
        message: 'Provider created successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to create provider: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to create provider'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Get()
  @RequirePermissions(['providers:read'])
  @ApiOperation({ summary: 'Get all providers for organization' })
  @ApiResponse({ status: 200, description: 'Providers retrieved successfully' })
  async getProviders(@TenantId() organizationId: string) {
    try {
      const providers = await this.providerManagerService.getProviders(organizationId);

      return {
        success: true,
        data: providers,
        message: 'Providers retrieved successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to get providers: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to get providers'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':id')
  @RequirePermissions(['providers:read'])
  @ApiOperation({ summary: 'Get provider by ID' })
  @ApiResponse({ status: 200, description: 'Provider retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Provider not found' })
  async getProvider(@Param('id') id: string, @TenantId() organizationId: string) {
    try {
      const provider = await this.providerManagerService.getProvider(id, organizationId);

      return {
        success: true,
        data: provider,
        message: 'Provider retrieved successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to get provider ${id}: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to get provider'
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put(':id')
  @RequirePermissions(['providers:update'])
  @ApiOperation({ summary: 'Update provider' })
  @ApiResponse({ status: 200, description: 'Provider updated successfully' })
  @ApiResponse({ status: 404, description: 'Provider not found' })
  async updateProvider(
    @Param('id') id: string,
    @Body() updateProviderDto: UpdateProviderDto,
    @TenantId() organizationId: string
  ) {
    try {
      const provider = await this.providerManagerService.updateProvider(
        id,
        updateProviderDto,
        organizationId
      );

      return {
        success: true,
        data: provider,
        message: 'Provider updated successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to update provider ${id}: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to update provider'
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete(':id')
  @RequirePermissions(['providers:delete'])
  @ApiOperation({ summary: 'Delete provider' })
  @ApiResponse({ status: 200, description: 'Provider deleted successfully' })
  @ApiResponse({ status: 404, description: 'Provider not found' })
  async deleteProvider(@Param('id') id: string, @TenantId() organizationId: string) {
    try {
      await this.providerManagerService.deleteProvider(id, organizationId);

      return {
        success: true,
        message: 'Provider deleted successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to delete provider ${id}: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to delete provider'
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Patch(':id/toggle')
  @RequirePermissions(['providers:update'])
  @ApiOperation({ summary: 'Toggle provider active status' })
  @ApiResponse({ status: 200, description: 'Provider status updated successfully' })
  async toggleProvider(
    @Param('id') id: string,
    @Body() body: { isActive: boolean },
    @TenantId() organizationId: string
  ) {
    try {
      const provider = await this.providerManagerService.toggleProvider(
        id,
        body.isActive,
        organizationId
      );

      return {
        success: true,
        data: provider,
        message: `Provider ${body.isActive ? 'activated' : 'deactivated'} successfully`
      };
    } catch (error) {
      this.logger.error(`Failed to toggle provider ${id}: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to toggle provider'
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('test')
  @RequirePermissions(['providers:read'])
  @ApiOperation({ summary: 'Test provider configuration' })
  @ApiResponse({ status: 200, description: 'Provider test completed' })
  async testProviderConfig(@Body() testProviderDto: TestProviderDto) {
    try {
      const result = await this.providerIntegrationService.testProvider(testProviderDto);

      return {
        success: true,
        data: result,
        message: 'Provider test completed successfully'
      };
    } catch (error) {
      this.logger.error(`Provider test failed: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Provider test failed'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Post(':id/test')
  @RequirePermissions(['providers:read'])
  @ApiOperation({ summary: 'Test existing provider' })
  @ApiResponse({ status: 200, description: 'Provider test completed' })
  async testProvider(@Param('id') id: string, @TenantId() organizationId: string) {
    try {
      const result = await this.providerStreamService.testProvider(id);

      return {
        success: true,
        data: result,
        message: 'Provider test completed successfully'
      };
    } catch (error) {
      this.logger.error(`Provider test failed for ${id}: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Provider test failed'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Get('analytics')
  @RequirePermissions(['providers:read'])
  @ApiOperation({ summary: 'Get provider analytics' })
  @ApiResponse({ status: 200, description: 'Analytics retrieved successfully' })
  async getProviderAnalytics(
    @Query() query: ProviderAnalyticsDto,
    @TenantId() organizationId: string
  ) {
    try {
      const analytics = await this.providerManagerService.getProviderAnalytics(
        organizationId,
        query.timeRange
      );

      return {
        success: true,
        data: analytics,
        message: 'Analytics retrieved successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to get analytics: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to get analytics'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':id/usage')
  @RequirePermissions(['providers:read'])
  @ApiOperation({ summary: 'Get provider usage' })
  @ApiResponse({ status: 200, description: 'Usage retrieved successfully' })
  async getProviderUsage(
    @Param('id') id: string,
    @Query() query: ProviderUsageDto,
    @TenantId() organizationId: string
  ) {
    try {
      // Verify provider belongs to organization
      await this.providerManagerService.getProvider(id, organizationId);

      const usage = await this.providerManagerService.getProviderUsage(id, query.timeRange);

      return {
        success: true,
        data: usage,
        message: 'Usage retrieved successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to get usage for ${id}: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to get usage'
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('select')
  @RequirePermissions(['providers:read'])
  @ApiOperation({ summary: 'Select optimal provider' })
  @ApiResponse({ status: 200, description: 'Provider selected successfully' })
  async selectProvider(
    @Body() selectionDto: ProviderSelectionDto,
    @TenantId() organizationId: string
  ) {
    try {
      const result = await this.providerSelectorService.selectOptimalProvider({
        ...selectionDto,
        organizationId
      });

      return {
        success: true,
        data: result,
        message: 'Provider selected successfully'
      };
    } catch (error) {
      this.logger.error(`Provider selection failed: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Provider selection failed'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Post(':id/models/:modelId/request')
  @RequirePermissions(['providers:execute'])
  @ApiOperation({ summary: 'Send request to provider model' })
  @ApiResponse({ status: 200, description: 'Request processed successfully' })
  async sendProviderRequest(
    @Param('id') providerId: string,
    @Param('modelId') modelId: string,
    @Body() requestDto: ProviderRequestDto,
    @TenantId() organizationId: string,
    @CurrentUser() user: User
  ) {
    try {
      // Verify provider belongs to organization
      await this.providerManagerService.getProvider(providerId, organizationId);

      const result = await this.providerIntegrationService.executeRequest(
        providerId,
        modelId,
        requestDto,
        user.id
      );

      return {
        success: true,
        data: result,
        message: 'Request processed successfully'
      };
    } catch (error) {
      this.logger.error(`Provider request failed: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Request failed'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Post(':id/models/:modelId/stream')
  @RequirePermissions(['providers:execute'])
  @ApiOperation({ summary: 'Stream request to provider model' })
  @ApiResponse({ status: 200, description: 'Stream started successfully' })
  async streamProviderRequest(
    @Param('id') providerId: string,
    @Param('modelId') modelId: string,
    @Body() requestDto: ProviderRequestDto,
    @TenantId() organizationId: string,
    @CurrentUser() user: User,
    @Request() req: any
  ) {
    try {
      // Verify provider belongs to organization
      await this.providerManagerService.getProvider(providerId, organizationId);

      const stream = await this.providerStreamService.streamRequest(
        providerId,
        modelId,
        requestDto,
        user.id
      );

      // Set headers for streaming
      req.res.setHeader('Content-Type', 'text/plain; charset=utf-8');
      req.res.setHeader('Transfer-Encoding', 'chunked');
      req.res.setHeader('Cache-Control', 'no-cache');
      req.res.setHeader('Connection', 'keep-alive');

      // Return the stream
      return stream;
    } catch (error) {
      this.logger.error(`Provider stream failed: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Stream failed'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Get(':id/health')
  @RequirePermissions(['providers:read'])
  @ApiOperation({ summary: 'Get provider health status' })
  @ApiResponse({ status: 200, description: 'Health status retrieved successfully' })
  async getProviderHealth(@Param('id') id: string, @TenantId() organizationId: string) {
    try {
      const provider = await this.providerManagerService.getProvider(id, organizationId);

      const health = {
        status: provider.performanceMetrics?.healthStatus || 'unknown',
        latency: provider.performanceMetrics?.averageLatency || 0,
        lastChecked: provider.performanceMetrics?.lastChecked || new Date()
      };

      return {
        success: true,
        data: health,
        message: 'Health status retrieved successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to get health for ${id}: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to get health status'
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}