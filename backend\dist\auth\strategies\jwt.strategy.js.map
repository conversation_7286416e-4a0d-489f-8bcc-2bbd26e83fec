{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../../src/auth/strategies/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAmE;AACnE,+CAAoD;AACpD,+CAAoD;AACpD,2CAA+C;AAaxC,IAAM,WAAW,GAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,CAAC;IACvD,YAAoB,aAA4B;QAC5C,KAAK,CAAC;YACF,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,gBAAgB,EAAE,KAAK;YACvB,WAAW,EAAE,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC;YACpD,iBAAiB,EAAE,IAAI;SAC1B,CAAC,CAAC;QANa,kBAAa,GAAb,aAAa,CAAe;IAOhD,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAgB,EAAE,OAAmB;QAEhD,MAAM,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAGxD,IAAI,OAAO,CAAC,QAAQ,IAAI,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAChE,MAAM,IAAI,8BAAqB,CAAC,sBAAsB,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO;YACH,MAAM,EAAE,OAAO,CAAC,GAAG;YACnB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,QAAQ;SACzC,CAAC;IACN,CAAC;IAEO,wBAAwB,CAAC,OAAgB;QAE7C,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;QAClC,IAAI,IAAI,EAAE,CAAC;YACP,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,SAAS,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,KAAK,KAAK,EAAE,CAAC;gBAC1D,OAAO,SAAS,CAAC;YACrB,CAAC;QACL,CAAC;QAGD,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAW,CAAC;QAC9D,IAAI,YAAY,EAAE,CAAC;YACf,OAAO,YAAY,CAAC;QACxB,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;CACJ,CAAA;AA7CY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;qCAE0B,sBAAa;GADvC,WAAW,CA6CvB"}