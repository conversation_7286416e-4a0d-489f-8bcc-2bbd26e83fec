import { User } from '../users/user.entity';
import { Workflow } from '../workflows/workflow.entity';
import { Tool } from '../tools/tool.entity';
export declare enum AgentType {
    CONDUCTOR = "conductor",
    SPECIALIZED = "specialized",
    REVIEW = "review",
    COORDINATOR = "coordinator",
    EXECUTOR = "executor"
}
export declare enum AgentStatus {
    DRAFT = "draft",
    ACTIVE = "active",
    INACTIVE = "inactive",
    TRAINING = "training",
    ERROR = "error",
    DEPRECATED = "deprecated"
}
export declare enum AgentCapability {
    TEXT_GENERATION = "text_generation",
    CODE_GENERATION = "code_generation",
    DATA_ANALYSIS = "data_analysis",
    API_INTEGRATION = "api_integration",
    FILE_PROCESSING = "file_processing",
    WEB_SCRAPING = "web_scraping",
    EMAIL_PROCESSING = "email_processing",
    DOCUMENT_PROCESSING = "document_processing",
    IMAGE_PROCESSING = "image_processing",
    AUDIO_PROCESSING = "audio_processing",
    VIDEO_PROCESSING = "video_processing",
    DATABASE_OPERATIONS = "database_operations",
    WORKFLOW_ORCHESTRATION = "workflow_orchestration",
    DECISION_MAKING = "decision_making",
    LEARNING = "learning"
}
export declare class Agent {
    id: string;
    name: string;
    description: string;
    type: AgentType;
    status: AgentStatus;
    capabilities: AgentCapability[];
    configuration: {
        model: {
            provider: string;
            model: string;
            version: string;
            parameters: Record<string, any>;
        };
        memory: {
            type: 'none' | 'conversation' | 'vector' | 'hybrid';
            maxTokens: number;
            retentionDays: number;
        };
        tools: Array<{
            id: string;
            name: string;
            type: string;
            config: Record<string, any>;
            permissions: string[];
        }>;
        prompts: {
            system: string;
            user: string;
            assistant: string;
            examples: Array<{
                input: string;
                output: string;
            }>;
        };
        constraints: {
            maxTokens: number;
            maxRequests: number;
            rateLimit: number;
            costLimit: number;
        };
        security: {
            dataRetention: number;
            encryption: boolean;
            auditLogging: boolean;
            accessControl: string[];
        };
    };
    performance: {
        totalRequests: number;
        successfulRequests: number;
        failedRequests: number;
        averageResponseTime: number;
        averageTokensUsed: number;
        averageCost: number;
        lastUsedAt: Date;
        uptime: number;
        errorRate: number;
    };
    learning: {
        enabled: boolean;
        adaptationRate: number;
        feedbackLoop: boolean;
        trainingData: Array<{
            input: string;
            expectedOutput: string;
            actualOutput: string;
            feedback: number;
            timestamp: Date;
        }>;
        modelVersion: string;
        lastTrainedAt: Date;
    };
    metadata: {
        version: string;
        author: string;
        tags: string[];
        category: string;
        documentation: string;
        examples: Array<{
            title: string;
            description: string;
            input: string;
            output: string;
        }>;
        dependencies: string[];
        compatibility: string[];
    };
    permissions: {
        owners: string[];
        editors: string[];
        viewers: string[];
        public: boolean;
        allowedOrganizations: string[];
    };
    monitoring: {
        healthCheck: {
            enabled: boolean;
            interval: number;
            timeout: number;
            threshold: number;
        };
        alerts: Array<{
            type: string;
            condition: string;
            action: string;
            recipients: string[];
        }>;
        metrics: {
            responseTime: number[];
            errorRate: number[];
            usageCount: number[];
            costHistory: number[];
        };
    };
    user: User;
    userId: string;
    organizationId: string;
    workflow: Workflow;
    workflowId: string;
    tools: Tool[];
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date;
}
