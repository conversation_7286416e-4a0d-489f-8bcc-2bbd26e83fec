import { Injectable, BadRequestException } from '@nestjs/common';
import { WorkflowDefinitionDto } from './dto/workflow.dto';
import { CustomLoggerService } from '../logging/logger.service';

@Injectable()
export class WorkflowValidationService {
    constructor(private readonly logger: CustomLoggerService) { }

    async validateWorkflowDefinition(definition: WorkflowDefinitionDto): Promise<void> {
        try {
            // Validate version
            if (!definition.version || !this.isValidVersion(definition.version)) {
                throw new BadRequestException('Invalid workflow version format');
            }

            // Validate steps
            if (!definition.steps || definition.steps.length === 0) {
                throw new BadRequestException('Workflow must have at least one step');
            }

            // Validate step IDs are unique
            const stepIds = definition.steps.map(step => step.id);
            const uniqueStepIds = new Set(stepIds);
            if (stepIds.length !== uniqueStepIds.size) {
                throw new BadRequestException('Step IDs must be unique');
            }

            // Validate step configurations
            for (const step of definition.steps) {
                await this.validateStep(step);
            }

            // Validate triggers
            if (!definition.triggers || definition.triggers.length === 0) {
                throw new BadRequestException('Workflow must have at least one trigger');
            }

            for (const trigger of definition.triggers) {
                await this.validateTrigger(trigger);
            }

            // Validate dependencies (no circular dependencies)
            await this.validateDependencies(definition.steps);

            // Validate error handling
            await this.validateErrorHandling(definition.errorHandling);

            this.logger.log('Workflow definition validation passed', 'WorkflowValidationService');
        } catch (error) {
            this.logger.error(`Workflow validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowValidationService');
            throw error;
        }
    }

    private async validateStep(step: any): Promise<void> {
        if (!step.id || typeof step.id !== 'string') {
            throw new BadRequestException('Step must have a valid string ID');
        }

        if (!step.type || typeof step.type !== 'string') {
            throw new BadRequestException('Step must have a valid type');
        }

        if (!step.name || typeof step.name !== 'string') {
            throw new BadRequestException('Step must have a valid name');
        }

        if (!step.config || typeof step.config !== 'object') {
            throw new BadRequestException('Step must have a valid configuration object');
        }

        // Validate step type-specific configuration
        await this.validateStepTypeConfig(step.type, step.config);

        // Validate dependencies if present
        if (step.dependencies) {
            if (!Array.isArray(step.dependencies)) {
                throw new BadRequestException('Step dependencies must be an array');
            }
            for (const dep of step.dependencies) {
                if (typeof dep !== 'string') {
                    throw new BadRequestException('Step dependencies must be string IDs');
                }
            }
        }

        // Validate conditions if present
        if (step.conditions) {
            if (typeof step.conditions !== 'object') {
                throw new BadRequestException('Step conditions must be an object');
            }
        }
    }

    private async validateTrigger(trigger: any): Promise<void> {
        if (!trigger.type) {
            throw new BadRequestException('Trigger must have a valid type');
        }

        if (!trigger.config || typeof trigger.config !== 'object') {
            throw new BadRequestException('Trigger must have a valid configuration object');
        }

        // Validate trigger type-specific configuration
        await this.validateTriggerTypeConfig(trigger.type, trigger.config);
    }

    private async validateErrorHandling(errorHandling: any): Promise<void> {
        if (!errorHandling) {
            throw new BadRequestException('Error handling configuration is required');
        }

        if (typeof errorHandling.retryCount !== 'number' || errorHandling.retryCount < 0) {
            throw new BadRequestException('Retry count must be a non-negative number');
        }

        if (typeof errorHandling.retryDelay !== 'number' || errorHandling.retryDelay < 0) {
            throw new BadRequestException('Retry delay must be a non-negative number');
        }

        if (errorHandling.fallbackSteps) {
            if (!Array.isArray(errorHandling.fallbackSteps)) {
                throw new BadRequestException('Fallback steps must be an array');
            }
            for (const step of errorHandling.fallbackSteps) {
                if (typeof step !== 'string') {
                    throw new BadRequestException('Fallback steps must be string IDs');
                }
            }
        }
    }

    private async validateDependencies(steps: any[]): Promise<void> {
        const stepIds = new Set(steps.map(step => step.id));
        const visited = new Set<string>();
        const recursionStack = new Set<string>();

        for (const step of steps) {
            if (step.dependencies) {
                for (const depId of step.dependencies) {
                    if (!stepIds.has(depId)) {
                        throw new BadRequestException(`Step ${step.id} depends on non-existent step ${depId}`);
                    }
                }
            }
        }

        // Check for circular dependencies
        for (const step of steps) {
            if (!visited.has(step.id)) {
                if (this.hasCircularDependency(step.id, steps, visited, recursionStack)) {
                    throw new BadRequestException(`Circular dependency detected involving step ${step.id}`);
                }
            }
        }
    }

    private hasCircularDependency(
        stepId: string,
        steps: any[],
        visited: Set<string>,
        recursionStack: Set<string>
    ): boolean {
        if (recursionStack.has(stepId)) {
            return true;
        }

        if (visited.has(stepId)) {
            return false;
        }

        visited.add(stepId);
        recursionStack.add(stepId);

        const step = steps.find(s => s.id === stepId);
        if (step && step.dependencies) {
            for (const depId of step.dependencies) {
                if (this.hasCircularDependency(depId, steps, visited, recursionStack)) {
                    return true;
                }
            }
        }

        recursionStack.delete(stepId);
        return false;
    }

    private async validateStepTypeConfig(type: string, config: any): Promise<void> {
        // Implement type-specific validation logic
        switch (type) {
            case 'http_request':
                this.validateHttpRequestConfig(config);
                break;
            case 'data_transformation':
                this.validateDataTransformationConfig(config);
                break;
            case 'condition':
                this.validateConditionConfig(config);
                break;
            case 'loop':
                this.validateLoopConfig(config);
                break;
            default:
                // For custom step types, just ensure config is an object
                if (typeof config !== 'object') {
                    throw new BadRequestException(`Invalid configuration for step type: ${type}`);
                }
        }
    }

    private async validateTriggerTypeConfig(type: string, config: any): Promise<void> {
        // Implement trigger type-specific validation logic
        switch (type) {
            case 'webhook':
                this.validateWebhookConfig(config);
                break;
            case 'scheduled':
                this.validateScheduledConfig(config);
                break;
            case 'event':
                this.validateEventConfig(config);
                break;
            default:
                // For custom trigger types, just ensure config is an object
                if (typeof config !== 'object') {
                    throw new BadRequestException(`Invalid configuration for trigger type: ${type}`);
                }
        }
    }

    private validateHttpRequestConfig(config: any): void {
        if (!config.url || typeof config.url !== 'string') {
            throw new BadRequestException('HTTP request step must have a valid URL');
        }

        if (config.method && !['GET', 'POST', 'PUT', 'PATCH', 'DELETE'].includes(config.method)) {
            throw new BadRequestException('HTTP request step must have a valid method');
        }
    }

    private validateDataTransformationConfig(config: any): void {
        if (!config.transformation || typeof config.transformation !== 'object') {
            throw new BadRequestException('Data transformation step must have a valid transformation configuration');
        }
    }

    private validateConditionConfig(config: any): void {
        if (!config.condition || typeof config.condition !== 'object') {
            throw new BadRequestException('Condition step must have a valid condition configuration');
        }
    }

    private validateLoopConfig(config: any): void {
        if (!config.iterator || typeof config.iterator !== 'string') {
            throw new BadRequestException('Loop step must have a valid iterator configuration');
        }
    }

    private validateWebhookConfig(config: any): void {
        if (!config.path || typeof config.path !== 'string') {
            throw new BadRequestException('Webhook trigger must have a valid path');
        }
    }

    private validateScheduledConfig(config: any): void {
        if (!config.cron || typeof config.cron !== 'string') {
            throw new BadRequestException('Scheduled trigger must have a valid cron expression');
        }
    }

    private validateEventConfig(config: any): void {
        if (!config.eventType || typeof config.eventType !== 'string') {
            throw new BadRequestException('Event trigger must have a valid event type');
        }
    }

    private isValidVersion(version: string): boolean {
        // Simple version validation - can be enhanced
        return /^\d+\.\d+\.\d+$/.test(version);
    }
} 