import { OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
export interface ApixEvent {
    type: string;
    payload: any;
    organizationId: string;
    userId?: string;
    timestamp: Date;
    metadata?: Record<string, any>;
}
export declare class ApixGateway implements OnGatewayConnection, OnGatewayDisconnect {
    private jwtService;
    private prisma;
    server: Server;
    private readonly logger;
    private redis;
    private pubClient;
    private subClient;
    constructor(jwtService: JwtService, prisma: PrismaService);
    handleConnection(client: Socket): Promise<void>;
    handleDisconnect(client: Socket): Promise<void>;
    handleSubscribe(client: Socket, data: {
        channels: string[];
    }): Promise<void>;
    handleUnsubscribe(client: Socket, data: {
        channels: string[];
    }): Promise<void>;
    publishEvent(event: ApixEvent): Promise<void>;
    publishToOrganization(organizationId: string, event: Omit<ApixEvent, 'organizationId'>): Promise<void>;
    publishToUser(userId: string, organizationId: string, event: Omit<ApixEvent, 'organizationId' | 'userId'>): Promise<void>;
    emitToOrganization(organizationId: string, eventType: string, payload: any): Promise<void>;
    emitToUser(userId: string, organizationId: string, eventType: string, payload: any): Promise<void>;
    publishAgentEvent(type: string, payload: any, organizationId: string, userId?: string): Promise<void>;
    publishToolEvent(type: string, payload: any, organizationId: string, userId?: string): Promise<void>;
    publishSessionEvent(type: string, payload: any, organizationId: string, userId?: string): Promise<void>;
    publishSystemEvent(type: string, payload: any, organizationId: string): Promise<void>;
    private setupRedisSubscriptions;
    private broadcastEvent;
    private validateChannelAccess;
    getEventHistory(organizationId: string, limit?: number): Promise<ApixEvent[]>;
    getActiveConnections(organizationId: string): Promise<any[]>;
}
