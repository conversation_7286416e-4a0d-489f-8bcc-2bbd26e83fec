{"version": 3, "file": "session-memory.service.js", "sourceRoot": "", "sources": ["../../src/agents/session-memory.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,qDAAsD;AACtD,qCAA4B;AA0BrB,IAAM,oBAAoB,4BAA1B,MAAM,oBAAoB;IAM/B,YACiB,KAA6B;QAAZ,UAAK,GAAL,KAAK,CAAO;QAN7B,WAAM,GAAG,IAAI,eAAM,CAAC,sBAAoB,CAAC,IAAI,CAAC,CAAC;QAC/C,mBAAc,GAAG,UAAU,CAAC;QAC5B,uBAAkB,GAAG,IAAI,CAAC;QAC1B,2BAAsB,GAAG,EAAE,CAAC;IAI1C,CAAC;IAEJ,KAAK,CAAC,aAAa,CACjB,OAAe,EACf,cAAsB,EACtB,MAIC;QAED,MAAM,SAAS,GAAG,GAAG,cAAc,IAAI,OAAO,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAE1G,MAAM,OAAO,GAAkB;YAC7B,EAAE,EAAE,SAAS;YACb,OAAO;YACP,cAAc;YACd,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE;gBACR,UAAU,EAAE,CAAC;gBACb,SAAS,EAAE,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,SAAS,KAAI,IAAI,CAAC,kBAAkB;gBACvD,aAAa,EAAE,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,aAAa,KAAI,IAAI,CAAC,sBAAsB;gBACnE,cAAc,EAAE,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,cAAc,KAAI,KAAK;aAChD;YACD,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAGhC,MAAM,iBAAiB,GAAG,OAAO,CAAC,QAAQ,CAAC,aAAa,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;QACxE,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,cAAc,GAAG,SAAS,EAAE,EAAE,iBAAiB,CAAC,CAAC;QAEjF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,SAAS,eAAe,OAAO,EAAE,CAAC,CAAC;QACvE,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,SAAiB,EAAE,cAAsB;QACxD,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,GAAG,SAAS,EAAE,CAAC,CAAC;YAE/E,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,OAAO,GAAkB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAGvD,IAAI,OAAO,CAAC,cAAc,KAAK,cAAc,EAAE,CAAC;gBAC9C,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACpD,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,SAAS,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;YACrH,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,SAAiB,EAAE,OAAuB;;QACzD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,MAAA,OAAO,CAAC,QAAQ,0CAAE,cAAc,CAAC,CAAC;YAEnF,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACvC,CAAC;YAGD,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC/B,OAAO,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAG/B,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC3D,OAAO,CAAC,QAAQ,CAAC,UAAU,IAAI,aAAa,CAAC;YAG7C,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;gBAC7D,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACtC,CAAC;YAED,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEhC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,SAAS,qBAAqB,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QACzG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,SAAS,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;YAChI,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,SAAiB,EAAE,cAAsB,EAAE,OAA4B;QACzF,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;YAEjE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACvC,CAAC;YAED,OAAO,CAAC,OAAO,mCAAQ,OAAO,CAAC,OAAO,GAAK,OAAO,CAAE,CAAC;YACrD,OAAO,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAE/B,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,SAAS,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;YAChI,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,SAAiB,EAAE,cAAsB,EAAE,KAAc;QAC/E,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;YAEjE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YAClC,OAAO,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QACnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,SAAS,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;YAC7H,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,SAAiB,EAAE,cAAsB;QAC3D,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;YAEjE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACvC,CAAC;YAED,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,GAAG,SAAS,EAAE,CAAC,CAAC;YAC3D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,SAAS,EAAE,CAAC,CAAC;QACnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,SAAS,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;YACxH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,OAAe,EAAE,cAAsB;QAC9D,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,GAAG,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI,OAAO,IAAI,CAAC;YACvE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAE5C,MAAM,QAAQ,GAAoB,EAAE,CAAC;YAErC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC9C,IAAI,WAAW,EAAE,CAAC;oBAChB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;gBACzC,CAAC;YACH,CAAC;YAED,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QACpG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,OAAO,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;YAC9H,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,OAAsB;QAC9C,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAClB,GAAG,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,EAAE,EAAE,EACrC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CACxB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,OAAsB;QAElD,MAAM,cAAc,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;QACzE,MAAM,cAAc,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAEpF,OAAO,CAAC,QAAQ,GAAG,CAAC,GAAG,cAAc,EAAE,GAAG,cAAc,CAAC,CAAC;QAG1D,OAAO,CAAC,QAAQ,CAAC,UAAU,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CACnD,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAChE,CAAC,CACF,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,OAAO,CAAC,EAAE,iBAAiB,OAAO,CAAC,QAAQ,CAAC,MAAM,WAAW,CAAC,CAAC;IAC9F,CAAC;IAEO,cAAc,CAAC,IAAY;QAEjC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,cAAsB;QAM1C,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,GAAG,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI,CAAC;YAC5D,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAE5C,IAAI,aAAa,GAAG,CAAC,CAAC;YACtB,IAAI,cAAc,GAAG,CAAC,CAAC;YACvB,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAE7D,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC9C,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,OAAO,GAAkB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;oBACvD,aAAa,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;oBAEzC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,SAAS,EAAE,CAAC;wBAC5C,cAAc,EAAE,CAAC;oBACnB,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO;gBACL,aAAa,EAAE,IAAI,CAAC,MAAM;gBAC1B,cAAc;gBACd,aAAa;gBACb,oBAAoB,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aACxE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gDAAgD,cAAc,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;YACjJ,OAAO;gBACL,aAAa,EAAE,CAAC;gBAChB,cAAc,EAAE,CAAC;gBACjB,aAAa,EAAE,CAAC;gBAChB,oBAAoB,EAAE,CAAC;aACxB,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAhPY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;IAQR,WAAA,IAAA,qBAAW,GAAE,CAAA;qCAAyB,iBAAK;GAPnC,oBAAoB,CAgPhC"}