import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Like, Repository } from 'typeorm';
import { SessionMemory as SessionMemoryEntity, SessionMessage } from './session-memory.entity';



export interface SessionMemory {
  id: string;
  agentId: string;
  organizationId: string;
  messages: SessionMessage[];
  context: Record<string, any>;
  metadata: {
    tokenCount: number;
    maxTokens: number;
    retentionDays: number;
    enableLongTerm: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}
  
@Injectable()
export class SessionMemoryService {
  private readonly logger = new Logger(SessionMemoryService.name);
  private readonly SESSION_PREFIX = 'session:';
  private readonly DEFAULT_MAX_TOKENS = 4000;
  private readonly DEFAULT_RETENTION_DAYS = 30;

  constructor(
    @InjectRepository(SessionMemoryEntity)
    private sessionMemoryRepository: Repository<SessionMemoryEntity>,
  ) {}

  async createSession(
    agentId: string,
    organizationId: string,
    config?: {
      maxTokens?: number;
      retentionDays?: number;
      enableLongTerm?: boolean;
    },
  ): Promise<SessionMemoryEntity> {
    const sessionId = `${organizationId}_${agentId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const session: SessionMemoryEntity = new SessionMemoryEntity();
    session.id = sessionId;
    session.agentId = agentId;
    session.organizationId = organizationId;
    session.messages = [];
    session.context = {};
    session.metadata = {
      tokenCount: 0,
      maxTokens: config?.maxTokens || this.DEFAULT_MAX_TOKENS,
      retentionDays: config?.retentionDays || this.DEFAULT_RETENTION_DAYS,
      enableLongTerm: config?.enableLongTerm || false,
    };
    session.createdAt = new Date();
    session.updatedAt = new Date();

    await this.saveSession(session);
    
    // Set expiration
    const expirationSeconds = session.metadata.retentionDays * 24 * 60 * 60;
    session.expiresAt = new Date(Date.now() + expirationSeconds * 1000);

    await this.saveSession(session);
    
    await this.saveSession(session);


    this.logger.log(`Session created: ${sessionId} for agent: ${agentId}`);
    return session;
  }

  async getSession(sessionId: string, organizationId: string): Promise<SessionMemoryEntity | null> {
    try {
      const sessionData = await this.sessionMemoryRepository.findOne({
        where: { id: sessionId },
      });
      
      if (!sessionData) {
        return null;
      }

      const session: SessionMemoryEntity = sessionData;
      
      // Verify organization access
      if (session.organizationId !== organizationId) {
        throw new Error('Unauthorized access to session');
      }

      return session;
    } catch (error) {
      this.logger.error(`Failed to get session ${sessionId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    }
  }

  async addMessage(sessionId: string, message: SessionMessage): Promise<void> {
    try {
      const session = await this.getSession(sessionId, message.metadata?.organizationId);
      
      if (!session) {
        throw new Error('Session not found');
      }

      // Add message
      session.messages.push(message);
      session.updatedAt = new Date();

      // Update token count (rough estimation)
      const messageTokens = this.estimateTokens(message.content);
      session.metadata.tokenCount += messageTokens;

      // Truncate if necessary
      if (session.metadata.tokenCount > session.metadata.maxTokens) {
        await this.truncateSession(session);
      }

      await this.saveSession(session);
      
      this.logger.debug(`Message added to session ${sessionId}, total messages: ${session.messages.length}`);
    } catch (error) {
      this.logger.error(`Failed to add message to session ${sessionId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }

  async updateContext(sessionId: string, organizationId: string, context: Record<string, any>): Promise<void> {
    try {
      const session = await this.getSession(sessionId, organizationId);
      
      if (!session) {
        throw new Error('Session not found');
      }

      session.context = { ...session.context, ...context };
      session.updatedAt = new Date();

      await this.saveSession(session);
    } catch (error) {
      this.logger.error(`Failed to update session context ${sessionId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }

  async getSessionHistory(sessionId: string, organizationId: string, limit?: number): Promise<SessionMessage[]> {
    try {
      const session = await this.getSession(sessionId, organizationId);
      
      if (!session) {
        return [];
      }

      const messages = session.messages;
      return limit ? messages.slice(-limit) : messages;
    } catch (error) {
      this.logger.error(`Failed to get session history ${sessionId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return [];
    }
  }

  async deleteSession(sessionId: string, organizationId: string): Promise<void> {
    try {
      const session = await this.getSession(sessionId, organizationId);
      
      if (!session) {
        throw new Error('Session not found');
      }

        await this.sessionMemoryRepository.delete(sessionId);
      this.logger.log(`Session deleted: ${sessionId}`);
    } catch (error) {
      this.logger.error(`Failed to delete session ${sessionId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }

  async getSessionsByAgent(agentId: string, organizationId: string): Promise<SessionMemoryEntity[]> {
    try {
      const pattern = `${this.SESSION_PREFIX}${organizationId}_${agentId}_*`;
      const keys = await this.sessionMemoryRepository.find({
        where: { id: Like(`${this.SESSION_PREFIX}${organizationId}_${agentId}_*`) },
      });
      
      const sessions: SessionMemoryEntity[] = [];
      
      for (const key of keys) {
        const sessionData = await this.sessionMemoryRepository.findOne({
          where: { id: key.id },
        });
        if (sessionData) {
          sessions.push(sessionData);
        }
      }

      return sessions.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
    } catch (error) {
      this.logger.error(`Failed to get sessions for agent ${agentId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return [];
    }
  }

  private async saveSession(session: SessionMemoryEntity): Promise<void> {
    await this.sessionMemoryRepository.save(session);
  }

  private async truncateSession(session: SessionMemoryEntity): Promise<void> {
    // Keep system messages and recent messages
    const systemMessages = session.messages.filter((m: SessionMessage) => m.role === 'system');
    const recentMessages = session.messages.filter((m: SessionMessage) => m.role !== 'system').slice(-10);
    
    session.messages = [...systemMessages, ...recentMessages];
    
    // Recalculate token count
    session.metadata.tokenCount = session.messages.reduce(
      (total: number, message: SessionMessage) => total + this.estimateTokens(message.content),
      0,
    );

    this.logger.debug(`Session ${session.id} truncated to ${session.messages.length} messages`);
  }

  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }

  async getSessionStats(organizationId: string): Promise<{
    totalSessions: number;
    activeSessions: number;
    totalMessages: number;
    averageSessionLength: number;
  }> {
    try {
      const pattern = `${this.SESSION_PREFIX}${organizationId}_*`;
      const keys = await this.sessionMemoryRepository.find({
        where: { id: Like(pattern) },
      });
      
      let totalMessages = 0;
      let activeSessions = 0;
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

      for (const key of keys) {
        const sessionData = await this.sessionMemoryRepository.findOne({
          where: { id: key.id },
        });
        if (sessionData) {
          const session: SessionMemoryEntity = sessionData;
          totalMessages += session.messages.length;
          
          if (new Date(session.updatedAt) > oneDayAgo) {
            activeSessions++;
          }
        }
      }

      return {
        totalSessions: keys.length,
        activeSessions,
        totalMessages,
        averageSessionLength: keys.length > 0 ? totalMessages / keys.length : 0,
      };
    } catch (error) {
      this.logger.error(`Failed to get session stats for organization ${organizationId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return {
        totalSessions: 0,
        activeSessions: 0,
        totalMessages: 0,
        averageSessionLength: 0,
      };
    }
  }
}