"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RedisService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const ioredis_1 = require("ioredis");
const common_2 = require("@nestjs/common");
let RedisService = RedisService_1 = class RedisService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_2.Logger(RedisService_1.name);
    }
    async onModuleInit() {
        this.redisClient = new ioredis_1.default({
            host: this.configService.get('app.redis.host'),
            port: this.configService.get('app.redis.port'),
            password: this.configService.get('app.redis.password'),
            db: this.configService.get('app.redis.db'),
            maxRetriesPerRequest: 3,
            lazyConnect: true,
            keepAlive: 30000,
            family: 4,
        });
        this.redisSubscriber = new ioredis_1.default({
            host: this.configService.get('app.redis.host'),
            port: this.configService.get('app.redis.port'),
            password: this.configService.get('app.redis.password'),
            db: this.configService.get('app.redis.db'),
            maxRetriesPerRequest: 3,
            lazyConnect: true,
            keepAlive: 30000,
            family: 4,
        });
        this.redisClient.on('connect', () => {
            this.logger.log('Redis client connected');
        });
        this.redisClient.on('error', (error) => {
            this.logger.error('Redis client error:', error);
        });
        this.redisSubscriber.on('connect', () => {
            this.logger.log('Redis subscriber connected');
        });
        this.redisSubscriber.on('error', (error) => {
            this.logger.error('Redis subscriber error:', error);
        });
        await this.redisClient.connect();
        await this.redisSubscriber.connect();
    }
    async onModuleDestroy() {
        if (this.redisClient) {
            await this.redisClient.quit();
        }
        if (this.redisSubscriber) {
            await this.redisSubscriber.quit();
        }
    }
    getClient() {
        return this.redisClient;
    }
    getSubscriber() {
        return this.redisSubscriber;
    }
    async set(key, value, ttl) {
        const prefix = this.configService.get('app.redis.prefix');
        const fullKey = `${prefix}${key}`;
        if (ttl) {
            await this.redisClient.setex(fullKey, ttl, value);
        }
        else {
            await this.redisClient.set(fullKey, value);
        }
    }
    async get(key) {
        const prefix = this.configService.get('app.redis.prefix');
        const fullKey = `${prefix}${key}`;
        return await this.redisClient.get(fullKey);
    }
    async del(key) {
        const prefix = this.configService.get('app.redis.prefix');
        const fullKey = `${prefix}${key}`;
        await this.redisClient.del(fullKey);
    }
    async exists(key) {
        const prefix = this.configService.get('app.redis.prefix');
        const fullKey = `${prefix}${key}`;
        const result = await this.redisClient.exists(fullKey);
        return result === 1;
    }
    async expire(key, ttl) {
        const prefix = this.configService.get('app.redis.prefix');
        const fullKey = `${prefix}${key}`;
        await this.redisClient.expire(fullKey, ttl);
    }
    async hset(key, field, value) {
        const prefix = this.configService.get('app.redis.prefix');
        const fullKey = `${prefix}${key}`;
        await this.redisClient.hset(fullKey, field, value);
    }
    async hget(key, field) {
        const prefix = this.configService.get('app.redis.prefix');
        const fullKey = `${prefix}${key}`;
        return await this.redisClient.hget(fullKey, field);
    }
    async hgetall(key) {
        const prefix = this.configService.get('app.redis.prefix');
        const fullKey = `${prefix}${key}`;
        return await this.redisClient.hgetall(fullKey);
    }
    async hdel(key, field) {
        const prefix = this.configService.get('app.redis.prefix');
        const fullKey = `${prefix}${key}`;
        await this.redisClient.hdel(fullKey, field);
    }
    async publish(channel, message) {
        await this.redisClient.publish(channel, message);
    }
    async subscribe(channel, callback) {
        await this.redisSubscriber.subscribe(channel);
        this.redisSubscriber.on('message', (receivedChannel, message) => {
            if (receivedChannel === channel) {
                callback(message);
            }
        });
    }
    async unsubscribe(channel) {
        await this.redisSubscriber.unsubscribe(channel);
    }
    async flushdb() {
        await this.redisClient.flushdb();
    }
    async ping() {
        return await this.redisClient.ping();
    }
    async info() {
        return await this.redisClient.info();
    }
};
exports.RedisService = RedisService;
exports.RedisService = RedisService = RedisService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], RedisService);
//# sourceMappingURL=redis.service.js.map