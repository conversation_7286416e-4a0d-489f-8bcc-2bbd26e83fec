import {
  AgentTemplate,
  AgentInstance,
  AgentExecution,
  AgentAnalytics,
  SessionMemory,
  SessionMessage,
  CreateAgentTemplateDto,
  CreateAgentInstanceDto,
  UpdateAgentInstanceDto,
  ExecuteAgentDto,
  ApiResponse
} from '../types/agent.types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

class AgentApiClient {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const token = localStorage.getItem('auth_token');

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  // Agent Templates
  async createTemplate(template: CreateAgentTemplateDto): Promise<ApiResponse<AgentTemplate>> {
    return this.request<AgentTemplate>('/api/v1/agent-templates', {
      method: 'POST',
      body: JSON.stringify(template),
    });
  }

  async getTemplates(): Promise<ApiResponse<AgentTemplate[]>> {
    return this.request<AgentTemplate[]>('/api/v1/agent-templates');
  }

  async getPublicTemplates(): Promise<ApiResponse<AgentTemplate[]>> {
    return this.request<AgentTemplate[]>('/api/v1/agent-templates/public');
  }

  async getTemplatesByCategory(category: string): Promise<ApiResponse<AgentTemplate[]>> {
    return this.request<AgentTemplate[]>(`/api/v1/agent-templates/category/${category}`);
  }

  async getTemplate(id: string): Promise<ApiResponse<AgentTemplate>> {
    return this.request<AgentTemplate>(`/api/v1/agent-templates/${id}`);
  }

  async updateTemplate(id: string, template: Partial<CreateAgentTemplateDto>): Promise<ApiResponse<AgentTemplate>> {
    return this.request<AgentTemplate>(`/api/v1/agent-templates/${id}`, {
      method: 'PUT',
      body: JSON.stringify(template),
    });
  }

  async deleteTemplate(id: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/api/v1/agent-templates/${id}`, {
      method: 'DELETE',
    });
  }

  // Agent Instances
  async createAgent(agent: CreateAgentInstanceDto): Promise<ApiResponse<AgentInstance>> {
    return this.request<AgentInstance>('/api/v1/agents', {
      method: 'POST',
      body: JSON.stringify(agent),
    });
  }

  async getAgents(): Promise<ApiResponse<AgentInstance[]>> {
    return this.request<AgentInstance[]>('/api/v1/agents');
  }

  async getAgent(id: string): Promise<ApiResponse<AgentInstance>> {
    return this.request<AgentInstance>(`/api/v1/agents/${id}`);
  }

  async updateAgent(id: string, agent: UpdateAgentInstanceDto): Promise<ApiResponse<AgentInstance>> {
    return this.request<AgentInstance>(`/api/v1/agents/${id}`, {
      method: 'PUT',
      body: JSON.stringify(agent),
    });
  }

  async deleteAgent(id: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/api/v1/agents/${id}`, {
      method: 'DELETE',
    });
  }

  async executeAgent(id: string, execution: ExecuteAgentDto): Promise<ApiResponse<AgentExecution>> {
    return this.request<AgentExecution>(`/api/v1/agents/${id}/execute`, {
      method: 'POST',
      body: JSON.stringify(execution),
    });
  }

  async getAgentExecutions(id: string): Promise<ApiResponse<AgentExecution[]>> {
    return this.request<AgentExecution[]>(`/api/v1/agents/${id}/executions`);
  }

  async getAgentAnalytics(id: string): Promise<ApiResponse<AgentAnalytics>> {
    return this.request<AgentAnalytics>(`/api/v1/agents/${id}/analytics`);
  }

  async getAgentSessions(id: string): Promise<ApiResponse<SessionMemory[]>> {
    return this.request<SessionMemory[]>(`/api/v1/agents/${id}/sessions`);
  }

  // Session Management
  async getSessionHistory(sessionId: string, limit?: number): Promise<ApiResponse<SessionMessage[]>> {
    const params = limit ? `?limit=${limit}` : '';
    return this.request<SessionMessage[]>(`/api/v1/agents/sessions/${sessionId}/history${params}`);
  }

  async deleteSession(sessionId: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/api/v1/agents/sessions/${sessionId}`, {
      method: 'DELETE',
    });
  }

  // Analytics
  async getOrganizationAnalytics(): Promise<ApiResponse<{
    totalAgents: number;
    totalExecutions: number;
    totalCost: number;
    averageSuccessRate: number;
    topPerformingAgents: Array<{
      agentId: string;
      name: string;
      executions: number;
      successRate: number;
    }>;
    providerUsage: Record<string, number>;
    dailyExecutions: Array<{
      date: string;
      executions: number;
      cost: number;
    }>;
  }>> {
    return this.request('/api/v1/agents/analytics/organization');
  }

  async getProviderAnalytics(): Promise<ApiResponse<Array<{
    provider: string;
    executions: number;
    successRate: number;
    averageResponseTime: number;
    totalCost: number;
    reliability: number;
  }>>> {
    return this.request('/api/v1/agents/analytics/providers');
  }
}

export const agentApi = new AgentApiClient();