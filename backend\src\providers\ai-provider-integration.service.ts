import { Injectable, Logger } from '@nestjs/common';
import { AIProviderManagerService } from './ai-provider-manager.service';
import { AIProviderSelectorService } from './ai-provider-selector.service';
import { ApixGateway } from '../websocket/apix.gateway';
import { ConfigService } from '@nestjs/config';
import { OpenAI } from 'openai';
import { Anthropic } from '@anthropic-ai/sdk';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { Mistral } from '@mistralai/mistralai';
import { Groq } from 'groq-sdk';

export interface AIRequest {
  requestId: string;
  providerId?: string;
  modelId?: string;
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  organizationId: string;
}

export interface AIResponse {
  requestId: string;
  providerId: string;
  modelId: string;
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  cost: number;
  latency: number;
  metadata?: Record<string, any>;
}

@Injectable()
export class AIProviderIntegrationService {
  private readonly logger = new Logger(AIProviderIntegrationService.name);
  private readonly providerClients = new Map<string, any>();

  constructor(
    private providerManager: AIProviderManagerService,
    private providerSelector: AIProviderSelectorService,
    private apixGateway: ApixGateway,
    private configService: ConfigService,
  ) { }

  async processRequest(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();
    let selectedProvider: any;
    let selectedModel: any;

    try {
      // Emit request start event
      this.apixGateway.emitToOrganization(request.organizationId, 'provider.request.start', {
        requestId: request.requestId,
        providerId: request.providerId,
        modelId: request.modelId,
        input: request.messages,
        timestamp: new Date(),
      });

      // Select provider if not specified
      if (!request.providerId) {
        const selection = await this.providerSelector.selectOptimalProvider({
          organizationId: request.organizationId,
          capabilities: ['chat'],
        });
        request.providerId = selection.providerId;
        request.modelId = selection.modelId;
      }

      // Get provider configuration
      selectedProvider = await this.providerManager.getCachedProvider(request.providerId);
      if (!selectedProvider) {
        selectedProvider = await this.providerManager.getProviderById(
          request.providerId,
          request.organizationId
        );
      }

      if (!selectedProvider || !selectedProvider.isActive) {
        throw new Error('Provider not available');
      }

      // Get model information
      selectedModel = selectedProvider.models?.find((m: any) => m.id === request.modelId) ||
        selectedProvider.models?.[0];

      if (!selectedModel) {
        throw new Error('Model not available');
      }

      // Process request with retry logic
      const response = await this.executeWithRetry(
        () => this.callProvider(selectedProvider, selectedModel, request),
        3, // max retries
        request.organizationId
      );

      const latency = Date.now() - startTime;

      // Calculate cost (simplified)
      const cost = this.calculateCost(
        selectedProvider.type,
        response.usage.promptTokens,
        response.usage.completionTokens
      );

      const finalResponse: AIResponse = {
        requestId: request.requestId,
        providerId: selectedProvider.id,
        modelId: selectedModel.id,
        content: response.content,
        usage: response.usage,
        cost,
        latency,
        metadata: response.metadata,
      };

      // Track usage and performance
      await this.providerManager.trackUsage(selectedProvider.id, {
        requests: 1,
        tokensUsed: response.usage.totalTokens,
        costInCents: Math.round(cost * 100),
      });

      await this.providerSelector.updateProviderPerformance(selectedProvider.id, {
        latency,
        cost,
        success: true,
      });

      // Emit completion event
      this.apixGateway.emitToOrganization(request.organizationId, 'provider.request.complete', {
        requestId: request.requestId,
        output: response.content,
        durationMs: latency,
        timestamp: new Date(),
      });

      this.logger.log(`Request ${request.requestId} completed successfully with provider ${selectedProvider.id}`);
      return finalResponse;

    } catch (error) {
      const latency = Date.now() - startTime;

      // Update performance metrics for failure
      if (selectedProvider) {
        await this.providerSelector.updateProviderPerformance(selectedProvider.id, {
          latency,
          cost: 0,
          success: false,
        });
      }

      // Emit error event
      this.apixGateway.emitToOrganization(request.organizationId, 'provider.request.error', {
        requestId: request.requestId,
        error: error instanceof Error ? error.message : String(error),
        retryable: this.isRetryableError(error),
        timestamp: new Date(),
      });

      this.logger.error(`Request ${request.requestId} failed: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  private async callProvider(provider: any, model: any, request: AIRequest): Promise<{
    content: string;
    usage: { promptTokens: number; completionTokens: number; totalTokens: number };
    metadata?: Record<string, any>;
  }> {
    switch (provider.type) {
      case 'OPENAI':
        return this.callOpenAI(provider, model, request);
      case 'CLAUDE':
        return this.callClaude(provider, model, request);
      case 'GEMINI':
        return this.callGemini(provider, model, request);
      case 'MISTRAL':
        return this.callMistral(provider, model, request);
      case 'GROQ':
        return this.callGroq(provider, model, request);
      default:
        throw new Error(`Unsupported provider type: ${provider.type}`);
    }
  }

  private async callOpenAI(provider: any, model: any, request: AIRequest): Promise<any> {
    const client = this.getOrCreateClient(provider.id, () =>
      new OpenAI({ apiKey: provider.config.apiKey })
    );

    const completion = await client.chat.completions.create({
      model: model.name,
      messages: request.messages,
      temperature: request.temperature || 0.7,
      max_tokens: request.maxTokens || 1000,
      stream: false,
    });

    return {
      content: completion.choices[0]?.message?.content || '',
      usage: {
        promptTokens: completion.usage?.prompt_tokens || 0,
        completionTokens: completion.usage?.completion_tokens || 0,
        totalTokens: completion.usage?.total_tokens || 0,
      },
      metadata: {
        model: completion.model,
        finishReason: completion.choices[0]?.finish_reason,
      },
    };
  }

  private async callClaude(provider: any, model: any, request: AIRequest): Promise<any> {
    const client = this.getOrCreateClient(provider.id, () =>
      new Anthropic({ apiKey: provider.config.apiKey })
    );

    // Convert messages format for Claude
    const systemMessage = request.messages.find(m => m.role === 'system');
    const conversationMessages = request.messages.filter(m => m.role !== 'system');

    const message = await client.messages.create({
      model: model.name,
      max_tokens: request.maxTokens || 1000,
      temperature: request.temperature || 0.7,
      system: systemMessage?.content,
      messages: conversationMessages.map(m => ({
        role: m.role as 'user' | 'assistant',
        content: m.content,
      })),
    });

    const content = message.content[0]?.type === 'text' ? message.content[0].text : '';

    return {
      content,
      usage: {
        promptTokens: message.usage.input_tokens,
        completionTokens: message.usage.output_tokens,
        totalTokens: message.usage.input_tokens + message.usage.output_tokens,
      },
      metadata: {
        model: message.model,
        stopReason: message.stop_reason,
      },
    };
  }

  private async callGemini(provider: any, model: any, request: AIRequest): Promise<any> {
    const apiKey = this.configService.get<string>('app.aiProviders.google.apiKey');
    if (!apiKey) {
      throw new Error('Google AI API key not configured');
    }

    const client = this.getOrCreateClient(provider.id, () => new GoogleGenerativeAI(apiKey));
    const genModel = client.getGenerativeModel({ model: model.name });

    // Convert messages to Gemini format
    const prompt = request.messages.map(msg => msg.content).join('\n');

    const result = await genModel.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    // Estimate tokens since Gemini doesn't provide detailed usage
    const estimatedInputTokens = Math.ceil(prompt.length / 4);
    const estimatedOutputTokens = Math.ceil(text.length / 4);

    return {
      content: text,
      usage: {
        promptTokens: estimatedInputTokens,
        completionTokens: estimatedOutputTokens,
        totalTokens: estimatedInputTokens + estimatedOutputTokens,
      },
      cost: this.calculateCost(provider.id, model.name, estimatedInputTokens + estimatedOutputTokens),
    };
  }

  private async callMistral(provider: any, model: any, request: AIRequest): Promise<any> {
    const client = this.getOrCreateClient(provider.id, () => new Mistral(provider.config.apiKey));

    const response = await client.chat({
      model: model.name,
      messages: request.messages,
      temperature: request.temperature || 0.7,
      maxTokens: request.maxTokens || 1000,
    });

    const choice = response.choices[0];
    const usage = response.usage;

    return {
      content: choice.message.content,
      usage: {
        promptTokens: usage.prompt_tokens,
        completionTokens: usage.completion_tokens,
        totalTokens: usage.total_tokens,
      },
      metadata: {
        model: response.model,
        finishReason: choice.finish_reason,
      },
    };
  }

  private async callGroq(provider: any, model: any, request: AIRequest): Promise<any> {
    const apiKey = this.configService.get<string>('app.aiProviders.groq.apiKey');
    if (!apiKey) {
      throw new Error('Groq API key not configured');
    }

    const client = this.getOrCreateClient(provider.id, () => new Groq({ apiKey }));

    const response = await client.chat.completions.create({
      model: model.name,
      messages: request.messages,
      temperature: request.temperature || 0.7,
      max_tokens: request.maxTokens || 1000,
    });

    const choice = response.choices[0];
    const usage = response.usage!;

    return {
      content: choice.message.content,
      usage: {
        promptTokens: usage.prompt_tokens,
        completionTokens: usage.completion_tokens,
        totalTokens: usage.total_tokens,
      },
      cost: this.calculateCost(provider.id, model.name, usage.total_tokens),
    };
  }

  private getOrCreateClient(providerId: string, clientFactory: () => any): any {
    if (!this.providerClients.has(providerId)) {
      this.providerClients.set(providerId, clientFactory());
    }
    return this.providerClients.get(providerId);
  }

  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number,
    organizationId: string,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (attempt === maxRetries || !this.isRetryableError(error)) {
          break;
        }

        this.logger.warn(`Attempt ${attempt} failed, retrying in ${delay}ms: ${error instanceof Error ? error.message : String(error)}`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2; // Exponential backoff
      }
    }

    throw lastError || new Error('Unknown error occurred');
  }

  private isRetryableError(error: any): boolean {
    // Define which errors are retryable
    const retryableErrors = [
      'ECONNRESET',
      'ETIMEDOUT',
      'ENOTFOUND',
      'rate_limit_exceeded',
      'server_error',
      'service_unavailable',
    ];

    return retryableErrors.some(retryableError =>
      error.message?.toLowerCase().includes(retryableError.toLowerCase()) ||
      error.code?.toLowerCase().includes(retryableError.toLowerCase())
    );
  }

  private calculateCost(providerType: string, promptTokens: number, completionTokens: number): number {
    // Simplified cost calculation - in production, this would use real pricing
    const costPer1KTokens = {
      OPENAI: { input: 0.0015, output: 0.002 },
      CLAUDE: { input: 0.008, output: 0.024 },
      GEMINI: { input: 0.00025, output: 0.0005 },
      MISTRAL: { input: 0.0007, output: 0.0007 },
      GROQ: { input: 0.0001, output: 0.0001 },
    };

    const pricing = costPer1KTokens[providerType as keyof typeof costPer1KTokens] || { input: 0.001, output: 0.001 };

    return (promptTokens / 1000) * pricing.input + (completionTokens / 1000) * pricing.output;
  }






}