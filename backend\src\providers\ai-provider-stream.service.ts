import { Injectable, Logger } from '@nestjs/common';
import { AIProviderManagerService } from './ai-provider-manager.service';
import { AIProviderSelectorService, ProviderSelectionRequest } from './ai-provider-selector.service';
import { AIProvider, AIModel } from '../database/entities/ai-provider.entity';
import { AIRequest } from './ai-provider-integration.service';
import { OpenAI } from 'openai';
import { Anthropic } from '@anthropic-ai/sdk';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { Mistral } from '@mistralai/mistralai';
import { Groq } from 'groq-sdk';

@Injectable()
export class AIProviderStreamService {
    private readonly logger = new Logger(AIProviderStreamService.name);
    private readonly providerClients = new Map<string, any>();

    constructor(
        private readonly aiProviderManager: AIProviderManagerService,
        private readonly aiProviderSelector: AIProviderSelectorService,
    ) { }

    async streamRequest(request: AIRequest): Promise<AsyncIterable<string>> {
        const startTime = Date.now();
        let selectedProvider: any;

        try {
            // Select provider if not specified
            if (!request.providerId) {
                const selection = await this.aiProviderSelector.selectOptimalProvider({
                    organizationId: request.organizationId,
                    capabilities: ['chat'],
                });
                selectedProvider = await this.aiProviderManager.getProviderById(selection.providerId, request.organizationId);
            } else {
                selectedProvider = await this.aiProviderManager.getProviderById(request.providerId, request.organizationId);
            }

            if (!selectedProvider) {
                throw new Error('No suitable provider available for streaming');
            }

            // Get model
            const model = selectedProvider.models.find((m: AIModel) => m.id === request.modelId) || selectedProvider.models[0];
            if (!model) {
                throw new Error('Model not available');
            }

            // Stream based on provider type
            switch (selectedProvider.type) {
                case 'OPENAI':
                    return this.streamOpenAI(selectedProvider, model, request);
                case 'CLAUDE':
                    return this.streamClaude(selectedProvider, model, request);
                case 'GEMINI':
                    return this.streamGemini(selectedProvider, model, request);
                case 'MISTRAL':
                    return this.streamMistral(selectedProvider, model, request);
                case 'GROQ':
                    return this.streamGroq(selectedProvider, model, request);
                default:
                    throw new Error(`Streaming not supported for provider type: ${selectedProvider.type}`);
            }
        } catch (error) {
            this.logger.error(`Streaming request failed: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }

    private async *streamOpenAI(provider: any, model: any, request: AIRequest): AsyncIterable<string> {
        const client = this.getOrCreateClient(provider.id, () => new OpenAI({
            apiKey: provider.config.apiKey,
        }));

        const stream = await client.chat.completions.create({
            model: model.name,
            messages: request.messages,
            temperature: request.temperature || 0.7,
            max_tokens: request.maxTokens || 1000,
            stream: true,
        });

        for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content;
            if (content) {
                yield content;
            }
        }
    }

    private async *streamClaude(provider: any, model: any, request: AIRequest): AsyncIterable<string> {
        const client = this.getOrCreateClient(provider.id, () => new Anthropic({
            apiKey: provider.config.apiKey,
        }));

        const stream = await client.messages.create({
            model: model.name,
            max_tokens: request.maxTokens || 1000,
            messages: request.messages,
            stream: true,
        });

        for await (const chunk of stream) {
            if (chunk.type === 'content_block_delta') {
                yield chunk.delta.text;
            }
        }
    }

    private async *streamGemini(provider: any, model: any, request: AIRequest): AsyncIterable<string> {
        const client = this.getOrCreateClient(provider.id, () => new GoogleGenerativeAI(provider.config.apiKey));

        const geminiModel = client.getGenerativeModel({ model: model.name });
        const result = await geminiModel.generateContentStream({
            contents: request.messages.map(msg => ({ role: msg.role, parts: [{ text: msg.content }] })),
            generationConfig: {
                temperature: request.temperature || 0.7,
                maxOutputTokens: request.maxTokens || 1000,
            },
        });

        for await (const chunk of result.stream) {
            const text = chunk.text();
            if (text) {
                yield text;
            }
        }
    }

    private async *streamMistral(provider: any, model: any, request: AIRequest): AsyncIterable<string> {
        const client = this.getOrCreateClient(provider.id, () => new Mistral(provider.config.apiKey));

        const stream = await client.chat({
            model: model.name,
            messages: request.messages,
            temperature: request.temperature || 0.7,
            maxTokens: request.maxTokens || 1000,
            stream: true,
        });

        for await (const chunk of stream) {
            if (chunk.choices[0]?.delta?.content) {
                yield chunk.choices[0].delta.content;
            }
        }
    }

    private async *streamGroq(provider: any, model: any, request: AIRequest): AsyncIterable<string> {
        const client = this.getOrCreateClient(provider.id, () => new Groq({
            apiKey: provider.config.apiKey,
        }));

        const stream = await client.chat.completions.create({
            model: model.name,
            messages: request.messages,
            temperature: request.temperature || 0.7,
            max_tokens: request.maxTokens || 1000,
            stream: true,
        });

        for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content;
            if (content) {
                yield content;
            }
        }
    }

    private getOrCreateClient(providerId: string, clientFactory: () => any): any {
        if (!this.providerClients.has(providerId)) {
            this.providerClients.set(providerId, clientFactory());
        }
        return this.providerClients.get(providerId);
    }

    async testProvider(providerId: string, organizationId: string): Promise<{
        success: boolean;
        latency: number;
        error?: string;
    }> {
        const startTime = Date.now();

        try {
            const testRequest: AIRequest = {
                requestId: `test_${Date.now()}`,
                providerId,
                messages: [{ role: 'user', content: 'Hello, this is a test message.' }],
                maxTokens: 50,
                organizationId,
            };

            // Test streaming
            const stream = await this.streamRequest(testRequest);
            let content = '';
            for await (const chunk of stream) {
                content += chunk;
            }

            return {
                success: true,
                latency: Date.now() - startTime,
            };
        } catch (error) {
            return {
                success: false,
                latency: Date.now() - startTime,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
}   