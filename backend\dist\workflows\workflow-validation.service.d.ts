import { WorkflowDefinitionDto } from './dto/workflow.dto';
import { CustomLoggerService } from '../logging/logger.service';
export declare class WorkflowValidationService {
    private readonly logger;
    constructor(logger: CustomLoggerService);
    validateWorkflowDefinition(definition: WorkflowDefinitionDto): Promise<void>;
    private validateStep;
    private validateTrigger;
    private validateErrorHandling;
    private validateDependencies;
    private hasCircularDependency;
    private validateStepTypeConfig;
    private validateTriggerTypeConfig;
    private validateHttpRequestConfig;
    private validateDataTransformationConfig;
    private validateConditionConfig;
    private validateLoopConfig;
    private validateWebhookConfig;
    private validateScheduledConfig;
    private validateEventConfig;
    private isValidVersion;
}
