"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Tool = exports.ToolPermission = exports.ToolStatus = exports.ToolType = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../users/user.entity");
const agent_entity_1 = require("../agents/agent.entity");
var ToolType;
(function (ToolType) {
    ToolType["HTTP_API"] = "http_api";
    ToolType["DATABASE"] = "database";
    ToolType["FILE_SYSTEM"] = "file_system";
    ToolType["EMAIL"] = "email";
    ToolType["SMS"] = "sms";
    ToolType["WEBHOOK"] = "webhook";
    ToolType["CALENDAR"] = "calendar";
    ToolType["CRM"] = "crm";
    ToolType["PAYMENT"] = "payment";
    ToolType["ANALYTICS"] = "analytics";
    ToolType["AI_MODEL"] = "ai_model";
    ToolType["CUSTOM"] = "custom";
})(ToolType || (exports.ToolType = ToolType = {}));
var ToolStatus;
(function (ToolStatus) {
    ToolStatus["DRAFT"] = "draft";
    ToolStatus["ACTIVE"] = "active";
    ToolStatus["INACTIVE"] = "inactive";
    ToolStatus["ERROR"] = "error";
    ToolStatus["DEPRECATED"] = "deprecated";
})(ToolStatus || (exports.ToolStatus = ToolStatus = {}));
var ToolPermission;
(function (ToolPermission) {
    ToolPermission["READ"] = "read";
    ToolPermission["WRITE"] = "write";
    ToolPermission["EXECUTE"] = "execute";
    ToolPermission["ADMIN"] = "admin";
})(ToolPermission || (exports.ToolPermission = ToolPermission = {}));
let Tool = class Tool extends typeorm_1.BaseEntity {
};
exports.Tool = Tool;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Tool.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Tool.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Tool.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ToolType,
        default: ToolType.CUSTOM,
    }),
    __metadata("design:type", String)
], Tool.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ToolStatus,
        default: ToolStatus.DRAFT,
    }),
    __metadata("design:type", String)
], Tool.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], Tool.prototype, "configuration", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Tool.prototype, "capabilities", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Tool.prototype, "performance", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Tool.prototype, "security", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Tool.prototype, "monitoring", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Tool.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Tool.prototype, "permissions", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Tool.prototype, "testing", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, user => user.tools, { onDelete: 'CASCADE' }),
    __metadata("design:type", user_entity_1.User)
], Tool.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Tool.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Tool.prototype, "organizationId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => agent_entity_1.Agent, agent => agent.tools, { nullable: true }),
    __metadata("design:type", agent_entity_1.Agent)
], Tool.prototype, "agent", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Tool.prototype, "agentId", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Tool.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Tool.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], Tool.prototype, "deletedAt", void 0);
exports.Tool = Tool = __decorate([
    (0, typeorm_1.Entity)('tools'),
    (0, typeorm_1.Index)(['userId', 'status']),
    (0, typeorm_1.Index)(['organizationId', 'type']),
    (0, typeorm_1.Index)(['agentId', 'status'])
], Tool);
//# sourceMappingURL=tool.entity.js.map