import { UniversalSDKService } from './universal-sdk.service';
import { SmartRoutingService } from './smart-routing.service';
import { ProviderAnalyticsService } from './provider-analytics.service';
import { LoggerService } from '../common/services/logger.service';
import { Observable } from 'rxjs';
export declare class ProviderManagementController {
    private universalSDK;
    private smartRouting;
    private analytics;
    private logger;
    constructor(universalSDK: UniversalSDKService, smartRouting: SmartRoutingService, analytics: ProviderAnalyticsService, logger: LoggerService);
    universalComplete(body: {
        prompt: string;
        provider?: string;
        model?: string;
        temperature?: number;
        maxTokens?: number;
        priority?: 'cost' | 'speed' | 'quality' | 'balanced';
        systemPrompt?: string;
        context?: Array<{
            role: 'user' | 'assistant' | 'system';
            content: string;
        }>;
        maxLatency?: number;
        maxCost?: number;
        tags?: string[];
    }, req: any): Promise<{
        success: boolean;
        data: import("./universal-sdk.service").UniversalAIResponse;
        error?: undefined;
    } | {
        success: boolean;
        error: string;
        data?: undefined;
    }>;
    universalStream(body: {
        prompt: string;
        provider?: string;
        model?: string;
        temperature?: number;
        maxTokens?: number;
        priority?: 'cost' | 'speed' | 'quality' | 'balanced';
        systemPrompt?: string;
        context?: Array<{
            role: 'user' | 'assistant' | 'system';
            content: string;
        }>;
    }, req: any): Promise<{
        success: boolean;
        data: {
            requestId: string;
            provider: string;
            model: string;
            streamUrl: string;
        };
        error?: undefined;
    } | {
        success: boolean;
        error: string;
        data?: undefined;
    }>;
    getOptimalProvider(priority: "cost" | "speed" | "quality" | "balanced" | undefined, maxLatency?: number, maxCost?: number, requiresStreaming?: boolean, req: any): Promise<{
        success: boolean;
        data: import("./smart-routing.service").RoutingResult;
        error?: undefined;
    } | {
        success: boolean;
        error: string;
        data?: undefined;
    }>;
    getProviderMetrics(req: any): Promise<{
        success: boolean;
        data: import("./smart-routing.service").ProviderMetrics[];
        error?: undefined;
    } | {
        success: boolean;
        error: string;
        data?: undefined;
    }>;
    getOrganizationAnalytics(timeframe: "hour" | "day" | "week" | "month" | undefined, req: any): Promise<{
        success: boolean;
        data: import("./provider-analytics.service").OrganizationUsageReport;
        error?: undefined;
    } | {
        success: boolean;
        error: string;
        data?: undefined;
    }>;
    getProviderAnalytics(providerId: string, timeframe: "hour" | "day" | "week" | "month" | undefined, req: any): Promise<{
        success: boolean;
        data: import("./provider-analytics.service").ProviderPerformanceReport;
        error?: undefined;
    } | {
        success: boolean;
        error: string;
        data?: undefined;
    }>;
    getAvailableModels(req: any): Promise<{
        success: boolean;
        data: {
            provider: string;
            models: Array<{
                id: string;
                name: string;
                capabilities: string[];
                costPerToken: number;
            }>;
        }[];
        error?: undefined;
    } | {
        success: boolean;
        error: string;
        data?: undefined;
    }>;
    getUsageStats(timeframe: "hour" | "day" | "week" | "month" | undefined, req: any): Promise<{
        success: boolean;
        data: {
            requests: any;
            totalCost: any;
            totalTokens: any;
            averageLatency: any;
            timeframe: "hour" | "day" | "week" | "month";
        };
        error?: undefined;
    } | {
        success: boolean;
        error: string;
        data?: undefined;
    }>;
    checkProviderHealth(providerId: string, req: any): Promise<{
        success: boolean;
        data: {
            healthy: boolean;
            latency: number;
            error?: string;
        };
        error?: undefined;
    } | {
        success: boolean;
        error: string;
        data?: undefined;
    }>;
    liveMetrics(req: any): Observable<any>;
    getDashboardData(req: any): Promise<{
        success: boolean;
        data: {
            metrics: import("./smart-routing.service").ProviderMetrics[];
            analytics: import("./provider-analytics.service").OrganizationUsageReport;
            models: {
                provider: string;
                models: Array<{
                    id: string;
                    name: string;
                    capabilities: string[];
                    costPerToken: number;
                }>;
            }[];
            usage: {
                requests: any;
                totalCost: any;
                totalTokens: any;
                averageLatency: any;
                timeframe: "hour" | "day" | "week" | "month";
            };
            lastUpdated: string;
        };
        error?: undefined;
    } | {
        success: boolean;
        error: string;
        data?: undefined;
    }>;
}
