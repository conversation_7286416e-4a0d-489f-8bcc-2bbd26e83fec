import { BaseEntity } from 'typeorm';
import { Agent } from './agent.entity';
import { Session } from './session.entity';
export declare enum ExecutionStatus {
    PENDING = "PENDING",
    RUNNING = "RUNNING",
    COMPLETED = "COMPLETED",
    FAILED = "FAILED",
    CANCELLED = "CANCELLED"
}
export declare class AgentExecution extends BaseEntity {
    id: string;
    agentId: string;
    agent: Agent;
    sessionId: string;
    session: Session;
    input: string;
    output: string;
    status: ExecutionStatus;
    context: Record<string, any>;
    metadata: {
        provider: string;
        model: string;
        tokens: {
            input: number;
            output: number;
            total: number;
            promptTokens: number;
            completionTokens: number;
            totalTokens: number;
        };
        cost: number;
        duration: number;
        retryCount: number;
    };
    errorMessage: string;
    errorDetails: Record<string, any>;
    organizationId: string;
    createdAt: Date;
    completedAt: Date;
    retryCount: number;
    duration: number;
    cost: number;
}
