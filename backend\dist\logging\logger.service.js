"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomLoggerService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const winston = require("winston");
const DailyRotateFile = require("winston-daily-rotate-file");
const path = require("path");
let CustomLoggerService = class CustomLoggerService {
    constructor(configService) {
        this.configService = configService;
        this.initializeLogger();
    }
    initializeLogger() {
        const logLevel = this.configService.get('app.logging.level') || 'info';
        const enableFileLogging = this.configService.get('app.logging.enableFileLogging');
        const logDirectory = this.configService.get('app.logging.logDirectory') || 'logs';
        const transports = [
            new winston.transports.Console({
                format: winston.format.combine(winston.format.timestamp(), winston.format.colorize(), winston.format.printf((_a) => {
                    var { timestamp, level, message, context, trace } = _a, meta = __rest(_a, ["timestamp", "level", "message", "context", "trace"]);
                    let log = `${timestamp} [${level}] ${context ? `[${context}] ` : ''}${message}`;
                    if (Object.keys(meta).length > 0) {
                        log += ` ${JSON.stringify(meta)}`;
                    }
                    if (trace) {
                        log += `\n${trace}`;
                    }
                    return log;
                })),
            }),
        ];
        if (enableFileLogging) {
            transports.push(new DailyRotateFile({
                filename: path.join(logDirectory, 'error-%DATE%.log'),
                datePattern: 'YYYY-MM-DD',
                level: 'error',
                maxSize: '20m',
                maxFiles: '14d',
                format: winston.format.combine(winston.format.timestamp(), winston.format.errors({ stack: true }), winston.format.json()),
            }));
            transports.push(new DailyRotateFile({
                filename: path.join(logDirectory, 'combined-%DATE%.log'),
                datePattern: 'YYYY-MM-DD',
                maxSize: '20m',
                maxFiles: '14d',
                format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
            }));
        }
        this.logger = winston.createLogger({
            level: logLevel,
            format: winston.format.combine(winston.format.timestamp(), winston.format.errors({ stack: true }), winston.format.json()),
            defaultMeta: { service: 'ai-platform' },
            transports,
        });
    }
    log(message, context) {
        this.logger.info(message, { context });
    }
    error(message, trace, context) {
        this.logger.error(message, { context, trace });
    }
    warn(message, context) {
        this.logger.warn(message, { context });
    }
    debug(message, context) {
        this.logger.debug(message, { context });
    }
    verbose(message, context) {
        this.logger.verbose(message, { context });
    }
    logWithMeta(message, meta, context) {
        this.logger.info(message, Object.assign({ context }, meta));
    }
    logErrorWithMeta(message, error, meta = {}, context) {
        this.logger.error(message, Object.assign({ context, error: {
                name: error.name,
                message: error.message,
                stack: error.stack,
            } }, meta));
    }
    logPerformance(operation, duration, meta = {}, context) {
        this.logger.info(`Performance: ${operation}`, Object.assign({ context,
            operation,
            duration }, meta));
    }
    logSecurity(event, meta = {}, context) {
        this.logger.warn(`Security Event: ${event}`, Object.assign({ context,
            event }, meta));
    }
    logDatabase(query, duration, meta = {}, context) {
        this.logger.debug(`Database Query: ${query}`, Object.assign({ context,
            query,
            duration }, meta));
    }
    logApiRequest(method, url, statusCode, duration, meta = {}, context) {
        this.logger.info(`API Request: ${method} ${url}`, Object.assign({ context,
            method,
            url,
            statusCode,
            duration }, meta));
    }
};
exports.CustomLoggerService = CustomLoggerService;
exports.CustomLoggerService = CustomLoggerService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], CustomLoggerService);
//# sourceMappingURL=logger.service.js.map