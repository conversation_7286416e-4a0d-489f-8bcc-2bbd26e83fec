"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExecutionController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const swagger_1 = require("@nestjs/swagger");
const agent_runtime_service_1 = require("../agents/execution/agent-runtime.service");
const tool_runtime_service_1 = require("../tools/execution/tool-runtime.service");
const workflow_runtime_service_1 = require("../workflows/execution/workflow-runtime.service");
const ai_provider_integration_service_1 = require("../providers/ai-provider-integration.service");
const logger_service_1 = require("../common/services/logger.service");
let ExecutionController = class ExecutionController {
    constructor(agentRuntime, toolRuntime, workflowRuntime, aiProvider, logger) {
        this.agentRuntime = agentRuntime;
        this.toolRuntime = toolRuntime;
        this.workflowRuntime = workflowRuntime;
        this.aiProvider = aiProvider;
        this.logger = logger;
    }
    async executeAgent(agentId, body, req) {
        try {
            const context = {
                agentId,
                input: body.input,
                sessionId: body.sessionId,
                variables: body.variables,
                tools: body.tools,
            };
            const result = await this.agentRuntime.executeAgent(context);
            this.logger.business('agent_executed', {
                agentId,
                userId: req.user.id,
                organizationId: req.user.organizationId,
                success: result.success,
                duration: result.duration,
            });
            return {
                success: true,
                data: result,
            };
        }
        catch (error) {
            this.logger.error('Agent execution failed', {
                agentId,
                userId: req.user.id,
                error: error instanceof Error ? error.message : 'Unknown error',
            });
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    async executeTool(toolId, body, req) {
        try {
            const context = {
                toolId,
                parameters: body.parameters,
                agentId: body.agentId,
                sessionId: body.sessionId,
                timeout: body.timeout,
            };
            const result = await this.toolRuntime.executeTool(context);
            this.logger.business('tool_executed', {
                toolId,
                userId: req.user.id,
                organizationId: req.user.organizationId,
                success: result.success,
                duration: result.duration,
            });
            return {
                success: true,
                data: result,
            };
        }
        catch (error) {
            this.logger.error('Tool execution failed', {
                toolId,
                userId: req.user.id,
                error: error instanceof Error ? error.message : 'Unknown error',
            });
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    async executeWorkflow(workflowId, body, req) {
        try {
            const context = {
                workflowId,
                input: body.input,
                variables: body.variables,
                sessionId: body.sessionId,
                userId: req.user.id,
                organizationId: req.user.organizationId,
            };
            const result = await this.workflowRuntime.executeWorkflow(context);
            this.logger.business('workflow_executed', {
                workflowId,
                userId: req.user.id,
                organizationId: req.user.organizationId,
                success: result.success,
                duration: result.duration,
                steps: result.steps.length,
            });
            return {
                success: true,
                data: result,
            };
        }
        catch (error) {
            this.logger.error('Workflow execution failed', {
                workflowId,
                userId: req.user.id,
                error: error instanceof Error ? error.message : 'Unknown error',
            });
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    async aiComplete(body, req) {
        try {
            const request = {
                requestId: `req_${Date.now()}`,
                providerId: body.provider,
                modelId: body.model,
                messages: [{ role: 'user', content: body.prompt }],
                temperature: body.temperature,
                maxTokens: body.maxTokens,
                stream: body.stream,
                organizationId: req.user.organizationId,
            };
            const result = await this.aiProvider.processRequest(request);
            this.logger.business('ai_completion', {
                provider: result.providerId,
                model: result.modelId,
                userId: req.user.id,
                organizationId: req.user.organizationId,
                tokens: result.usage.totalTokens,
                cost: result.cost,
            });
            return {
                success: true,
                data: result,
            };
        }
        catch (error) {
            this.logger.error('AI completion failed', {
                userId: req.user.id,
                error: error instanceof Error ? error.message : 'Unknown error',
            });
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    async getAgentTools(agentId, req) {
        try {
            const tools = await this.toolRuntime.getAvailableTools(agentId);
            return {
                success: true,
                data: tools,
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    async getProviders(req) {
        try {
            const providers = this.aiProvider.getAvailableProviders();
            return {
                success: true,
                data: providers,
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    async healthCheck() {
        return {
            success: true,
            data: {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                services: {
                    agentRuntime: 'operational',
                    toolRuntime: 'operational',
                    workflowRuntime: 'operational',
                    aiProviders: 'operational',
                },
            },
        };
    }
};
exports.ExecutionController = ExecutionController;
__decorate([
    (0, common_1.Post)('agents/:agentId/execute'),
    (0, swagger_1.ApiOperation)({ summary: 'Execute an AI agent' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Agent executed successfully' }),
    __param(0, (0, common_1.Param)('agentId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ExecutionController.prototype, "executeAgent", null);
__decorate([
    (0, common_1.Post)('tools/:toolId/execute'),
    (0, swagger_1.ApiOperation)({ summary: 'Execute a tool' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Tool executed successfully' }),
    __param(0, (0, common_1.Param)('toolId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ExecutionController.prototype, "executeTool", null);
__decorate([
    (0, common_1.Post)('workflows/:workflowId/execute'),
    (0, swagger_1.ApiOperation)({ summary: 'Execute a workflow' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Workflow executed successfully' }),
    __param(0, (0, common_1.Param)('workflowId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ExecutionController.prototype, "executeWorkflow", null);
__decorate([
    (0, common_1.Post)('ai/complete'),
    (0, swagger_1.ApiOperation)({ summary: 'Direct AI completion' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'AI completion successful' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ExecutionController.prototype, "aiComplete", null);
__decorate([
    (0, common_1.Get)('agents/:agentId/tools'),
    (0, swagger_1.ApiOperation)({ summary: 'Get available tools for an agent' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Tools retrieved successfully' }),
    __param(0, (0, common_1.Param)('agentId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ExecutionController.prototype, "getAgentTools", null);
__decorate([
    (0, common_1.Get)('providers'),
    (0, swagger_1.ApiOperation)({ summary: 'Get available AI providers' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Providers retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ExecutionController.prototype, "getProviders", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({ summary: 'Check execution engine health' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Health check successful' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ExecutionController.prototype, "healthCheck", null);
exports.ExecutionController = ExecutionController = __decorate([
    (0, swagger_1.ApiTags)('execution'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('execution'),
    __metadata("design:paramtypes", [agent_runtime_service_1.AgentRuntimeService,
        tool_runtime_service_1.ToolRuntimeService,
        workflow_runtime_service_1.WorkflowRuntimeService,
        ai_provider_integration_service_1.AIProviderIntegrationService,
        logger_service_1.LoggerService])
], ExecutionController);
//# sourceMappingURL=execution.controller.js.map