import { Injectable, CanActivate, ExecutionContext, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { Redis } from 'ioredis';

export interface RateLimitOptions {
  ttl: number; // Time window in seconds
  limit: number; // Max requests per window
  skipIf?: (request: Request) => boolean;
  keyGenerator?: (request: Request) => string;
}

export const RATE_LIMIT_KEY = 'rate-limit';

export const RateLimit = (options: RateLimitOptions) => {
  return (target: any, propertyKey?: string | symbol, descriptor?: PropertyDescriptor) => {
    if (descriptor && propertyKey) {
      Reflector.createDecorator<RateLimitOptions>()(options)(target, propertyKey, descriptor);
    } else {
      Reflector.createDecorator<RateLimitOptions>()(options)(target);
    }
  };
};

@Injectable()
export class RateLimitGuard implements CanActivate {
  private redis: Redis;

  constructor(
    private reflector: Reflector,
    private configService: ConfigService,
  ) {
    const redisUrl = this.configService.get<string>('app.redis.url');
    if (!redisUrl) {
      throw new Error('Redis URL is required for rate limiting');
    }
    this.redis = new Redis(redisUrl);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();

    // Get rate limit options from decorator or use defaults
    const options = this.reflector.getAllAndOverride<RateLimitOptions>(RATE_LIMIT_KEY, [
      context.getHandler(),
      context.getClass(),
    ]) || {
      ttl: this.configService.get<number>('app.rateLimit.ttl', 60),
      limit: this.configService.get<number>('app.rateLimit.max', 100),
    };

    // Skip rate limiting if condition is met
    if (options.skipIf && options.skipIf(request)) {
      return true;
    }

    // Generate key for rate limiting
    const key = options.keyGenerator
      ? options.keyGenerator(request)
      : this.getDefaultKey(request);

    const redisKey = `rate-limit:${key}`;

    try {
      // Get current count
      const current = await this.redis.get(redisKey);
      const count = current ? parseInt(current, 10) : 0;

      if (count >= options.limit) {
        throw new HttpException(
          {
            statusCode: HttpStatus.TOO_MANY_REQUESTS,
            message: 'Too many requests',
            error: 'Rate limit exceeded',
          },
          HttpStatus.TOO_MANY_REQUESTS,
        );
      }

      // Increment counter
      const pipeline = this.redis.pipeline();
      pipeline.incr(redisKey);

      if (count === 0) {
        pipeline.expire(redisKey, options.ttl);
      }

      await pipeline.exec();

      return true;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      // If Redis is down, allow the request but log the error
      console.error('Rate limiting error:', error);
      return true;
    }
  }

  private getDefaultKey(request: Request): string {
    // Use IP address and user ID (if authenticated) for rate limiting
    const ip = request.ip || request.connection.remoteAddress || 'unknown';
    const userId = (request as any).user?.id || 'anonymous';
    return `${ip}:${userId}`;
  }
}

// Decorator for easy use
export const ApiRateLimit = (options: Partial<RateLimitOptions> = {}) => {
  const defaultOptions: RateLimitOptions = {
    ttl: 60, // 1 minute
    limit: 100, // 100 requests per minute
    ...options,
  };

  return RateLimit(defaultOptions);
};
