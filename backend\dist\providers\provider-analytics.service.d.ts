import { PrismaService } from '../prisma/prisma.service';
import { LoggerService } from '../common/services/logger.service';
export interface ProviderPerformanceReport {
    providerId: string;
    providerName: string;
    timeframe: string;
    metrics: {
        totalRequests: number;
        successfulRequests: number;
        failedRequests: number;
        successRate: number;
        averageLatency: number;
        p95Latency: number;
        p99Latency: number;
        totalTokens: number;
        totalCost: number;
        costPerToken: number;
        availability: number;
    };
    trends: {
        requestsChange: number;
        latencyChange: number;
        costChange: number;
        successRateChange: number;
    };
    recommendations: string[];
}
export interface OrganizationUsageReport {
    organizationId: string;
    timeframe: string;
    summary: {
        totalRequests: number;
        totalCost: number;
        totalTokens: number;
        averageLatency: number;
        topProvider: string;
        topModel: string;
    };
    providerBreakdown: Array<{
        provider: string;
        requests: number;
        cost: number;
        tokens: number;
        percentage: number;
    }>;
    costOptimization: {
        potentialSavings: number;
        recommendations: string[];
    };
}
export declare class ProviderAnalyticsService {
    private prisma;
    private loggerService;
    private readonly logger;
    constructor(prisma: PrismaService, loggerService: LoggerService);
    generateProviderReport(providerId: string, timeframe?: 'hour' | 'day' | 'week' | 'month'): Promise<ProviderPerformanceReport>;
    generateOrganizationReport(organizationId: string, timeframe?: 'hour' | 'day' | 'week' | 'month'): Promise<OrganizationUsageReport>;
    private calculateProviderMetrics;
    private calculateTrends;
    private generateRecommendations;
    private analyzeCostOptimization;
    private getTimeframes;
    private getEmptyMetrics;
    private getEmptyReport;
    private groupBy;
    private calculateAvailability;
    generateDailyReports(): Promise<void>;
}
