import { AgentTemplatesService, CreateTemplateDto, TemplateCategory } from './agent-templates.service';
import { AgentTemplate } from '../database/entities/agent.entity';
export declare class AgentTemplatesController {
    private readonly templatesService;
    constructor(templatesService: AgentTemplatesService);
    createTemplate(createTemplateDto: CreateTemplateDto, req: any): Promise<AgentTemplate>;
    getTemplates(req: any): Promise<AgentTemplate[]>;
    getTemplateCategories(req: any): Promise<TemplateCategory[]>;
    getTemplatesByCategory(category: string, req: any): Promise<AgentTemplate[]>;
    searchTemplates(query: string, req: any): Promise<AgentTemplate[]>;
    getPopularTemplates(limit: number | undefined, req: any): Promise<AgentTemplate[]>;
    getTemplate(id: string, req: any): Promise<AgentTemplate>;
    updateTemplate(id: string, updateData: Partial<CreateTemplateDto>, req: any): Promise<AgentTemplate>;
    deleteTemplate(id: string, req: any): Promise<void>;
    duplicateTemplate(id: string, req: any): Promise<AgentTemplate>;
    getTemplateStats(id: string, req: any): Promise<any>;
    seedDefaultTemplates(req: any): Promise<void>;
}
