import { LoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
export declare class CustomLoggerService implements LoggerService {
    private configService;
    private logger;
    constructor(configService: ConfigService);
    private initializeLogger;
    log(message: string, context?: string): void;
    error(message: string, trace?: string, context?: string): void;
    warn(message: string, context?: string): void;
    debug(message: string, context?: string): void;
    verbose(message: string, context?: string): void;
    logWithMeta(message: string, meta: Record<string, any>, context?: string): void;
    logErrorWithMeta(message: string, error: Error, meta?: Record<string, any>, context?: string): void;
    logPerformance(operation: string, duration: number, meta?: Record<string, any>, context?: string): void;
    logSecurity(event: string, meta?: Record<string, any>, context?: string): void;
    logDatabase(query: string, duration: number, meta?: Record<string, any>, context?: string): void;
    logApiRequest(method: string, url: string, statusCode: number, duration: number, meta?: Record<string, any>, context?: string): void;
}
