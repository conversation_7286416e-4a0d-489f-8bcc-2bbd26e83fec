"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SmartRoutingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SmartRoutingService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const logger_service_1 = require("../common/services/logger.service");
const config_1 = require("@nestjs/config");
let SmartRoutingService = SmartRoutingService_1 = class SmartRoutingService {
    constructor(prisma, loggerService, configService) {
        this.prisma = prisma;
        this.loggerService = loggerService;
        this.configService = configService;
        this.logger = new common_1.Logger(SmartRoutingService_1.name);
        this.metricsCache = new Map();
        this.cacheExpiry = 5 * 60 * 1000;
        setInterval(() => this.updateMetricsCache(), this.cacheExpiry);
        this.updateMetricsCache();
    }
    async selectOptimalProvider(request) {
        try {
            const providers = await this.getAvailableProviders(request.organizationId);
            if (providers.length === 0) {
                throw new Error('No providers available for organization');
            }
            const providerMetrics = await this.getProviderMetrics(providers.map(p => p.id));
            const scoredProviders = this.scoreProviders(providers, providerMetrics, request);
            scoredProviders.sort((a, b) => b.score - a.score);
            const best = scoredProviders[0];
            const fallbacks = scoredProviders.slice(1, 4).map(p => ({
                providerId: p.provider.id,
                modelId: p.model.id,
                confidence: p.score,
            }));
            this.loggerService.business('provider_selected', {
                organizationId: request.organizationId,
                selectedProvider: best.provider.id,
                selectedModel: best.model.id,
                score: best.score,
                priority: request.priority,
                fallbackCount: fallbacks.length,
            });
            return {
                providerId: best.provider.id,
                modelId: best.model.id,
                confidence: best.score,
                reasoning: best.reasoning,
                fallbacks,
            };
        }
        catch (error) {
            this.logger.error(`Provider selection failed: ${error}`);
            throw error;
        }
    }
    async getAvailableProviders(organizationId) {
        return await this.prisma.aIProvider.findMany({
            where: {
                organizationId,
                isActive: true,
            },
            include: {
                aiModels: {
                    where: { isActive: true },
                },
            },
        });
    }
    async getProviderMetrics(providerIds) {
        const metrics = new Map();
        for (const providerId of providerIds) {
            const cached = this.metricsCache.get(providerId);
            if (cached && Date.now() - cached.lastUpdated.getTime() < this.cacheExpiry) {
                metrics.set(providerId, cached);
                continue;
            }
            const calculated = await this.calculateProviderMetrics(providerId);
            metrics.set(providerId, calculated);
            this.metricsCache.set(providerId, calculated);
        }
        return metrics;
    }
    async calculateProviderMetrics(providerId) {
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
        const executions = await this.prisma.agentExecution.findMany({
            where: {
                provider: providerId,
                createdAt: { gte: oneHourAgo },
            },
            select: {
                duration: true,
                status: true,
                cost: true,
                tokens: true,
            },
        });
        if (executions.length === 0) {
            return {
                providerId,
                averageLatency: 2000,
                successRate: 0.95,
                costPerToken: 0.000002,
                availability: 1.0,
                lastUpdated: new Date(),
            };
        }
        const successful = executions.filter(e => e.status === 'COMPLETED');
        const totalTokens = executions.reduce((sum, e) => { var _a; return sum + ((_a = e.tokens) === null || _a === void 0 ? void 0 : _a.total) || 0; }, 0);
        const totalCost = executions.reduce((sum, e) => sum + e.cost, 0);
        return {
            providerId,
            averageLatency: successful.reduce((sum, e) => sum + e.duration, 0) / successful.length,
            successRate: successful.length / executions.length,
            costPerToken: totalTokens > 0 ? totalCost / totalTokens : 0.000002,
            availability: this.calculateAvailability(executions),
            lastUpdated: new Date(),
        };
    }
    calculateAvailability(executions) {
        const recentFailures = executions.filter(e => e.status === 'FAILED' &&
            Date.now() - new Date(e.createdAt).getTime() < 15 * 60 * 1000);
        if (recentFailures.length === 0)
            return 1.0;
        if (recentFailures.length >= 5)
            return 0.5;
        return 0.9;
    }
    scoreProviders(providers, metrics, request) {
        var _a;
        const scored = [];
        for (const provider of providers) {
            const providerMetrics = metrics.get(provider.id);
            if (!providerMetrics)
                continue;
            for (const model of provider.aiModels) {
                if (request.requiresStreaming && !((_a = model.capabilities) === null || _a === void 0 ? void 0 : _a.streaming))
                    continue;
                if (request.maxLatency && providerMetrics.averageLatency > request.maxLatency)
                    continue;
                if (request.maxCost && providerMetrics.costPerToken > request.maxCost)
                    continue;
                const score = this.calculateScore(providerMetrics, request, model);
                const reasoning = this.generateReasoning(providerMetrics, request, score);
                scored.push({
                    provider,
                    model,
                    score,
                    reasoning,
                });
            }
        }
        return scored;
    }
    calculateScore(metrics, request, model) {
        let score = 0;
        score += metrics.availability * 30;
        score += metrics.successRate * 30;
        switch (request.priority) {
            case 'speed':
                score += (5000 - Math.min(metrics.averageLatency, 5000)) / 5000 * 40;
                break;
            case 'cost':
                score += (0.00001 - Math.min(metrics.costPerToken, 0.00001)) / 0.00001 * 40;
                break;
            case 'quality':
                score += (model.qualityScore || 70) / 100 * 40;
                break;
            case 'balanced':
                score += (5000 - Math.min(metrics.averageLatency, 5000)) / 5000 * 15;
                score += (0.00001 - Math.min(metrics.costPerToken, 0.00001)) / 0.00001 * 15;
                score += (model.qualityScore || 70) / 100 * 10;
                break;
        }
        return Math.min(score, 100);
    }
    generateReasoning(metrics, request, score) {
        const reasons = [];
        if (metrics.successRate > 0.98)
            reasons.push('high reliability');
        if (metrics.averageLatency < 1000)
            reasons.push('fast response');
        if (metrics.costPerToken < 0.000001)
            reasons.push('low cost');
        if (metrics.availability === 1.0)
            reasons.push('full availability');
        const priority = request.priority === 'balanced' ? 'balanced performance' : `optimized for ${request.priority}`;
        return `Selected for ${priority}. Score: ${score.toFixed(1)}. ${reasons.join(', ')}.`;
    }
    async updateMetricsCache() {
        try {
            const providers = await this.prisma.aIProvider.findMany({
                where: { isActive: true },
                select: { id: true },
            });
            for (const provider of providers) {
                const metrics = await this.calculateProviderMetrics(provider.id);
                this.metricsCache.set(provider.id, metrics);
            }
            this.logger.log(`Updated metrics cache for ${providers.length} providers`);
        }
        catch (error) {
            this.logger.error(`Failed to update metrics cache: ${error}`);
        }
    }
    async getProviderMetricsForOrganization(organizationId) {
        const providers = await this.getAvailableProviders(organizationId);
        const metrics = await this.getProviderMetrics(providers.map(p => p.id));
        return Array.from(metrics.values());
    }
    async checkProviderHealth(providerId) {
        try {
            const startTime = Date.now();
            const latency = Date.now() - startTime;
            return {
                healthy: true,
                latency,
            };
        }
        catch (error) {
            return {
                healthy: false,
                latency: -1,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
};
exports.SmartRoutingService = SmartRoutingService;
exports.SmartRoutingService = SmartRoutingService = SmartRoutingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        logger_service_1.LoggerService,
        config_1.ConfigService])
], SmartRoutingService);
//# sourceMappingURL=smart-routing.service.js.map