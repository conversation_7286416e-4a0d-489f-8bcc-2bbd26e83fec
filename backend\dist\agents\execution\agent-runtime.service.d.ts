import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { LoggerService } from '../../common/services/logger.service';
export interface AgentExecutionContext {
    agentId: string;
    sessionId?: string;
    input: string;
    variables?: Record<string, any>;
    tools?: string[];
    memory?: {
        shortTerm: any[];
        longTerm: any[];
        context: Record<string, any>;
    };
}
export interface AgentExecutionResult {
    success: boolean;
    output: string;
    tokens: {
        input: number;
        output: number;
        total: number;
    };
    cost: number;
    duration: number;
    provider: string;
    model: string;
    error?: string;
    metadata?: Record<string, any>;
}
export interface AgentMemory {
    shortTerm: Array<{
        role: 'user' | 'assistant' | 'system';
        content: string;
        timestamp: Date;
    }>;
    longTerm: Array<{
        summary: string;
        importance: number;
        timestamp: Date;
    }>;
    context: Record<string, any>;
}
export declare class AgentRuntimeService {
    private prisma;
    private configService;
    private loggerService;
    private readonly logger;
    constructor(prisma: PrismaService, configService: ConfigService, loggerService: LoggerService);
    executeAgent(context: AgentExecutionContext): Promise<AgentExecutionResult>;
    private loadAgent;
    private prepareExecutionContext;
    private loadAgentMemory;
    private runAgentLogic;
    private buildPrompt;
    private getProviderService;
    private updateAgentMemory;
    private logExecution;
}
