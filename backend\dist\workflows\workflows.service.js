"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const workflow_entity_1 = require("./workflow.entity");
const workflow_validation_service_1 = require("./workflow-validation.service");
const workflow_analytics_service_1 = require("./workflow-analytics.service");
const logger_service_1 = require("../logging/logger.service");
let WorkflowsService = class WorkflowsService {
    constructor(workflowRepository, validationService, analyticsService, logger) {
        this.workflowRepository = workflowRepository;
        this.validationService = validationService;
        this.analyticsService = analyticsService;
        this.logger = logger;
    }
    async createWorkflow(createWorkflowDto, userId, organizationId) {
        try {
            await this.validationService.validateWorkflowDefinition(createWorkflowDto.definition);
            const workflow = this.workflowRepository.create(Object.assign(Object.assign({}, createWorkflowDto), { userId,
                organizationId, status: workflow_entity_1.WorkflowStatus.DRAFT }));
            const savedWorkflow = await this.workflowRepository.save(workflow);
            this.logger.log(`Workflow created: ${savedWorkflow.id} by user: ${userId}`, 'WorkflowsService');
            return savedWorkflow;
        }
        catch (error) {
            this.logger.error(`Failed to create workflow: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowsService');
            throw error;
        }
    }
    async getWorkflows(filters, userId, organizationId) {
        try {
            const queryBuilder = this.buildWorkflowQuery(filters, userId, organizationId);
            const [workflows, total] = await queryBuilder.getManyAndCount();
            return { workflows, total };
        }
        catch (error) {
            this.logger.error(`Failed to get workflows: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowsService');
            throw error;
        }
    }
    async getWorkflowById(id, userId, organizationId) {
        try {
            const workflow = await this.workflowRepository.findOne({
                where: { id, organizationId },
                relations: ['user', 'agents'],
            });
            if (!workflow) {
                throw new common_1.NotFoundException(`Workflow with ID ${id} not found`);
            }
            if (workflow.userId !== userId && !this.hasViewPermission(workflow, userId)) {
                throw new common_1.ForbiddenException('Access denied to this workflow');
            }
            return workflow;
        }
        catch (error) {
            this.logger.error(`Failed to get workflow ${id}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowsService');
            throw error;
        }
    }
    async updateWorkflow(id, updateWorkflowDto, userId, organizationId) {
        try {
            const workflow = await this.getWorkflowById(id, userId, organizationId);
            if (workflow.userId !== userId && !this.hasEditPermission(workflow, userId)) {
                throw new common_1.ForbiddenException('Edit access denied to this workflow');
            }
            if (updateWorkflowDto.definition) {
                await this.validationService.validateWorkflowDefinition(updateWorkflowDto.definition);
            }
            if (workflow.status === workflow_entity_1.WorkflowStatus.ACTIVE && !updateWorkflowDto.forceUpdate) {
                throw new common_1.BadRequestException('Cannot update active workflow without force flag');
            }
            Object.assign(workflow, updateWorkflowDto);
            const updatedWorkflow = await this.workflowRepository.save(workflow);
            this.logger.log(`Workflow updated: ${id} by user: ${userId}`, 'WorkflowsService');
            return updatedWorkflow;
        }
        catch (error) {
            this.logger.error(`Failed to update workflow ${id}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowsService');
            throw error;
        }
    }
    async deleteWorkflow(id, userId, organizationId) {
        try {
            const workflow = await this.getWorkflowById(id, userId, organizationId);
            if (workflow.userId !== userId && !this.hasAdminPermission(workflow, userId)) {
                throw new common_1.ForbiddenException('Delete access denied to this workflow');
            }
            workflow.deletedAt = new Date();
            await this.workflowRepository.save(workflow);
            this.logger.log(`Workflow deleted: ${id} by user: ${userId}`, 'WorkflowsService');
        }
        catch (error) {
            this.logger.error(`Failed to delete workflow ${id}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowsService');
            throw error;
        }
    }
    async changeWorkflowStatus(id, status, userId, organizationId) {
        try {
            const workflow = await this.getWorkflowById(id, userId, organizationId);
            if (workflow.userId !== userId && !this.hasEditPermission(workflow, userId)) {
                throw new common_1.ForbiddenException('Status change access denied to this workflow');
            }
            if (!this.isValidStatusTransition(workflow.status, status)) {
                throw new common_1.BadRequestException(`Invalid status transition from ${workflow.status} to ${status}`);
            }
            workflow.status = status;
            if (status === workflow_entity_1.WorkflowStatus.ACTIVE) {
                workflow.lastExecutedAt = new Date();
            }
            const updatedWorkflow = await this.workflowRepository.save(workflow);
            this.logger.log(`Workflow status changed: ${id} to ${status} by user: ${userId}`, 'WorkflowsService');
            return updatedWorkflow;
        }
        catch (error) {
            this.logger.error(`Failed to change workflow status ${id}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowsService');
            throw error;
        }
    }
    async duplicateWorkflow(id, userId, organizationId) {
        try {
            const originalWorkflow = await this.getWorkflowById(id, userId, organizationId);
            const duplicatedWorkflow = new workflow_entity_1.Workflow();
            duplicatedWorkflow.name = `${originalWorkflow.name} (Copy)`;
            duplicatedWorkflow.description = originalWorkflow.description;
            duplicatedWorkflow.definition = originalWorkflow.definition;
            duplicatedWorkflow.metadata = originalWorkflow.metadata;
            duplicatedWorkflow.permissions = originalWorkflow.permissions;
            duplicatedWorkflow.status = workflow_entity_1.WorkflowStatus.DRAFT;
            duplicatedWorkflow.executionCount = 0;
            duplicatedWorkflow.successCount = 0;
            duplicatedWorkflow.failureCount = 0;
            duplicatedWorkflow.averageExecutionTime = 0;
            duplicatedWorkflow.totalCost = 0;
            duplicatedWorkflow.lastExecutedAt = new Date();
            duplicatedWorkflow.nextExecutionAt = new Date();
            duplicatedWorkflow.executionHistory = [];
            duplicatedWorkflow.userId = userId;
            duplicatedWorkflow.organizationId = organizationId;
            const savedWorkflow = await this.workflowRepository.save(duplicatedWorkflow);
            this.logger.log(`Workflow duplicated: ${id} to ${savedWorkflow.id} by user: ${userId}`, 'WorkflowsService');
            return savedWorkflow;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorStack = error instanceof Error ? error.stack : '';
            this.logger.error(`Failed to duplicate workflow ${id}: ${errorMessage}`, errorStack, 'WorkflowsService');
            throw error;
        }
    }
    async getWorkflowAnalytics(id, userId, organizationId) {
        try {
            const workflow = await this.getWorkflowById(id, userId, organizationId);
            return await this.analyticsService.getWorkflowAnalytics(workflow);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorStack = error instanceof Error ? error.stack : '';
            this.logger.error(`Failed to get workflow analytics ${id}: ${errorMessage}`, errorStack, 'WorkflowsService');
            throw error;
        }
    }
    buildWorkflowQuery(filters, userId, organizationId) {
        const queryBuilder = this.workflowRepository.createQueryBuilder('workflow')
            .leftJoinAndSelect('workflow.user', 'user')
            .where('workflow.organizationId = :organizationId', { organizationId })
            .andWhere('workflow.deletedAt IS NULL');
        if (filters.status) {
            queryBuilder.andWhere('workflow.status = :status', { status: filters.status });
        }
        if (filters.search) {
            queryBuilder.andWhere('(workflow.name ILIKE :search OR workflow.description ILIKE :search)', { search: `%${filters.search}%` });
        }
        if (filters.userId) {
            queryBuilder.andWhere('workflow.userId = :userId', { userId: filters.userId });
        }
        if (filters.createdAfter) {
            queryBuilder.andWhere('workflow.createdAt >= :createdAfter', { createdAfter: filters.createdAfter });
        }
        if (filters.createdBefore) {
            queryBuilder.andWhere('workflow.createdAt <= :createdBefore', { createdBefore: filters.createdBefore });
        }
        if (filters.page && filters.limit) {
            const offset = (filters.page - 1) * filters.limit;
            queryBuilder.skip(offset).take(filters.limit);
        }
        const sortField = filters.sortBy || 'createdAt';
        const sortOrder = filters.sortOrder || 'DESC';
        queryBuilder.orderBy(`workflow.${sortField}`, sortOrder);
        return queryBuilder;
    }
    hasViewPermission(workflow, userId) {
        var _a, _b, _c;
        return ((_b = (_a = workflow.permissions) === null || _a === void 0 ? void 0 : _a.viewers) === null || _b === void 0 ? void 0 : _b.includes(userId)) || ((_c = workflow.permissions) === null || _c === void 0 ? void 0 : _c.public);
    }
    hasEditPermission(workflow, userId) {
        var _a, _b;
        return ((_b = (_a = workflow.permissions) === null || _a === void 0 ? void 0 : _a.editors) === null || _b === void 0 ? void 0 : _b.includes(userId)) || workflow.userId === userId;
    }
    hasAdminPermission(workflow, userId) {
        var _a, _b;
        return ((_b = (_a = workflow.permissions) === null || _a === void 0 ? void 0 : _a.owners) === null || _b === void 0 ? void 0 : _b.includes(userId)) || workflow.userId === userId;
    }
    isValidStatusTransition(currentStatus, newStatus) {
        var _a;
        const validTransitions = {
            [workflow_entity_1.WorkflowStatus.DRAFT]: [workflow_entity_1.WorkflowStatus.ACTIVE, workflow_entity_1.WorkflowStatus.COMPLETED, workflow_entity_1.WorkflowStatus.FAILED, workflow_entity_1.WorkflowStatus.CANCELLED, workflow_entity_1.WorkflowStatus.PAUSED],
            [workflow_entity_1.WorkflowStatus.ACTIVE]: [workflow_entity_1.WorkflowStatus.PAUSED, workflow_entity_1.WorkflowStatus.COMPLETED, workflow_entity_1.WorkflowStatus.CANCELLED, workflow_entity_1.WorkflowStatus.FAILED],
            [workflow_entity_1.WorkflowStatus.PAUSED]: [workflow_entity_1.WorkflowStatus.ACTIVE, workflow_entity_1.WorkflowStatus.CANCELLED, workflow_entity_1.WorkflowStatus.FAILED],
            [workflow_entity_1.WorkflowStatus.COMPLETED]: [workflow_entity_1.WorkflowStatus.ACTIVE, workflow_entity_1.WorkflowStatus.FAILED],
            [workflow_entity_1.WorkflowStatus.FAILED]: [workflow_entity_1.WorkflowStatus.ACTIVE, workflow_entity_1.WorkflowStatus.CANCELLED],
            [workflow_entity_1.WorkflowStatus.CANCELLED]: [workflow_entity_1.WorkflowStatus.ACTIVE, workflow_entity_1.WorkflowStatus.FAILED],
        };
        return ((_a = validTransitions[currentStatus]) === null || _a === void 0 ? void 0 : _a.includes(newStatus)) || false;
    }
    async getRealTimeMetrics(id, userId, organizationId) {
        try {
            const workflow = await this.getWorkflowById(id, userId, organizationId);
            return await this.analyticsService.getRealTimeMetrics(workflow.id);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorStack = error instanceof Error ? error.stack : '';
            this.logger.error(`Failed to get real time metrics ${id}: ${errorMessage}`, errorStack, 'WorkflowsService');
            throw error;
        }
    }
};
exports.WorkflowsService = WorkflowsService;
exports.WorkflowsService = WorkflowsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(workflow_entity_1.Workflow)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        workflow_validation_service_1.WorkflowValidationService,
        workflow_analytics_service_1.WorkflowAnalyticsService,
        logger_service_1.CustomLoggerService])
], WorkflowsService);
//# sourceMappingURL=workflows.service.js.map