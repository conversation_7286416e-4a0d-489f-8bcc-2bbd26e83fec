import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, Index, BaseEntity } from 'typeorm';
import { User } from '../users/user.entity';
import { Agent } from '../agents/agent.entity';

export enum WorkflowStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export enum WorkflowTriggerType {
  MANUAL = 'manual',
  SCHEDULED = 'scheduled',
  WEBHOOK = 'webhook',
  EVENT = 'event',
  API = 'api',
}

@Entity('workflows')
@Index(['userId', 'status'])
@Index(['organizationId', 'createdAt'])
@Index(['organizationId', 'status'])
export class Workflow extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  name!: string;

  @Column({ type: 'text', nullable: true })
  description!: string;

  @Column({ type: 'jsonb' })
  definition!: {
    version: string;
    steps: Array<{
      id: string;
      type: string;
      name: string;
      config: Record<string, any>;
      dependencies?: string[];
      conditions?: Record<string, any>;
    }>;
    triggers: Array<{
      type: WorkflowTriggerType;
      config: Record<string, any>;
    }>;
    variables: Record<string, any>;
    errorHandling: {
      retryCount: number;
      retryDelay: number;
      fallbackSteps?: string[];
    };
  };

  @Column({
    type: 'enum',
    enum: WorkflowStatus,
    default: WorkflowStatus.DRAFT,
  })
  status!: WorkflowStatus;

  @Column({ type: 'jsonb', nullable: true })
  executionHistory!: Array<{
    stepId: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    startTime: Date;
    endTime?: Date;
    result?: any;
    error?: string;
  }>;

  @Column({ type: 'jsonb', nullable: true })
  metadata!: {
    tags: string[];
    category: string;
    priority: 'low' | 'medium' | 'high' | 'critical';
    estimatedDuration: number;
    costEstimate: number;
  };

  @Column({ type: 'jsonb', nullable: true })
  permissions!: {
    owners: string[];
    editors: string[];
    viewers: string[];
    public: boolean;
  };

  @Column({ nullable: true })
  lastExecutedAt!: Date;

  @Column({ nullable: true })
  nextExecutionAt!: Date;

  @Column({ type: 'int', default: 0 })
  executionCount!: number;

  @Column({ type: 'int', default: 0 })
  successCount!: number;

  @Column({ type: 'int', default: 0 })
  failureCount!: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  averageExecutionTime!: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  totalCost!: number;

  @ManyToOne(() => User, user => user.workflows, { onDelete: 'CASCADE' })
  user!: User;

  @Column()
  userId!: string;

  @Column()
  organizationId!: string;

  @OneToMany(() => Agent, agent => agent.workflow)
  agents!: Agent[];

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  @Column({ nullable: true })
  deletedAt!: Date;
} 