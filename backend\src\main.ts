import { NestFactory } from '@nestjs/core';
import { <PERSON><PERSON><PERSON>Pipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import helmet from 'helmet';
import * as compression from 'compression';
import { AppModule } from './app.module';
import { CustomLoggerService } from './logging/logger.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: ['error', 'warn', 'log', 'debug', 'verbose'],
  });

  const configService = app.get(ConfigService);
  const logger = app.get(CustomLoggerService);

  // Global prefix
  const apiPrefix = configService.get('app.api.prefix');
  const apiVersion = configService.get('app.api.version');
  app.setGlobalPrefix(`${apiPrefix}/${apiVersion}`);

  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
    crossOriginEmbedderPolicy: false,
  }));

  // Compression
  app.use(compression());

  // CORS
  const corsOrigins = configService.get('app.security.corsOrigins');
  app.enableCors({
    origin: corsOrigins,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-Tenant-ID'],
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
      errorHttpStatusCode: 422,
    }),
  );

  // Swagger documentation (only in non-production)
  const swaggerEnabled = configService.get('app.api.swaggerEnabled');
  if (swaggerEnabled) {
    const config = new DocumentBuilder()
      .setTitle('AI Agent Orchestration Platform API')
      .setDescription('Revolutionary AI Agent Orchestration Platform with Multi-Tenant Support')
      .setVersion('1.0.0')
      .addBearerAuth()
      .addTag('auth', 'Authentication endpoints')
      .addTag('users', 'User management endpoints')
      .addTag('agents', 'AI Agent management endpoints')
      .addTag('workflows', 'Workflow orchestration endpoints')
      .addTag('health', 'Health check endpoints')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
      },
    });
  }

  const port = configService.get('app.port') || 3000;
  const environment = configService.get('app.environment');

  await app.listen(port, '0.0.0.0');

  logger.logWithMeta('Application started successfully', {
    port,
    environment,
    apiPrefix: `${apiPrefix}/${apiVersion}`,
    swaggerEnabled,
    corsOrigins,
  }, 'Bootstrap');

  // Graceful shutdown
  process.on('SIGTERM', async () => {
    logger.log('SIGTERM received, shutting down gracefully', 'Bootstrap');
    await app.close();
    process.exit(0);
  });

  process.on('SIGINT', async () => {
    logger.log('SIGINT received, shutting down gracefully', 'Bootstrap');
    await app.close();
    process.exit(0);
  });
}

bootstrap().catch((error) => {
  console.error('Failed to start application:', error);
  process.exit(1);
});