import { Is<PERSON><PERSON>, IsS<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsE<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum SystemRole {
    SUPER_ADMIN = 'SUPER_ADMIN',
    ORG_ADMIN = 'ORG_ADMIN',
    DEVELOPER = 'DEVELOPER',
    VIEWER = 'VIEWER',
    USER = 'USER',
}

export class RegisterDto {
    @ApiProperty({
        description: 'User email address',
        example: '<EMAIL>',
    })
    @IsEmail({}, { message: 'Please provide a valid email address' })
    email!: string;

    @ApiProperty({
        description: 'User password',
        example: 'securePassword123',
        minLength: 8,
        maxLength: 128,
    })
    @IsString({ message: 'Password must be a string' })
    @MinLength(8, { message: 'Password must be at least 8 characters long' })
    @MaxLength(128, { message: 'Password must not exceed 128 characters' })
    password!: string;

    @ApiProperty({
        description: 'User full name',
        example: '<PERSON>',
        minLength: 2,
        maxLength: 100,
    })
    @IsString({ message: 'Name must be a string' })
    @MinLength(2, { message: 'Name must be at least 2 characters long' })
    @MaxLength(100, { message: 'Name must not exceed 100 characters' })
    name!: string;

    @ApiProperty({
        description: 'System role for the user',
        enum: SystemRole,
        default: SystemRole.USER,
        required: false,
    })
    @IsOptional()
    @IsEnum(SystemRole, { message: 'Invalid system role' })
    systemRole?: SystemRole = SystemRole.USER;
} 