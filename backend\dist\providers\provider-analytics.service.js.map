{"version": 3, "file": "provider-analytics.service.js", "sourceRoot": "", "sources": ["../../src/providers/provider-analytics.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,6DAAyD;AACzD,sEAAkE;AAClE,+CAAwD;AAqDjD,IAAM,wBAAwB,gCAA9B,MAAM,wBAAwB;IAGnC,YACU,MAAqB,EACrB,aAA4B;QAD5B,WAAM,GAAN,MAAM,CAAe;QACrB,kBAAa,GAAb,aAAa,CAAe;QAJrB,WAAM,GAAG,IAAI,eAAM,CAAC,0BAAwB,CAAC,IAAI,CAAC,CAAC;IAKjE,CAAC;IAEJ,KAAK,CAAC,sBAAsB,CAC1B,UAAkB,EAClB,YAA+C,KAAK;QAEpD,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAG/D,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAG9E,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;QAG9F,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC;YACvD,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;YACzB,MAAM,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;SACvB,CAAC,CAAC;QAGH,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;QAGrE,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;QAE7E,OAAO;YACL,UAAU;YACV,YAAY,EAAE,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,KAAI,SAAS;YACzC,SAAS;YACT,OAAO,EAAE,cAAc;YACvB,MAAM;YACN,eAAe;SAChB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,0BAA0B,CAC9B,cAAsB,EACtB,YAA+C,KAAK;QAEpD,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAGhD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YACpD,KAAK,EAAE;gBACL,cAAc;gBACd,SAAS,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE;gBACzB,MAAM,EAAE,WAAW;aACpB;YACD,MAAM,EAAE;gBACN,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE,IAAI;gBACV,MAAM,EAAE,IAAI;gBACZ,OAAO,EAAE,IAAI;aACd;SACF,CAAC,CAAC;QAEH,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QACxD,CAAC;QAGD,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC;QACtC,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACnE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAC/C,MAAM,MAAM,GAAG,GAAG,CAAC,MAAa,CAAC;YACjC,OAAO,GAAG,GAAG,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,KAAI,CAAC,CAAC,CAAC;QAC1C,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC;QAG3F,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAC1D,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACpD,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAC9D,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC5D,CAAC;QACF,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACxD,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACtD,CAAC;QAGF,MAAM,iBAAiB,GAAG,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;YAChF,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAClE,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBAC9C,MAAM,MAAM,GAAG,GAAG,CAAC,MAAa,CAAC;gBACjC,OAAO,GAAG,GAAG,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,KAAI,CAAC,CAAC,CAAC;YAC1C,CAAC,EAAE,CAAC,CAAC,CAAC;YAEN,OAAO;gBACL,QAAQ;gBACR,QAAQ,EAAE,IAAI,CAAC,MAAM;gBACrB,IAAI,EAAE,YAAY;gBAClB,MAAM,EAAE,cAAc;gBACtB,UAAU,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,GAAG,GAAG;aAChD,CAAC;QACJ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;QAG3C,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;QAEtF,OAAO;YACL,cAAc;YACd,SAAS;YACT,OAAO,EAAE;gBACP,aAAa;gBACb,SAAS;gBACT,WAAW;gBACX,cAAc;gBACd,WAAW;gBACX,QAAQ;aACT;YACD,iBAAiB;YACjB,gBAAgB;SACjB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,UAAkB,EAAE,KAAW,EAAE,KAAY;QAClF,MAAM,WAAW,GAAQ;YACvB,QAAQ,EAAE,UAAU;YACpB,SAAS,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE;SAC1B,CAAC;QAEF,IAAI,KAAK,EAAE,CAAC;YACV,WAAW,CAAC,SAAS,CAAC,EAAE,GAAG,KAAK,CAAC;QACnC,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YACpD,KAAK,EAAE,WAAW;YAClB,MAAM,EAAE;gBACN,MAAM,EAAE,IAAI;gBACZ,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,IAAI;gBACV,MAAM,EAAE,IAAI;aACb;SACF,CAAC,CAAC;QAEH,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;QAChC,CAAC;QAED,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC;QAClE,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC;QAE3D,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACvE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAC/C,MAAM,MAAM,GAAG,GAAG,CAAC,MAAa,CAAC;YACjC,OAAO,GAAG,GAAG,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,KAAI,CAAC,CAAC,CAAC;QAC1C,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAEnE,OAAO;YACL,aAAa,EAAE,QAAQ,CAAC,MAAM;YAC9B,kBAAkB,EAAE,UAAU,CAAC,MAAM;YACrC,cAAc,EAAE,MAAM,CAAC,MAAM;YAC7B,WAAW,EAAE,UAAU,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM;YAChD,cAAc,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM;YAC3E,UAAU,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC;YAC/D,UAAU,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC;YAC/D,WAAW;YACX,SAAS;YACT,YAAY,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAC3D,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC;SACnD,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,OAAY,EAAE,QAAa;QACjD,MAAM,UAAU,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;QAE/E,OAAO;YACL,cAAc,EAAE,UAAU,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,aAAa,CAAC;YACzE,aAAa,EAAE,UAAU,CAAC,OAAO,CAAC,cAAc,EAAE,QAAQ,CAAC,cAAc,CAAC;YAC1E,UAAU,EAAE,UAAU,CAAC,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC;YAC7D,iBAAiB,EAAE,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC,WAAW,CAAC;SACzE,CAAC;IACJ,CAAC;IAEO,uBAAuB,CAAC,OAAY,EAAE,MAAW;QACvD,MAAM,eAAe,GAAa,EAAE,CAAC;QAGrC,IAAI,OAAO,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC;YAC/B,eAAe,CAAC,IAAI,CAAC,mEAAmE,CAAC,CAAC;QAC5F,CAAC;QAGD,IAAI,OAAO,CAAC,cAAc,GAAG,IAAI,EAAE,CAAC;YAClC,eAAe,CAAC,IAAI,CAAC,qFAAqF,CAAC,CAAC;QAC9G,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,GAAG,EAAE,EAAE,CAAC;YAC9B,eAAe,CAAC,IAAI,CAAC,oEAAoE,CAAC,CAAC;QAC7F,CAAC;QAGD,IAAI,MAAM,CAAC,UAAU,GAAG,EAAE,EAAE,CAAC;YAC3B,eAAe,CAAC,IAAI,CAAC,sFAAsF,CAAC,CAAC;QAC/G,CAAC;QAED,IAAI,OAAO,CAAC,YAAY,GAAG,OAAO,EAAE,CAAC;YACnC,eAAe,CAAC,IAAI,CAAC,oEAAoE,CAAC,CAAC;QAC7F,CAAC;QAGD,IAAI,OAAO,CAAC,YAAY,GAAG,IAAI,EAAE,CAAC;YAChC,eAAe,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;QAClG,CAAC;QAED,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,eAAe,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;QAC5E,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,QAAe,EAAE,cAAsB;QAE3E,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAGnE,MAAM,gBAAgB,GAAG,SAAS,GAAG,GAAG,CAAC;QAEzC,MAAM,eAAe,GAAG;YACtB,gEAAgE;YAChE,yDAAyD;YACzD,2CAA2C;YAC3C,sCAAsC;SACvC,CAAC;QAEF,OAAO;YACL,gBAAgB;YAChB,eAAe;SAChB,CAAC;IACJ,CAAC;IAEO,aAAa,CAAC,SAAiB;QACrC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,CAAC;YACP,GAAG,EAAE,EAAE;YACP,IAAI,EAAE,EAAE,GAAG,CAAC;YACZ,KAAK,EAAE,EAAE,GAAG,EAAE;SACf,CAAC;QAEF,MAAM,KAAK,GAAG,OAAO,CAAC,SAAiC,CAAC,IAAI,EAAE,CAAC;QAC/D,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAC/D,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAEzE,OAAO,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC;IAClC,CAAC;IAEO,eAAe;QACrB,OAAO;YACL,aAAa,EAAE,CAAC;YAChB,kBAAkB,EAAE,CAAC;YACrB,cAAc,EAAE,CAAC;YACjB,WAAW,EAAE,CAAC;YACd,cAAc,EAAE,CAAC;YACjB,UAAU,EAAE,CAAC;YACb,UAAU,EAAE,CAAC;YACb,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,CAAC;YACZ,YAAY,EAAE,CAAC;YACf,YAAY,EAAE,CAAC;SAChB,CAAC;IACJ,CAAC;IAEO,cAAc,CAAC,cAAsB,EAAE,SAAiB;QAC9D,OAAO;YACL,cAAc;YACd,SAAS;YACT,OAAO,EAAE;gBACP,aAAa,EAAE,CAAC;gBAChB,SAAS,EAAE,CAAC;gBACZ,WAAW,EAAE,CAAC;gBACd,cAAc,EAAE,CAAC;gBACjB,WAAW,EAAE,MAAM;gBACnB,QAAQ,EAAE,MAAM;aACjB;YACD,iBAAiB,EAAE,EAAE;YACrB,gBAAgB,EAAE;gBAChB,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,CAAC,oDAAoD,CAAC;aACxE;SACF,CAAC;IACJ,CAAC;IAEO,OAAO,CAAI,KAAU,EAAE,GAAY;QACzC,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;YACnC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzB,OAAO,MAAM,CAAC;QAChB,CAAC,EAAE,EAAyB,CAAC,CAAC;IAChC,CAAC;IAEO,qBAAqB,CAAC,QAAe;QAC3C,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACzC,CAAC,CAAC,MAAM,KAAK,QAAQ;YACrB,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAC9D,CAAC;QAEF,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,GAAG,CAAC;QAC5C,IAAI,cAAc,CAAC,MAAM,IAAI,CAAC;YAAE,OAAO,GAAG,CAAC;QAC3C,OAAO,IAAI,CAAC;IACd,CAAC;IAIK,AAAN,KAAK,CAAC,oBAAoB;QACxB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;YAE9D,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;gBAC5D,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;aACjC,CAAC,CAAC;YAEH,KAAK,MAAM,GAAG,IAAI,aAAa,EAAE,CAAC;gBAChC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;gBAGpE,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC;oBACnC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,kBAAkB,EAAE;wBAC9C,cAAc,EAAE,GAAG,CAAC,EAAE;wBACtB,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,SAAS;wBACnC,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,aAAa;qBACvC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,aAAa,CAAC,MAAM,gBAAgB,CAAC,CAAC;QACvF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,KAAK,EAAE,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;CACF,CAAA;AArVY,4DAAwB;AA2T7B;IADL,IAAA,eAAI,EAAC,yBAAc,CAAC,qBAAqB,CAAC;;;;oEA0B1C;mCApVU,wBAAwB;IADpC,IAAA,mBAAU,GAAE;qCAKO,8BAAa;QACN,8BAAa;GAL3B,wBAAwB,CAqVpC"}