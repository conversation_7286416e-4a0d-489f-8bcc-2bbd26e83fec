"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const bcrypt = require("bcrypt");
const user_entity_1 = require("../users/entities/user.entity");
const role_entity_1 = require("../roles/entities/role.entity");
const permission_entity_1 = require("../permissions/entities/permission.entity");
const tenant_entity_1 = require("../tenants/entities/tenant.entity");
const refresh_token_entity_1 = require("./entities/refresh-token.entity");
let AuthService = class AuthService {
    constructor(userRepository, roleRepository, permissionRepository, tenantRepository, refreshTokenRepository, jwtService) {
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
        this.permissionRepository = permissionRepository;
        this.tenantRepository = tenantRepository;
        this.refreshTokenRepository = refreshTokenRepository;
        this.jwtService = jwtService;
    }
    async validateUser(email, password) {
        const user = await this.userRepository.findOne({
            where: { email },
            relations: ['roles', 'roles.permissions', 'tenant'],
        });
        if (user && await this.verifyPassword(password, user.password)) {
            const { password } = user, result = __rest(user, ["password"]);
            return result;
        }
        return null;
    }
    async login(loginDto, tenantId) {
        var _a;
        const { email, password } = loginDto;
        const user = await this.userRepository.findOne({
            where: { email },
            relations: ['roles', 'roles.permissions', 'tenant'],
        });
        if (!user || !(await this.verifyPassword(password, user.password))) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        if (!user.isActive) {
            throw new common_1.UnauthorizedException('Account is deactivated');
        }
        if (tenantId && ((_a = user.tenant) === null || _a === void 0 ? void 0 : _a.id) !== tenantId) {
            throw new common_1.ForbiddenException('Access denied to this tenant');
        }
        await this.userRepository.update(user.id, {
            lastLoginAt: new Date(),
        });
        const tokens = await this.generateTokens(user);
        await this.saveRefreshToken(user.id, tokens.refreshToken);
        return Object.assign({ user: {
                id: user.id,
                email: user.email,
                name: user.name,
                roles: user.roles.map(role => ({
                    id: role.id,
                    name: role.name,
                    permissions: role.permissions.map(permission => ({
                        id: permission.id,
                        name: permission.name,
                        resource: permission.resource,
                        action: permission.action,
                    })),
                })),
                tenant: user.tenant ? {
                    id: user.tenant.id,
                    name: user.tenant.name,
                    domain: user.tenant.domain,
                } : null,
            } }, tokens);
    }
    async register(registerDto, tenantId) {
        const { email, password, name, systemRole = 'USER' } = registerDto;
        const existingUser = await this.userRepository.findOne({
            where: { email },
        });
        if (existingUser) {
            throw new common_1.BadRequestException('User with this email already exists');
        }
        const hashedPassword = await this.hashPassword(password);
        const defaultRole = await this.roleRepository.findOne({
            where: { name: systemRole },
            relations: ['permissions'],
        });
        if (!defaultRole) {
            throw new common_1.BadRequestException(`Role ${systemRole} not found`);
        }
        const user = this.userRepository.create({
            email,
            password: hashedPassword,
            name,
            roles: [defaultRole],
            isActive: true,
        });
        if (tenantId) {
            const tenant = await this.tenantRepository.findOne({
                where: { id: tenantId },
            });
            if (tenant) {
                user.tenant = tenant;
            }
        }
        const savedUser = await this.userRepository.save(user);
        const tokens = await this.generateTokens(savedUser);
        await this.saveRefreshToken(savedUser.id, tokens.refreshToken);
        return Object.assign({ user: {
                id: savedUser.id,
                email: savedUser.email,
                name: savedUser.name,
                roles: savedUser.roles.map(role => ({
                    id: role.id,
                    name: role.name,
                    permissions: role.permissions.map(permission => ({
                        id: permission.id,
                        name: permission.name,
                        resource: permission.resource,
                        action: permission.action,
                    })),
                })),
                tenant: savedUser.tenant ? {
                    id: savedUser.tenant.id,
                    name: savedUser.tenant.name,
                    domain: savedUser.tenant.domain,
                } : null,
            } }, tokens);
    }
    async refreshToken(refreshTokenDto) {
        const { refreshToken } = refreshTokenDto;
        try {
            const payload = this.jwtService.verify(refreshToken, {
                secret: process.env.JWT_REFRESH_SECRET,
            });
            const storedToken = await this.refreshTokenRepository.findOne({
                where: { token: refreshToken, isRevoked: false },
                relations: ['user'],
            });
            if (!storedToken) {
                throw new common_1.UnauthorizedException('Invalid refresh token');
            }
            if (new Date() > storedToken.expiresAt) {
                await this.revokeRefreshToken(refreshToken);
                throw new common_1.UnauthorizedException('Refresh token expired');
            }
            const tokens = await this.generateTokens(storedToken.user);
            await this.revokeRefreshToken(refreshToken);
            await this.saveRefreshToken(storedToken.user.id, tokens.refreshToken);
            return tokens;
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid refresh token');
        }
    }
    async logout(userId, refreshToken) {
        if (refreshToken) {
            await this.revokeRefreshToken(refreshToken);
        }
        await this.refreshTokenRepository.update({ userId, isRevoked: false }, { isRevoked: true });
        return { message: 'Successfully logged out' };
    }
    async changePassword(userId, changePasswordDto) {
        const { currentPassword, newPassword } = changePasswordDto;
        const user = await this.userRepository.findOne({
            where: { id: userId },
        });
        if (!user || !(await this.verifyPassword(currentPassword, user.password))) {
            throw new common_1.UnauthorizedException('Current password is incorrect');
        }
        const hashedPassword = await this.hashPassword(newPassword);
        await this.userRepository.update(userId, {
            password: hashedPassword,
        });
        await this.refreshTokenRepository.update({ userId, isRevoked: false }, { isRevoked: true });
        return { message: 'Password changed successfully' };
    }
    async hasPermission(userId, resource, action) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
            relations: ['roles', 'roles.permissions'],
        });
        if (!user) {
            return false;
        }
        for (const role of user.roles) {
            for (const permission of role.permissions) {
                if (permission.resource === resource && permission.action === action) {
                    return true;
                }
            }
        }
        return false;
    }
    async getUserPermissions(userId) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
            relations: ['roles', 'roles.permissions'],
        });
        if (!user) {
            return [];
        }
        const permissions = new Set();
        for (const role of user.roles) {
            for (const permission of role.permissions) {
                permissions.add(`${permission.resource}:${permission.action}`);
            }
        }
        return Array.from(permissions);
    }
    async generateTokens(user) {
        var _a;
        const payload = {
            sub: user.id,
            email: user.email,
            roles: user.roles.map(role => role.name),
            tenantId: (_a = user.tenant) === null || _a === void 0 ? void 0 : _a.id,
        };
        const [accessToken, refreshToken] = await Promise.all([
            this.jwtService.signAsync(payload),
            this.jwtService.signAsync(payload, {
                secret: process.env.JWT_REFRESH_SECRET,
                expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
            }),
        ]);
        return {
            accessToken,
            refreshToken,
        };
    }
    async hashPassword(password) {
        const saltRounds = 12;
        return bcrypt.hash(password, saltRounds);
    }
    async verifyPassword(password, hashedPassword) {
        return bcrypt.compare(password, hashedPassword);
    }
    async saveRefreshToken(userId, token) {
        const refreshToken = this.refreshTokenRepository.create({
            userId,
            token,
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            isRevoked: false,
        });
        await this.refreshTokenRepository.save(refreshToken);
    }
    async revokeRefreshToken(token) {
        await this.refreshTokenRepository.update({ token }, { isRevoked: true });
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(role_entity_1.Role)),
    __param(2, (0, typeorm_1.InjectRepository)(permission_entity_1.Permission)),
    __param(3, (0, typeorm_1.InjectRepository)(tenant_entity_1.Tenant)),
    __param(4, (0, typeorm_1.InjectRepository)(refresh_token_entity_1.RefreshToken)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        jwt_1.JwtService])
], AuthService);
//# sourceMappingURL=auth.service.js.map