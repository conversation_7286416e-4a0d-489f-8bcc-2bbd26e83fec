declare const _default: (() => {
    port: number;
    environment: string;
    database: {
        host: string;
        port: number;
        username: string;
        password: string;
        database: string;
        synchronize: boolean;
        logging: boolean;
    };
    redis: {
        host: string;
        port: number;
        password: string | undefined;
        db: number;
        prefix: string;
    };
    jwt: {
        secret: string;
        expiresIn: string;
        refreshSecret: string;
        refreshExpiresIn: string;
        issuer: string;
        audience: string;
    };
    security: {
        bcryptRounds: number;
        rateLimitWindowMs: number;
        rateLimitMax: number;
        corsOrigins: string[];
    };
    logging: {
        level: string;
        enableFileLogging: boolean;
        logDirectory: string;
    };
    api: {
        prefix: string;
        version: string;
        swaggerEnabled: boolean;
    };
}) & import("@nestjs/config").ConfigFactoryKeyHost<{
    port: number;
    environment: string;
    database: {
        host: string;
        port: number;
        username: string;
        password: string;
        database: string;
        synchronize: boolean;
        logging: boolean;
    };
    redis: {
        host: string;
        port: number;
        password: string | undefined;
        db: number;
        prefix: string;
    };
    jwt: {
        secret: string;
        expiresIn: string;
        refreshSecret: string;
        refreshExpiresIn: string;
        issuer: string;
        audience: string;
    };
    security: {
        bcryptRounds: number;
        rateLimitWindowMs: number;
        rateLimitMax: number;
        corsOrigins: string[];
    };
    logging: {
        level: string;
        enableFileLogging: boolean;
        logDirectory: string;
    };
    api: {
        prefix: string;
        version: string;
        swaggerEnabled: boolean;
    };
}>;
export default _default;
