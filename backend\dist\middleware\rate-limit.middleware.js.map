{"version": 3, "file": "rate-limit.middleware.js", "sourceRoot": "", "sources": ["../../src/middleware/rate-limit.middleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4D;AAE5D,8DAAgE;AAUzD,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAG5B,YAA6B,MAA2B;QAA3B,WAAM,GAAN,MAAM,CAAqB;QAFvC,mBAAc,GAAG,IAAI,GAAG,EAAgD,CAAC;IAE9B,CAAC;IAE7D,GAAG,CAAC,GAAoB,EAAE,GAAa,EAAE,IAAkB;QACvD,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YACvC,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YAEzC,IAAI,CAAC,QAAQ,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC1B,OAAO,IAAI,EAAE,CAAC;YAClB,CAAC;YAED,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvB,MAAM,GAAG,GAAG,GAAG,QAAQ,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;YACpD,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAE7C,IAAI,CAAC,OAAO,IAAI,GAAG,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;gBAEtC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE;oBACzB,KAAK,EAAE,CAAC;oBACR,SAAS,EAAE,GAAG,GAAG,SAAS,CAAC,QAAQ;iBACtC,CAAC,CAAC;YACP,CAAC;iBAAM,IAAI,OAAO,CAAC,KAAK,IAAI,SAAS,CAAC,GAAG,EAAE,CAAC;gBAExC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,QAAQ,EAAE,EAAE,qBAAqB,CAAC,CAAC;gBAC/E,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACxB,KAAK,EAAE,mBAAmB;oBAC1B,OAAO,EAAE,8CAA8C;oBACvD,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;iBAC1D,CAAC,CAAC;YACP,CAAC;iBAAM,CAAC;gBAEJ,OAAO,CAAC,KAAK,EAAE,CAAC;YACpB,CAAC;YAGD,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAClD,GAAG,CAAC,SAAS,CAAC,mBAAmB,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC;YAClD,GAAG,CAAC,SAAS,CAAC,uBAAuB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,GAAG,CAAC,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,KAAK,KAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAChG,GAAG,CAAC,SAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,SAAS,KAAI,GAAG,GAAG,SAAS,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;YAE5G,IAAI,EAAE,CAAC;QACX,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,YAAY,EAAE,EAAE,qBAAqB,CAAC,CAAC;YACzF,IAAI,EAAE,CAAC;QACX,CAAC;IACL,CAAC;IAEO,WAAW,CAAC,GAAoB;;QAEpC,IAAI,MAAA,GAAG,CAAC,IAAI,0CAAE,MAAM,EAAE,CAAC;YACnB,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;QAC3B,CAAC;QAGD,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAW,CAAC;QAC3D,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC;QACxF,OAAO,EAAE,IAAI,SAAS,CAAC;IAC3B,CAAC;IAEO,YAAY,CAAC,GAAY;QAC7B,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;QACtB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAG1B,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;YAC/B,OAAO,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;QACvC,CAAC;QAGD,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YAC3B,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;QACzC,CAAC;QAGD,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7B,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;QACxC,CAAC;QAGD,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ,CAAA;AArFY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAI4B,oCAAmB;GAH/C,mBAAmB,CAqF/B"}