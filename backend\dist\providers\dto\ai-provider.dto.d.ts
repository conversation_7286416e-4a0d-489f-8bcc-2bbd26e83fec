export declare enum ProviderType {
    OPENAI = "openai",
    CLAUDE = "claude",
    GEMINI = "gemini",
    MISTRAL = "mistral",
    GROQ = "groq",
    CUSTOM = "custom"
}
export declare class AIProviderDto {
    id: string;
    name: string;
    version: string;
    capabilities: {
        textGeneration: boolean;
        textCompletion: boolean;
        chatCompletion: boolean;
        embeddings: boolean;
        functionCalling: boolean;
        vision: boolean;
        audio: boolean;
        streaming: boolean;
    };
    costPer1KTokens: {
        input: number;
        output: number;
    };
    models: Array<{
        id: string;
        name: string;
        contextLength: number;
        maxTokens: number;
        capabilities: string[];
    }>;
    config: {
        apiKey: string;
        baseUrl?: string;
        timeout?: number;
        maxRetries?: number;
    };
    status: 'active' | 'inactive' | 'error';
    organizationId: string;
    createdAt: Date;
    updatedAt: Date;
}
export declare class CreateAIProviderDto {
    name: string;
    version: string;
    type: ProviderType;
    capabilities: {
        textGeneration: boolean;
        textCompletion: boolean;
        chatCompletion: boolean;
        embeddings: boolean;
        functionCalling: boolean;
        vision: boolean;
        audio: boolean;
        streaming: boolean;
    };
    costPer1KTokens: {
        input: number;
        output: number;
    };
    models: Array<{
        id: string;
        name: string;
        contextLength: number;
        maxTokens: number;
        capabilities: string[];
    }>;
    config: {
        apiKey: string;
        baseUrl?: string;
        timeout?: number;
        maxRetries?: number;
    };
}
export declare class UpdateAIProviderDto {
    name?: string;
    version?: string;
    capabilities?: {
        textGeneration: boolean;
        textCompletion: boolean;
        chatCompletion: boolean;
        embeddings: boolean;
        functionCalling: boolean;
        vision: boolean;
        audio: boolean;
        streaming: boolean;
    };
    costPer1KTokens?: {
        input: number;
        output: number;
    };
    models?: Array<{
        id: string;
        name: string;
        contextLength: number;
        maxTokens: number;
        capabilities: string[];
    }>;
    config?: {
        apiKey?: string;
        baseUrl?: string;
        timeout?: number;
        maxRetries?: number;
    };
}
export declare class AIRequestDto {
    requestId: string;
    providerId: string;
    modelId: string;
    organizationId: string;
    userId?: string;
    messages: Array<{
        role: 'system' | 'user' | 'assistant';
        content: string;
        name?: string;
        functionCall?: {
            name: string;
            arguments: string;
        };
    }>;
    maxTokens?: number;
    temperature?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    stream?: boolean;
    functions?: Array<{
        name: string;
        description: string;
        parameters: Record<string, any>;
    }>;
    capabilities: string[];
}
export declare class AIResponseDto {
    requestId: string;
    providerId: string;
    modelId: string;
    content: string;
    usage: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
    cost: number;
    latency: number;
    status: 'completed' | 'failed' | 'cancelled';
    error?: string;
    timestamp: Date;
}
export declare class ProviderSelectionRequestDto {
    organizationId: string;
    userId?: string;
    modelId: string;
    capabilities: string[];
    maxCost?: number;
    maxLatency?: number;
    preferredProviders?: string[];
    excludeProviders?: string[];
    requireStreaming?: boolean;
}
export declare class ProviderSelectionResponseDto {
    providerId: string;
    modelId: string;
    score: number;
    estimatedCost: number;
    estimatedLatency: number;
    reason: string;
}
