"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AgentTemplatesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentTemplatesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const agent_entity_1 = require("../database/entities/agent.entity");
const apix_gateway_1 = require("../websocket/apix.gateway");
let AgentTemplatesService = AgentTemplatesService_1 = class AgentTemplatesService {
    constructor(templateRepository, apixGateway) {
        this.templateRepository = templateRepository;
        this.apixGateway = apixGateway;
        this.logger = new common_1.Logger(AgentTemplatesService_1.name);
    }
    async createTemplate(createTemplateDto, organizationId, userId) {
        try {
            const template = this.templateRepository.create(Object.assign(Object.assign({}, createTemplateDto), { createdBy: userId, organizationId }));
            const savedTemplate = await this.templateRepository.save(template);
            this.apixGateway.emitToOrganization(organizationId, 'template_created', {
                templateId: savedTemplate.id,
                name: savedTemplate.name,
                category: savedTemplate.category,
                timestamp: new Date(),
            });
            this.logger.log(`Template created: ${savedTemplate.id} for organization: ${organizationId}`);
            return savedTemplate;
        }
        catch (error) {
            this.logger.error(`Failed to create template: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async getTemplatesByOrganization(organizationId) {
        try {
            return await this.templateRepository.find({
                where: [
                    { organizationId },
                    { isPublic: true }
                ],
                order: { createdAt: 'DESC' },
            });
        }
        catch (error) {
            this.logger.error(`Failed to get templates: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async getTemplateById(templateId, organizationId) {
        try {
            const template = await this.templateRepository.findOne({
                where: [
                    { id: templateId, organizationId },
                    { id: templateId, isPublic: true }
                ],
            });
            if (!template) {
                throw new common_1.NotFoundException(`Template with ID ${templateId} not found`);
            }
            return template;
        }
        catch (error) {
            this.logger.error(`Failed to get template ${templateId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async updateTemplate(templateId, updateData, organizationId) {
        try {
            const template = await this.getTemplateById(templateId, organizationId);
            Object.assign(template, updateData);
            const updatedTemplate = await this.templateRepository.save(template);
            this.apixGateway.emitToOrganization(organizationId, 'template_updated', {
                templateId: updatedTemplate.id,
                changes: updateData,
                timestamp: new Date(),
            });
            return updatedTemplate;
        }
        catch (error) {
            this.logger.error(`Failed to update template ${templateId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async deleteTemplate(templateId, organizationId) {
        try {
            const template = await this.getTemplateById(templateId, organizationId);
            await this.templateRepository.remove(template);
            this.apixGateway.emitToOrganization(organizationId, 'template_deleted', {
                templateId,
                timestamp: new Date(),
            });
        }
        catch (error) {
            this.logger.error(`Failed to delete template ${templateId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async getTemplateCategories(organizationId) {
        try {
            const categories = await this.templateRepository
                .createQueryBuilder('template')
                .select('template.category', 'category')
                .addSelect('COUNT(*)', 'count')
                .where('template.organizationId = :organizationId OR template.isPublic = true', { organizationId })
                .groupBy('template.category')
                .getRawMany();
            const categoryData = {
                'customer-support': {
                    name: 'Customer Support',
                    description: 'Agents for handling customer inquiries and support',
                    icon: 'headphones',
                },
                'sales': {
                    name: 'Sales',
                    description: 'Agents for lead generation and sales processes',
                    icon: 'trending-up',
                },
                'marketing': {
                    name: 'Marketing',
                    description: 'Agents for content creation and marketing tasks',
                    icon: 'megaphone',
                },
                'hr': {
                    name: 'Human Resources',
                    description: 'Agents for HR processes and employee support',
                    icon: 'users',
                },
                'finance': {
                    name: 'Finance',
                    description: 'Agents for financial analysis and reporting',
                    icon: 'dollar-sign',
                },
                'operations': {
                    name: 'Operations',
                    description: 'Agents for operational tasks and workflows',
                    icon: 'settings',
                },
                'development': {
                    name: 'Development',
                    description: 'Agents for coding and technical tasks',
                    icon: 'code',
                },
                'general': {
                    name: 'General',
                    description: 'General-purpose agents for various tasks',
                    icon: 'bot',
                },
            };
            return categories.map(cat => {
                var _a, _b, _c;
                return ({
                    id: cat.category,
                    name: ((_a = categoryData[cat.category]) === null || _a === void 0 ? void 0 : _a.name) || cat.category,
                    description: ((_b = categoryData[cat.category]) === null || _b === void 0 ? void 0 : _b.description) || 'Custom category',
                    icon: ((_c = categoryData[cat.category]) === null || _c === void 0 ? void 0 : _c.icon) || 'bot',
                    templateCount: parseInt(cat.count),
                });
            });
        }
        catch (error) {
            this.logger.error(`Failed to get template categories: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async getTemplatesByCategory(category, organizationId) {
        try {
            return await this.templateRepository.find({
                where: [
                    { category, organizationId },
                    { category, isPublic: true }
                ],
                order: { createdAt: 'DESC' },
            });
        }
        catch (error) {
            this.logger.error(`Failed to get templates by category: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async searchTemplates(query, organizationId) {
        try {
            return await this.templateRepository
                .createQueryBuilder('template')
                .where('(template.organizationId = :organizationId OR template.isPublic = true)', { organizationId })
                .andWhere('(template.name ILIKE :query OR template.description ILIKE :query OR template.category ILIKE :query)', {
                query: `%${query}%`,
            })
                .orderBy('template.createdAt', 'DESC')
                .getMany();
        }
        catch (error) {
            this.logger.error(`Failed to search templates: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async getPopularTemplates(organizationId, limit = 10) {
        try {
            return await this.templateRepository.find({
                where: { isPublic: true },
                order: { createdAt: 'DESC' },
                take: limit,
            });
        }
        catch (error) {
            this.logger.error(`Failed to get popular templates: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async duplicateTemplate(templateId, organizationId, userId) {
        try {
            const originalTemplate = await this.getTemplateById(templateId, organizationId);
            const duplicatedTemplate = this.templateRepository.create({
                name: `${originalTemplate.name} (Copy)`,
                category: originalTemplate.category,
                description: originalTemplate.description,
                config: originalTemplate.config,
                skills: originalTemplate.skills,
                isPublic: false,
                promptTemplate: originalTemplate.promptTemplate,
                type: originalTemplate.type,
                supportedProviders: originalTemplate.supportedProviders,
                metadata: originalTemplate.metadata,
                createdBy: userId,
                organizationId,
            });
            const savedTemplate = await this.templateRepository.save(duplicatedTemplate);
            this.apixGateway.emitToOrganization(organizationId, 'template_duplicated', {
                originalTemplateId: templateId,
                newTemplateId: savedTemplate.id,
                timestamp: new Date(),
            });
            return savedTemplate;
        }
        catch (error) {
            this.logger.error(`Failed to duplicate template ${templateId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async getTemplateUsageStats(templateId, organizationId) {
        try {
            const template = await this.getTemplateById(templateId, organizationId);
            const usageStats = await this.templateRepository
                .createQueryBuilder('template')
                .leftJoin('template.instances', 'agent')
                .leftJoin('agent.executions', 'execution')
                .select('COUNT(DISTINCT agent.id)', 'totalInstances')
                .addSelect('COUNT(execution.id)', 'totalExecutions')
                .addSelect('AVG(execution.duration)', 'averageExecutionTime')
                .addSelect('SUM(execution.cost)', 'totalCost')
                .where('template.id = :templateId', { templateId })
                .getRawOne();
            return {
                templateId,
                templateName: template.name,
                totalInstances: parseInt(usageStats.totalInstances) || 0,
                totalExecutions: parseInt(usageStats.totalExecutions) || 0,
                averageExecutionTime: parseFloat(usageStats.averageExecutionTime) || 0,
                totalCost: parseFloat(usageStats.totalCost) || 0,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get template usage stats ${templateId}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async seedDefaultTemplates(organizationId, userId) {
        try {
            const defaultTemplates = [
                {
                    name: 'Customer Support Agent',
                    category: 'customer-support',
                    description: 'A helpful customer support agent that can handle common inquiries and provide solutions',
                    type: agent_entity_1.AgentType.BASIC,
                    promptTemplate: `You are a helpful customer support agent. Your role is to:
- Listen carefully to customer concerns
- Provide clear, accurate, and helpful responses
- Escalate complex issues when necessary
- Maintain a professional and empathetic tone
- Follow up to ensure customer satisfaction

Always be polite, patient, and solution-oriented.`,
                    skills: ['customer-service', 'problem-solving', 'communication'],
                    supportedProviders: [agent_entity_1.ProviderType.OPENAI, agent_entity_1.ProviderType.CLAUDE, agent_entity_1.ProviderType.GEMINI],
                    config: {
                        temperature: 0.7,
                        maxTokens: 1000,
                        systemPrompt: 'You are a helpful customer support agent.',
                    },
                },
                {
                    name: 'Sales Assistant',
                    category: 'sales',
                    description: 'A sales assistant that can help with lead qualification and sales processes',
                    type: agent_entity_1.AgentType.BASIC,
                    promptTemplate: `You are a sales assistant. Your role is to:
- Qualify leads based on their needs and budget
- Provide product information and pricing
- Handle objections professionally
- Schedule follow-up meetings
- Track sales opportunities

Be consultative, professional, and focused on value creation.`,
                    skills: ['sales', 'lead-qualification', 'product-knowledge'],
                    supportedProviders: [agent_entity_1.ProviderType.OPENAI, agent_entity_1.ProviderType.CLAUDE, agent_entity_1.ProviderType.GEMINI],
                    config: {
                        temperature: 0.6,
                        maxTokens: 800,
                        systemPrompt: 'You are a sales assistant.',
                    },
                },
                {
                    name: 'Content Writer',
                    category: 'marketing',
                    description: 'A content writer that can create engaging marketing materials and blog posts',
                    type: agent_entity_1.AgentType.BASIC,
                    promptTemplate: `You are a content writer. Your role is to:
- Create engaging and informative content
- Write in a clear, professional style
- Adapt tone and style to target audience
- Include relevant keywords naturally
- Ensure content is original and valuable

Write content that is informative, engaging, and optimized for your audience.`,
                    skills: ['content-writing', 'seo', 'marketing'],
                    supportedProviders: [agent_entity_1.ProviderType.OPENAI, agent_entity_1.ProviderType.CLAUDE, agent_entity_1.ProviderType.GEMINI],
                    config: {
                        temperature: 0.8,
                        maxTokens: 1500,
                        systemPrompt: 'You are a content writer.',
                    },
                },
                {
                    name: 'Code Assistant',
                    category: 'development',
                    description: 'A coding assistant that can help with programming tasks and code reviews',
                    type: agent_entity_1.AgentType.BASIC,
                    promptTemplate: `You are a code assistant. Your role is to:
- Write clean, efficient, and well-documented code
- Review code for best practices and security
- Debug issues and provide solutions
- Explain code concepts clearly
- Suggest improvements and optimizations

Write code that is readable, maintainable, and follows best practices.`,
                    skills: ['programming', 'code-review', 'debugging'],
                    supportedProviders: [agent_entity_1.ProviderType.OPENAI, agent_entity_1.ProviderType.CLAUDE, agent_entity_1.ProviderType.GEMINI],
                    config: {
                        temperature: 0.3,
                        maxTokens: 2000,
                        systemPrompt: 'You are a code assistant.',
                    },
                },
                {
                    name: 'Data Analyst',
                    category: 'finance',
                    description: 'A data analyst that can help with data interpretation and reporting',
                    type: agent_entity_1.AgentType.BASIC,
                    promptTemplate: `You are a data analyst. Your role is to:
- Analyze data and identify patterns
- Create clear and insightful reports
- Provide actionable recommendations
- Explain complex data in simple terms
- Help with data visualization decisions

Provide analysis that is accurate, insightful, and actionable.`,
                    skills: ['data-analysis', 'reporting', 'statistics'],
                    supportedProviders: [agent_entity_1.ProviderType.OPENAI, agent_entity_1.ProviderType.CLAUDE, agent_entity_1.ProviderType.GEMINI],
                    config: {
                        temperature: 0.5,
                        maxTokens: 1200,
                        systemPrompt: 'You are a data analyst.',
                    },
                },
            ];
            for (const templateData of defaultTemplates) {
                const existingTemplate = await this.templateRepository.findOne({
                    where: { name: templateData.name, organizationId },
                });
                if (!existingTemplate) {
                    await this.createTemplate(Object.assign(Object.assign({}, templateData), { isPublic: false }), organizationId, userId);
                }
            }
            this.logger.log(`Default templates seeded for organization: ${organizationId}`);
        }
        catch (error) {
            this.logger.error(`Failed to seed default templates: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
};
exports.AgentTemplatesService = AgentTemplatesService;
exports.AgentTemplatesService = AgentTemplatesService = AgentTemplatesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(agent_entity_1.AgentTemplate)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        apix_gateway_1.ApixGateway])
], AgentTemplatesService);
//# sourceMappingURL=agent-templates.service.js.map