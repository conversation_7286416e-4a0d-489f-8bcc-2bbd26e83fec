import { z } from 'zod';

// Enums
export enum AgentType {
  BASIC = 'BASIC',
  TOOL_DRIVEN = 'TOOL_DRIVEN',
  HYBRID = 'HYBRID',
  MULTI_TASK = 'MULTI_TASK',
  MULTI_PROVIDER = 'MULTI_PROVIDER',
  COLLABORATIVE = 'COLLABORATIVE'
}

export enum AgentStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  ERROR = 'ERROR',
  PAUSED = 'PAUSED'
}

export enum ProviderType {
  OPENAI = 'OPENAI',
  CLAUDE = 'CLAUDE',
  GEMINI = 'GEMINI',
  MISTRAL = 'MISTRAL',
  GROQ = 'GROQ'
}

// Zod Schemas
export const AgentTemplateSchema = z.object({
  id: z.string().optional(),
  name: z.string(),
  category: z.string(),
  description: z.string(),
  config: z.record(z.string(), z.any()),
  skills: z.array(z.string()),
  isPublic: z.boolean().default(false),
  promptTemplate: z.string(),
  type: z.nativeEnum(AgentType),
  supportedProviders: z.array(z.nativeEnum(ProviderType)),
  createdBy: z.string().optional(),
  organizationId: z.string().optional(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

export const AgentInstanceSchema = z.object({
  id: z.string().optional(),
  templateId: z.string(),
  name: z.string(),
  config: z.record(z.string(), z.any()),
  type: z.nativeEnum(AgentType),
  status: z.nativeEnum(AgentStatus).optional(),
  primaryProvider: z.nativeEnum(ProviderType),
  fallbackProviders: z.array(z.nativeEnum(ProviderType)).optional(),
  memoryConfig: z.object({
    maxTokens: z.number(),
    retentionDays: z.number(),
    enableLongTerm: z.boolean(),
  }).optional(),
  skills: z.array(z.string()).optional(),
  performanceMetrics: z.object({
    totalExecutions: z.number(),
    successRate: z.number(),
    averageResponseTime: z.number(),
    lastExecuted: z.string(),
  }).optional(),
  organizationId: z.string().optional(),
  createdBy: z.string().optional(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

export const AgentExecutionSchema = z.object({
  id: z.string(),
  agentId: z.string(),
  sessionId: z.string().optional(),
  input: z.string(),
  output: z.string().optional(),
  status: z.enum(['PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED']),
  context: z.record(z.string(), z.any()).optional(),
  metadata: z.object({
    provider: z.string(),
    model: z.string(),
    tokens: z.object({
      input: z.number(),
      output: z.number(),
      total: z.number(),
    }),
    cost: z.number(),
    duration: z.number(),
    retryCount: z.number(),
  }).optional(),
  errorMessage: z.string().optional(),
  errorDetails: z.record(z.string(), z.any()).optional(),
  organizationId: z.string(),
  createdAt: z.string(),
  completedAt: z.string().optional(),
});

// Types
export type AgentTemplate = z.infer<typeof AgentTemplateSchema>;
export type AgentInstance = z.infer<typeof AgentInstanceSchema>;
export type AgentExecution = z.infer<typeof AgentExecutionSchema>;

export interface CreateAgentTemplateDto {
  name: string;
  category: string;
  description: string;
  config: Record<string, any>;
  skills: string[];
  isPublic?: boolean;
  promptTemplate: string;
  type: AgentType;
  supportedProviders: ProviderType[];
}

export interface CreateAgentInstanceDto {
  templateId: string;
  name: string;
  config: Record<string, any>;
  type: AgentType;
  primaryProvider: ProviderType;
  fallbackProviders?: ProviderType[];
  memoryConfig?: {
    maxTokens: number;
    retentionDays: number;
    enableLongTerm: boolean;
  };
  skills?: string[];
}

export interface UpdateAgentInstanceDto {
  name?: string;
  config?: Record<string, any>;
  status?: AgentStatus;
  primaryProvider?: ProviderType;
  fallbackProviders?: ProviderType[];
}

export interface ExecuteAgentDto {
  message: string;
  sessionId?: string;
  context?: Record<string, any>;
  streamResponse?: boolean;
}

export interface AgentAnalytics {
  agentId: string;
  totalExecutions: number;
  successRate: number;
  averageResponseTime: number;
  totalCost: number;
  providerDistribution: Record<string, number>;
  errorRate: number;
  lastExecuted: string;
  performanceTrend: Array<{
    period: string;
    executions: number;
    successRate: number;
    averageTime: number;
    cost: number;
  }>;
}

export interface SessionMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface SessionMemory {
  id: string;
  agentId: string;
  organizationId: string;
  messages: SessionMessage[];
  context: Record<string, any>;
  metadata: {
    tokenCount: number;
    maxTokens: number;
    retentionDays: number;
    enableLongTerm: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

// API Response wrapper
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message: string;
}