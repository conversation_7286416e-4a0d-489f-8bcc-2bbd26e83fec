import { ExceptionFilter, ArgumentsHost } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
export interface ErrorResponse {
    statusCode: number;
    timestamp: string;
    path: string;
    method: string;
    message: string | string[];
    error?: string;
    details?: any;
    requestId?: string;
}
export declare class GlobalExceptionFilter implements ExceptionFilter {
    private configService;
    private readonly logger;
    constructor(configService: ConfigService);
    catch(exception: unknown, host: ArgumentsHost): void;
    private buildErrorResponse;
    private logError;
    private getPrismaErrorStatus;
    private getPrismaErrorMessage;
}
