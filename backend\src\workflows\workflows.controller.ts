import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
} from '@nestjs/common';
import { Request as ExpressRequest, request } from 'express';
import { WorkflowsService } from './workflows.service';
import { WorkflowExecutionService } from './workflow-execution.service';
import { WorkflowAnalyticsService } from './workflow-analytics.service';
import {
  CreateWorkflowDto,
  UpdateWorkflowDto,
  WorkflowFiltersDto,
} from './dto/workflow.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { PermissionGuard } from '../auth/permission.guard';
import { RequirePermissions } from '../auth/permissions.decorator';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { WorkflowStatus } from './workflow.entity';

interface RequestWithUser extends ExpressRequest {
  user: {
    userId: string;
    organizationId: string;
  };
}

@ApiTags('workflows')
@ApiBearerAuth()
@Controller('api/workflows')
@UseGuards(JwtAuthGuard)
export class WorkflowsController {
  constructor(
    private readonly workflowsService: WorkflowsService,
    private readonly executionService: WorkflowExecutionService,
    private readonly analyticsService: WorkflowAnalyticsService,
  ) {}

  @Post()
  @UseGuards(PermissionGuard)
  @RequirePermissions('workflows.write')
  @ApiOperation({ summary: 'Create a new workflow' })
  @ApiResponse({ status: 201, description: 'Workflow created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid workflow definition' })
  async createWorkflow(
    @Body() createWorkflowDto: CreateWorkflowDto,
    @Request() req: RequestWithUser,
  ) {
    return this.workflowsService.createWorkflow(
      createWorkflowDto,
      req.user.userId,
      req.user.organizationId,
    );
  }

  @Get()
  @UseGuards(PermissionGuard)
  @RequirePermissions('workflows.read')
  @ApiOperation({ summary: 'Get workflows with filtering and pagination' })
  @ApiResponse({ status: 200, description: 'Workflows retrieved successfully' })
  @ApiQuery({ name: 'search', required: false, description: 'Search term' })
  @ApiQuery({ name: 'status', required: false, enum: WorkflowStatus, description: 'Filter by status' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  async getWorkflows(
    @Query() filters: WorkflowFiltersDto,
    @Request() req: RequestWithUser,
  ) {
    return this.workflowsService.getWorkflows(
      filters,
      req.user.userId,
      req.user.organizationId,
    );
  }

  @Get(':id')
  @UseGuards(PermissionGuard)
  @RequirePermissions('workflows.read')
  @ApiOperation({ summary: 'Get workflow by ID' })
  @ApiResponse({ status: 200, description: 'Workflow retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Workflow not found' })
  @ApiParam({ name: 'id', description: 'Workflow ID' })
  async getWorkflowById(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: RequestWithUser,
  ) {
    return this.workflowsService.getWorkflowById(
      id,
      req.user.userId,
      req.user.organizationId,
    );
  }

  @Put(':id')
  @UseGuards(PermissionGuard)
  @RequirePermissions('workflows.write')
  @ApiOperation({ summary: 'Update workflow' })
  @ApiResponse({ status: 200, description: 'Workflow updated successfully' })
  @ApiResponse({ status: 404, description: 'Workflow not found' })
  @ApiParam({ name: 'id', description: 'Workflow ID' })
  async updateWorkflow(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateWorkflowDto: UpdateWorkflowDto,
    @Request() req: RequestWithUser,
  ) {
    return this.workflowsService.updateWorkflow(
      id,
      updateWorkflowDto,
      req.user.userId,
      req.user.organizationId,
    );
  }

  @Delete(':id')
  @UseGuards(PermissionGuard)
  @RequirePermissions('workflows.write')
  @ApiOperation({ summary: 'Delete workflow' })
  @ApiResponse({ status: 200, description: 'Workflow deleted successfully' })
  @ApiResponse({ status: 404, description: 'Workflow not found' })
  @ApiParam({ name: 'id', description: 'Workflow ID' })
  async deleteWorkflow(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: RequestWithUser,
  ) {
    return this.workflowsService.deleteWorkflow(
      id,
      req.user.userId,
      req.user.organizationId,
    );
  }

  @Put(':id/status')
  @UseGuards(PermissionGuard)
  @RequirePermissions('workflows.write')
  @ApiOperation({ summary: 'Change workflow status' })
  @ApiResponse({ status: 200, description: 'Workflow status changed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid status transition' })
  @ApiParam({ name: 'id', description: 'Workflow ID' })
  async changeWorkflowStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { status: WorkflowStatus },
    @Request() req: RequestWithUser,
  ) {
    return this.workflowsService.changeWorkflowStatus(
      id,
      body.status,
      req.user.userId,
      req.user.organizationId,
    );
  }

  @Post(':id/duplicate')
  @UseGuards(PermissionGuard)
  @RequirePermissions('workflows.write')
  @ApiOperation({ summary: 'Duplicate workflow' })
  @ApiResponse({ status: 201, description: 'Workflow duplicated successfully' })
  @ApiParam({ name: 'id', description: 'Workflow ID' })
  async duplicateWorkflow(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: RequestWithUser,
  ) {
    return this.workflowsService.duplicateWorkflow(
      id,
      req.user.userId,
      req.user.organizationId,
    );
  }

  @Post(':id/execute')
  @UseGuards(PermissionGuard)
  @RequirePermissions('workflows.execute')
  @ApiOperation({ summary: 'Execute workflow' })
  @ApiResponse({ status: 201, description: 'Workflow execution started' })
  @ApiResponse({ status: 400, description: 'Invalid workflow or execution parameters' })
  @ApiParam({ name: 'id', description: 'Workflow ID' })
  async executeWorkflow(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { inputVariables?: Record<string, any> },
    @Request() req: RequestWithUser,
  ) {
    const workflow = await this.workflowsService.getWorkflowById(
      id,
      req.user.userId,
      req.user.organizationId,
    );

    const executionId = await this.executionService.executeWorkflow(
      workflow,
      req.user.userId,
      body.inputVariables,
    );

    return {
      executionId,
      message: 'Workflow execution started',
      status: 'started',
    };
  }

  @Get(':id/executions/:executionId')
  @UseGuards(PermissionGuard)
  @RequirePermissions('workflows.read')
  @ApiOperation({ summary: 'Get workflow execution status' })
  @ApiResponse({ status: 200, description: 'Execution status retrieved' })
  @ApiParam({ name: 'id', description: 'Workflow ID' })
  @ApiParam({ name: 'executionId', description: 'Execution ID' })
  async getExecutionStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Param('executionId') executionId: string,
    @Request() req: RequestWithUser,
  ) {
    // Verify user has access to the workflow
    await this.workflowsService.getWorkflowById(
      id,
      req.user.userId,
      req.user.organizationId,
    );

    return this.executionService.getExecutionStatus(executionId);
  }

  @Put(':id/executions/:executionId/pause')
  @UseGuards(PermissionGuard)
  @RequirePermissions('workflows.execute')
  @ApiOperation({ summary: 'Pause workflow execution' })
  @ApiResponse({ status: 200, description: 'Execution paused successfully' })
  @ApiParam({ name: 'id', description: 'Workflow ID' })
  @ApiParam({ name: 'executionId', description: 'Execution ID' })
  async pauseExecution(
    @Param('id', ParseUUIDPipe) id: string,
    @Param('executionId') executionId: string,
    @Request() req: RequestWithUser,
  ) {
    // Verify user has access to the workflow
    await this.workflowsService.getWorkflowById(
      id,
      req.user.userId,
      req.user.organizationId,
    );

    await this.executionService.pauseExecution(executionId);
    return { message: 'Execution paused successfully' };
  }

  @Put(':id/executions/:executionId/resume')
  @UseGuards(PermissionGuard)
  @RequirePermissions('workflows.execute')
  @ApiOperation({ summary: 'Resume workflow execution' })
  @ApiResponse({ status: 200, description: 'Execution resumed successfully' })
  @ApiParam({ name: 'id', description: 'Workflow ID' })
  @ApiParam({ name: 'executionId', description: 'Execution ID' })
  async resumeExecution(
    @Param('id', ParseUUIDPipe) id: string,
    @Param('executionId') executionId: string,
    @Request() req: RequestWithUser,
  ) {
    // Verify user has access to the workflow
    await this.workflowsService.getWorkflowById(
      id,
      req.user.userId,
      req.user.organizationId,
    );

    await this.executionService.resumeExecution(executionId);
    return { message: 'Execution resumed successfully' };
  }

  @Delete(':id/executions/:executionId')
  @UseGuards(PermissionGuard)
  @RequirePermissions('workflows.execute')
  @ApiOperation({ summary: 'Cancel workflow execution' })
  @ApiResponse({ status: 200, description: 'Execution cancelled successfully' })
  @ApiParam({ name: 'id', description: 'Workflow ID' })
  @ApiParam({ name: 'executionId', description: 'Execution ID' })
  async cancelExecution(
    @Param('id', ParseUUIDPipe) id: string,
    @Param('executionId') executionId: string,
    @Request() req: RequestWithUser,
  ) {
    // Verify user has access to the workflow
    await this.workflowsService.getWorkflowById(
      id,
      req.user.userId,
      req.user.organizationId,
    );

    await this.executionService.cancelExecution(executionId);
    return { message: 'Execution cancelled successfully' };
  }

  @Get(':id/analytics')
  @UseGuards(PermissionGuard)
  @RequirePermissions('workflows.read')
  @ApiOperation({ summary: 'Get workflow analytics' })
  @ApiResponse({ status: 200, description: 'Analytics retrieved successfully' })
  @ApiParam({ name: 'id', description: 'Workflow ID' })
  async getWorkflowAnalytics(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: RequestWithUser,
  ) {
    return this.workflowsService.getWorkflowAnalytics(
      id,
      req.user.userId,
      req.user.organizationId,
    );
  }

  @Get(':id/analytics/realtime')
  @UseGuards(PermissionGuard)
  @RequirePermissions('workflows.read')
  @ApiOperation({ summary: 'Get real-time workflow metrics' })
  @ApiResponse({ status: 200, description: 'Real-time metrics retrieved' })
  @ApiParam({ name: 'id', description: 'Workflow ID' })
  async getRealTimeMetrics(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: RequestWithUser,
  ) {
    // Verify user has access to the workflow
    await this.workflowsService.getWorkflowById(
      id,
      req.user.userId,
      req.user.organizationId,
    );

    return this.analyticsService.getRealTimeMetrics(id);
  }

  @Get('organization/analytics')
  @UseGuards(PermissionGuard)
  @RequirePermissions('workflows.read')
  @ApiOperation({ summary: 'Get organization workflow analytics' })
  @ApiResponse({ status: 200, description: 'Organization analytics retrieved' })
  @ApiQuery({ name: 'startDate', required: false, description: 'Start date (ISO string)' })
  @ApiQuery({ name: 'endDate', required: false, description: 'End date (ISO string)' })
  async getOrganizationAnalytics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const req = request as RequestWithUser;
    const timeRange = {
      start: startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      end: endDate ? new Date(endDate) : new Date(),
    };

    return this.analyticsService.getOrganizationAnalytics(
      req.user.organizationId,
      timeRange,
    );
  }

  @Post(':id/validate')
  @UseGuards(PermissionGuard)
  @RequirePermissions('workflows.write')
  @ApiOperation({ summary: 'Validate workflow definition' })
  @ApiResponse({ status: 200, description: 'Workflow validation completed' })
  @ApiResponse({ status: 400, description: 'Workflow validation failed' })
  @ApiParam({ name: 'id', description: 'Workflow ID' })
  async validateWorkflow(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: RequestWithUser,
  ) {
    const workflow = await this.workflowsService.getWorkflowById(
      id,
      req.user.userId,
      req.user.organizationId,
    );

    // The validation is already done in the service, but we can add additional checks here
    return {
      valid: true,
      message: 'Workflow definition is valid',
      workflow: {
        id: workflow.id,
        name: workflow.name,
        steps: workflow.definition.steps.length,
        triggers: workflow.definition.triggers.length,
      },
    };
  }

  @Get(':id/export')
  @UseGuards(PermissionGuard)
  @RequirePermissions('workflows.read')
  @ApiOperation({ summary: 'Export workflow definition' })
  @ApiResponse({ status: 200, description: 'Workflow exported successfully' })
  @ApiParam({ name: 'id', description: 'Workflow ID' })
  async exportWorkflow(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: RequestWithUser,
  ) {
    const workflow = await this.workflowsService.getWorkflowById(
      id,
      req.user.userId,
      req.user.organizationId,
    );

    return {
      id: workflow.id,
      name: workflow.name,
      description: workflow.description,
      definition: workflow.definition,
      metadata: workflow.metadata,
      version: '1.0',
      exportedAt: new Date().toISOString(),
    };
  }

  @Post('import')
  @UseGuards(PermissionGuard)
  @RequirePermissions('workflows.write')
  @ApiOperation({ summary: 'Import workflow definition' })
  @ApiResponse({ status: 201, description: 'Workflow imported successfully' })
  @ApiResponse({ status: 400, description: 'Invalid workflow definition' })
  async importWorkflow(
    @Body() importData: {
      name: string;
      description?: string;
      definition: any;
      metadata?: any;
    },
    @Request() req: RequestWithUser,
  ) {
    const createWorkflowDto: CreateWorkflowDto = {
      name: importData.name,
      description: importData.description,
      definition: importData.definition,
      metadata: importData.metadata,
    };

    return this.workflowsService.createWorkflow(
      createWorkflowDto,
      req.user.userId,
      req.user.organizationId,
    );
  }
} 