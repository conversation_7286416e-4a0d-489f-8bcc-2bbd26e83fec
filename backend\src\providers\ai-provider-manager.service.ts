import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AIProvider } from '../database/entities/ai-provider.entity';
import { AIModel } from '../database/entities/ai-provider.entity';
import { ProviderUsage } from '../database/entities/ai-provider.entity';
import { RedisService } from '../cache/redis.service';
import { ProviderType } from '../database/entities/ai-provider.entity';
import { CreateProviderDto, UpdateProviderDto } from './dto/ai-provider.dto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';

@Injectable()
export class AIProviderManagerService {
  private readonly logger = new Logger(AIProviderManagerService.name);
  private readonly CACHE_TTL = 300; // 5 minutes

  constructor(
    @InjectRepository(AIProvider)
    private readonly providerRepository: Repository<AIProvider>,
    @InjectRepository(AIModel)
    private readonly modelRepository: Repository<AIModel>,
    @InjectRepository(ProviderUsage)
    private readonly usageRepository: Repository<ProviderUsage>,
    private readonly redisService: RedisService,
    private readonly eventEmitter: EventEmitter2
  ) { }

  async createProvider(createProviderDto: CreateProviderDto, organizationId: string, userId: string): Promise<AIProvider> {
    try {
      // Validate provider type
      if (!Object.values(ProviderType).includes(createProviderDto.type)) {
        throw new BadRequestException(`Invalid provider type: ${createProviderDto.type}`);
      }

      // Check if provider with same name exists in organization
      const existingProvider = await this.providerRepository.findOne({
        where: { name: createProviderDto.name, organizationId }
      });

      if (existingProvider) {
        throw new BadRequestException(`Provider with name '${createProviderDto.name}' already exists`);
      }

      // Create provider
      const provider = this.providerRepository.create({
        ...createProviderDto,
        organizationId,
        createdBy: userId,
        isActive: createProviderDto.isActive ?? true
      });

      const savedProvider = await this.providerRepository.save(provider);

      // Clear cache
      await this.clearProviderCache(organizationId);

      // Emit event
      this.eventEmitter.emit('provider.created', {
        providerId: savedProvider.id,
        createdBy: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        config: createProviderDto.config,
        models: createProviderDto.models,
        name: createProviderDto.name,
        type: createProviderDto.type,
        organizationId,
      });

      this.logger.log(`Provider ${savedProvider.name} created for organization ${organizationId}`);
      return savedProvider;
    } catch (error: any) {
      this.logger.error(`Failed to create provider: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getProviders(organizationId: string): Promise<AIProvider[]> {
    try {
      const cacheKey = `providers:${organizationId}`;
      const cached = await this.redisService.get(cacheKey);

      if (cached) {
        return JSON.parse(cached);
      }

      const providers = await this.providerRepository.find({
        where: { organizationId },
        relations: ['models'],
        order: { createdAt: 'DESC' }
      });

      // Cache the result
      await this.redisService.set(cacheKey, JSON.stringify(providers));

      return providers;
    } catch (error: any) {
      this.logger.error(`Failed to get providers: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getProvider(id: string, organizationId: string): Promise<AIProvider> {
    try {
      const provider = await this.providerRepository.findOne({
        where: { id, organizationId },
        relations: ['models']
      });

      if (!provider) {
        throw new NotFoundException(`Provider with ID ${id} not found`);
      }

      return provider;
    } catch (error: any) {
      this.logger.error(`Failed to get provider ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updateProvider(id: string, updateProviderDto: UpdateProviderDto, organizationId: string): Promise<AIProvider> {
    try {
      const provider = await this.getProvider(id, organizationId);

      // Update provider
      Object.assign(provider, updateProviderDto);
      const updatedProvider = await this.providerRepository.save(provider);

      // Clear cache
      await this.clearProviderCache(organizationId);

      // Emit event
      this.eventEmitter.emit('provider.updated', {
        providerId: updatedProvider.id,
        changes: Object.keys(updateProviderDto)
      });

      this.logger.log(`Provider ${updatedProvider.name} updated`);
      return updatedProvider;
    } catch (error: any) {
      this.logger.error(`Failed to update provider ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async deleteProvider(id: string, organizationId: string): Promise<void> {
    try {
      const provider = await this.getProvider(id, organizationId);

      // Delete associated models and usage data
      await this.modelRepository.delete({ providerId: id });
      await this.usageRepository.delete({ providerId: id });
      await this.providerRepository.remove(provider);

      // Clear cache
      await this.clearProviderCache(organizationId);

      // Emit event
      this.eventEmitter.emit('provider.deleted', { providerId: id });

      this.logger.log(`Provider ${provider.name} deleted`);
    } catch (error: any) {
      this.logger.error(`Failed to delete provider ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async toggleProvider(id: string, isActive: boolean, organizationId: string): Promise<AIProvider> {
    try {
      const provider = await this.getProvider(id, organizationId);
      provider.isActive = isActive;

      const updatedProvider = await this.providerRepository.save(provider);

      // Clear cache
      await this.clearProviderCache(organizationId);

      this.logger.log(`Provider ${provider.name} ${isActive ? 'activated' : 'deactivated'}`);
      return updatedProvider;
    } catch (error: any) {
      this.logger.error(`Failed to toggle provider ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getProviderAnalytics(organizationId: string, timeRange: string = '7d'): Promise<any> {
    try {
      const providers = await this.getProviders(organizationId);

      const analytics = {
        totalProviders: providers.length,
        activeProviders: providers.filter(p => p.isActive).length,
        totalRequests: 0,
        totalCost: 0,
        averageLatency: 0,
        overallReliability: 0,
        usageByProvider: {},
        costTrends: []
      };

      // Calculate metrics
      for (const provider of providers) {
        const usage = await this.getProviderUsage(provider.id, timeRange);

        analytics.totalRequests += usage.totalRequests;
        analytics.totalCost += usage.totalCost;
        analytics.averageLatency += provider.performanceMetrics?.averageLatency || 0;
        analytics.overallReliability += provider.performanceMetrics?.reliability || 0;

        analytics.usageByProvider[provider.id] = {
          requests: usage.totalRequests,
          tokens: usage.totalTokens,
          cost: usage.totalCost,
          latency: provider.performanceMetrics?.averageLatency || 0
        };
      }

      if (providers.length > 0) {
        analytics.averageLatency /= providers.length;
        analytics.overallReliability /= providers.length;
      }

      return analytics;
    } catch (error: any) {
      this.logger.error(`Failed to get provider analytics: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getProviderUsage(providerId: string, timeRange: string = '30d'): Promise<any> {
    try {
      const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const usage = await this.usageRepository
        .createQueryBuilder('usage')
        .where('usage.providerId = :providerId', { providerId })
        .andWhere('usage.date >= :startDate', { startDate })
        .getMany();

      const totalRequests = usage.reduce((sum, u) => sum + u.requests, 0);
      const totalTokens = usage.reduce((sum, u) => sum + u.tokensUsed, 0);
      const totalCost = usage.reduce((sum, u) => sum + u.costInCents, 0) / 100; // Convert cents to dollars

      return {
        totalRequests,
        totalTokens,
        totalCost,
        usage: usage.map(u => ({
          date: u.date,
          requests: u.requests,
          tokens: u.tokensUsed,
          cost: u.costInCents / 100
        }))
      };
    } catch (error: any) {
      this.logger.error(`Failed to get provider usage: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updateProviderMetrics(providerId: string, metrics: {
    latency: number;
    reliability: number;
    healthStatus: 'healthy' | 'degraded' | 'unhealthy';
    requests: number;
    tokens: number;
    cost: number;
  }): Promise<void> {
    try {
      const provider = await this.providerRepository.findOne({ where: { id: providerId } });
      if (!provider) {
        throw new NotFoundException(`Provider ${providerId} not found`);
      }

      // Update performance metrics
      provider.performanceMetrics = {
        ...provider.performanceMetrics,
        averageLatency: metrics.latency,
        reliability: metrics.reliability,
        healthStatus: metrics.healthStatus,
        totalRequests: (provider.performanceMetrics?.totalRequests || 0) + metrics.requests,
        successRate: metrics.reliability,
        lastChecked: new Date()
      };

      // Update usage metrics
      provider.usageMetrics = {
        ...provider.usageMetrics,
        totalTokens: (provider.usageMetrics?.totalTokens || 0) + metrics.tokens,
        totalCost: (provider.usageMetrics?.totalCost || 0) + metrics.cost,
        requestsToday: (provider.usageMetrics?.requestsToday || 0) + metrics.requests,
        costToday: (provider.usageMetrics?.costToday || 0) + metrics.cost
      };

      await this.providerRepository.save(provider);

      // Record usage
      await this.recordUsage(providerId, {
        requests: metrics.requests,
        tokensUsed: metrics.tokens,
        costInCents: Math.round(metrics.cost * 100)
      });

      // Clear cache
      await this.clearProviderCache(provider.organizationId);

    } catch (error: any) {
      this.logger.error(`Failed to update provider metrics: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async recordUsage(providerId: string, usage: {
    requests: number;
    tokensUsed: number;
    costInCents: number;
  }): Promise<void> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      let dailyUsage = await this.usageRepository.findOne({
        where: { providerId, date: today }
      });

      if (!dailyUsage) {
        dailyUsage = this.usageRepository.create({
          providerId,
          date: today,
          requests: 0,
          tokensUsed: 0,
          costInCents: 0
        });
      }

      dailyUsage.requests += usage.requests;
      dailyUsage.tokensUsed += usage.tokensUsed;
      dailyUsage.costInCents += usage.costInCents;

      await this.usageRepository.save(dailyUsage);

      // Emit usage event
      this.eventEmitter.emit('provider.usage.report', {
        providerId,
        date: today,
        requests: dailyUsage.requests,
        tokensUsed: dailyUsage.tokensUsed,
        costInCents: dailyUsage.costInCents
      });

    } catch (error: any) {
      this.logger.error(`Failed to record usage: ${error.message}`, error.stack);
    }
  }

  private async clearProviderCache(organizationId: string): Promise<void> {
    try {
      const cacheKey = `providers:${organizationId}`;
      await this.redisService.del(cacheKey);
    } catch (error: any) {
      this.logger.error(`Failed to clear provider cache: ${error.message}`, error.stack);
    }
  }

  // Health check cron job
  @Cron(CronExpression.EVERY_5_MINUTES)
  async performHealthChecks(): Promise<void> {
    try {
      const providers = await this.providerRepository.find({
        where: { isActive: true }
      });

      for (const provider of providers) {
        try {
          // Perform health check (this would integrate with AIProviderIntegrationService)
          const healthStatus = await this.checkProviderHealth(provider);
          if (healthStatus !== provider.performanceMetrics?.healthStatus) {
            provider.performanceMetrics = {
              ...provider.performanceMetrics,
              healthStatus,
              lastChecked: new Date()
            };
            await this.providerRepository.save(provider);
          }
        } catch (error: any) {
          this.logger.error(`Health check failed for provider ${provider.id}: ${error.message}`);
        }
      }
    } catch (error: any) {
      this.logger.error(`Health check cron job failed: ${error.message}`, error.stack);
    }
  }

  private async checkProviderHealth(provider: AIProvider): Promise<'healthy' | 'degraded' | 'unhealthy'> {


    return 'healthy';
  }
}