"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Agent = exports.AgentCapability = exports.AgentStatus = exports.AgentType = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../users/user.entity");
const workflow_entity_1 = require("../workflows/workflow.entity");
const tool_entity_1 = require("../tools/tool.entity");
var AgentType;
(function (AgentType) {
    AgentType["CONDUCTOR"] = "conductor";
    AgentType["SPECIALIZED"] = "specialized";
    AgentType["REVIEW"] = "review";
    AgentType["COORDINATOR"] = "coordinator";
    AgentType["EXECUTOR"] = "executor";
})(AgentType || (exports.AgentType = AgentType = {}));
var AgentStatus;
(function (AgentStatus) {
    AgentStatus["DRAFT"] = "draft";
    AgentStatus["ACTIVE"] = "active";
    AgentStatus["INACTIVE"] = "inactive";
    AgentStatus["TRAINING"] = "training";
    AgentStatus["ERROR"] = "error";
    AgentStatus["DEPRECATED"] = "deprecated";
})(AgentStatus || (exports.AgentStatus = AgentStatus = {}));
var AgentCapability;
(function (AgentCapability) {
    AgentCapability["TEXT_GENERATION"] = "text_generation";
    AgentCapability["CODE_GENERATION"] = "code_generation";
    AgentCapability["DATA_ANALYSIS"] = "data_analysis";
    AgentCapability["API_INTEGRATION"] = "api_integration";
    AgentCapability["FILE_PROCESSING"] = "file_processing";
    AgentCapability["WEB_SCRAPING"] = "web_scraping";
    AgentCapability["EMAIL_PROCESSING"] = "email_processing";
    AgentCapability["DOCUMENT_PROCESSING"] = "document_processing";
    AgentCapability["IMAGE_PROCESSING"] = "image_processing";
    AgentCapability["AUDIO_PROCESSING"] = "audio_processing";
    AgentCapability["VIDEO_PROCESSING"] = "video_processing";
    AgentCapability["DATABASE_OPERATIONS"] = "database_operations";
    AgentCapability["WORKFLOW_ORCHESTRATION"] = "workflow_orchestration";
    AgentCapability["DECISION_MAKING"] = "decision_making";
    AgentCapability["LEARNING"] = "learning";
})(AgentCapability || (exports.AgentCapability = AgentCapability = {}));
let Agent = class Agent {
};
exports.Agent = Agent;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Agent.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Agent.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Agent.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: AgentType,
        default: AgentType.SPECIALIZED,
    }),
    __metadata("design:type", String)
], Agent.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: AgentStatus,
        default: AgentStatus.DRAFT,
    }),
    __metadata("design:type", String)
], Agent.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Array)
], Agent.prototype, "capabilities", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], Agent.prototype, "configuration", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Agent.prototype, "performance", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Agent.prototype, "learning", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Agent.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Agent.prototype, "permissions", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Agent.prototype, "monitoring", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, user => user.agents, { onDelete: 'CASCADE' }),
    __metadata("design:type", user_entity_1.User)
], Agent.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Agent.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Agent.prototype, "organizationId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => workflow_entity_1.Workflow, workflow => workflow.agents, { nullable: true }),
    __metadata("design:type", workflow_entity_1.Workflow)
], Agent.prototype, "workflow", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Agent.prototype, "workflowId", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => tool_entity_1.Tool, tool => tool.agent),
    __metadata("design:type", Array)
], Agent.prototype, "tools", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Agent.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Agent.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], Agent.prototype, "deletedAt", void 0);
exports.Agent = Agent = __decorate([
    (0, typeorm_1.Entity)('agents'),
    (0, typeorm_1.Index)(['userId', 'status']),
    (0, typeorm_1.Index)(['organizationId', 'type']),
    (0, typeorm_1.Index)(['capabilities', 'status'])
], Agent);
//# sourceMappingURL=agent.entity.js.map