import { Repository } from 'typeorm';
import { AgentTemplate, AgentType, ProviderType } from '../database/entities/agent.entity';
import { ApixGateway } from '../websocket/apix.gateway';
export interface CreateTemplateDto {
    name: string;
    category: string;
    description: string;
    config: Record<string, any>;
    skills: string[];
    isPublic?: boolean;
    promptTemplate: string;
    type: AgentType;
    supportedProviders: ProviderType[];
    metadata?: Record<string, any>;
}
export interface TemplateCategory {
    id: string;
    name: string;
    description: string;
    icon: string;
    templateCount: number;
}
export declare class AgentTemplatesService {
    private templateRepository;
    private apixGateway;
    private readonly logger;
    constructor(templateRepository: Repository<AgentTemplate>, apixGateway: ApixGateway);
    createTemplate(createTemplateDto: CreateTemplateDto, organizationId: string, userId: string): Promise<AgentTemplate>;
    getTemplatesByOrganization(organizationId: string): Promise<AgentTemplate[]>;
    getTemplateById(templateId: string, organizationId: string): Promise<AgentTemplate>;
    updateTemplate(templateId: string, updateData: Partial<CreateTemplateDto>, organizationId: string): Promise<AgentTemplate>;
    deleteTemplate(templateId: string, organizationId: string): Promise<void>;
    getTemplateCategories(organizationId: string): Promise<TemplateCategory[]>;
    getTemplatesByCategory(category: string, organizationId: string): Promise<AgentTemplate[]>;
    searchTemplates(query: string, organizationId: string): Promise<AgentTemplate[]>;
    getPopularTemplates(organizationId: string, limit?: number): Promise<AgentTemplate[]>;
    duplicateTemplate(templateId: string, organizationId: string, userId: string): Promise<AgentTemplate>;
    getTemplateUsageStats(templateId: string, organizationId: string): Promise<any>;
    seedDefaultTemplates(organizationId: string, userId: string): Promise<void>;
}
