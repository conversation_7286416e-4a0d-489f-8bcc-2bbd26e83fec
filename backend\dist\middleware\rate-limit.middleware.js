"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RateLimitMiddleware = void 0;
const common_1 = require("@nestjs/common");
const logger_service_1 = require("../logging/logger.service");
let RateLimitMiddleware = class RateLimitMiddleware {
    constructor(logger) {
        this.logger = logger;
        this.rateLimitStore = new Map();
    }
    use(req, res, next) {
        try {
            const clientId = this.getClientId(req);
            const rateLimit = this.getRateLimit(req);
            if (!clientId || !rateLimit) {
                return next();
            }
            const now = Date.now();
            const key = `${clientId}:${req.method}:${req.path}`;
            const current = this.rateLimitStore.get(key);
            if (!current || now > current.resetTime) {
                this.rateLimitStore.set(key, {
                    count: 1,
                    resetTime: now + rateLimit.windowMs,
                });
            }
            else if (current.count >= rateLimit.max) {
                this.logger.warn(`Rate limit exceeded for ${clientId}`, 'RateLimitMiddleware');
                return res.status(429).json({
                    error: 'Too Many Requests',
                    message: 'Rate limit exceeded. Please try again later.',
                    retryAfter: Math.ceil((current.resetTime - now) / 1000),
                });
            }
            else {
                current.count++;
            }
            const currentEntry = this.rateLimitStore.get(key);
            res.setHeader('X-RateLimit-Limit', rateLimit.max);
            res.setHeader('X-RateLimit-Remaining', Math.max(0, rateLimit.max - ((currentEntry === null || currentEntry === void 0 ? void 0 : currentEntry.count) || 1)));
            res.setHeader('X-RateLimit-Reset', Math.ceil(((currentEntry === null || currentEntry === void 0 ? void 0 : currentEntry.resetTime) || now + rateLimit.windowMs) / 1000));
            next();
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error(`Rate limit middleware error: ${errorMessage}`, 'RateLimitMiddleware');
            next();
        }
    }
    getClientId(req) {
        var _a;
        if ((_a = req.user) === null || _a === void 0 ? void 0 : _a.userId) {
            return req.user.userId;
        }
        const forwarded = req.headers['x-forwarded-for'];
        const ip = forwarded ? forwarded.split(',')[0] : req.ip || req.connection.remoteAddress;
        return ip || 'unknown';
    }
    getRateLimit(req) {
        const path = req.path;
        const method = req.method;
        if (path.startsWith('/api/auth')) {
            return { max: 5, windowMs: 60000 };
        }
        if (path.startsWith('/api/')) {
            return { max: 100, windowMs: 60000 };
        }
        if (path.startsWith('/health')) {
            return { max: 30, windowMs: 60000 };
        }
        return null;
    }
};
exports.RateLimitMiddleware = RateLimitMiddleware;
exports.RateLimitMiddleware = RateLimitMiddleware = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [logger_service_1.CustomLoggerService])
], RateLimitMiddleware);
//# sourceMappingURL=rate-limit.middleware.js.map