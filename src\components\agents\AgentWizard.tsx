'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Bo<PERSON>,
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Loader2,
  Play,
  Save,
  Eye,
  Copy,
  Star,
  Users,
  Zap,
  Brain,
  Settings,
  MessageSquare,
  Code,
  TrendingUp,
  Headphones,
  Megaphone,
  DollarSign,
  Settings as SettingsIcon,
  Code as CodeIcon,
} from 'lucide-react';

interface AgentWizardData {
  step: number;
  name: string;
  description: string;
  templateId: string;
  type: string;
  primaryProvider: string;
  fallbackProviders: string[];
  config: {
    temperature: number;
    maxTokens: number;
    systemPrompt: string;
    skills: string[];
    capabilities: string[];
    personality: string;
    responseStyle: string;
  };
  memoryConfig: {
    maxTokens: number;
    retentionDays: number;
    enableLongTerm: boolean;
  };
  testMessages: string[];
  isPublic: boolean;
}

const personalityOptions = [
  { value: 'professional', label: 'Professional', description: 'Formal and business-like' },
  { value: 'friendly', label: 'Friendly', description: 'Warm and approachable' },
  { value: 'enthusiastic', label: 'Enthusiastic', description: 'Energetic and positive' },
  { value: 'analytical', label: 'Analytical', description: 'Logical and data-driven' },
  { value: 'creative', label: 'Creative', description: 'Imaginative and innovative' },
  { value: 'helpful', label: 'Helpful', description: 'Supportive and service-oriented' },
];

const responseStyleOptions = [
  { value: 'concise', label: 'Concise', description: 'Brief and to the point' },
  { value: 'detailed', label: 'Detailed', description: 'Comprehensive explanations' },
  { value: 'conversational', label: 'Conversational', description: 'Natural dialogue style' },
  { value: 'technical', label: 'Technical', description: 'Specialized terminology' },
  { value: 'educational', label: 'Educational', description: 'Teaching and explaining' },
  { value: 'creative', label: 'Creative', description: 'Imaginative and original' },
];

const capabilityOptions = [
  { value: 'chat', label: 'Chat', icon: MessageSquare, description: 'Basic conversation' },
  { value: 'vision', label: 'Vision', icon: Eye, description: 'Image analysis' },
  { value: 'function-calling', label: 'Function Calling', icon: Zap, description: 'API integration' },
  { value: 'code-generation', label: 'Code Generation', icon: Code, description: 'Programming assistance' },
  { value: 'analysis', label: 'Analysis', icon: Brain, description: 'Data analysis' },
];

const categoryIcons: Record<string, any> = {
  'customer-support': Headphones,
  'sales': TrendingUp,
  'marketing': Megaphone,
  'hr': Users,
  'finance': DollarSign,
  'operations': SettingsIcon,
  'development': CodeIcon,
  'general': Bot,
};

export default function AgentWizard() {
  const [currentStep, setCurrentStep] = useState(1);
  const [wizardData, setWizardData] = useState<AgentWizardData>({
    step: 1,
    name: '',
    description: '',
    templateId: '',
    type: 'BASIC',
    primaryProvider: '',
    fallbackProviders: [],
    config: {
      temperature: 0.7,
      maxTokens: 1000,
      systemPrompt: '',
      skills: [],
      capabilities: ['chat'],
      personality: 'helpful',
      responseStyle: 'conversational',
    },
    memoryConfig: {
      maxTokens: 4000,
      retentionDays: 30,
      enableLongTerm: false,
    },
    testMessages: [],
    isPublic: false,
  });

  const [templates, setTemplates] = useState<any[]>([]);
  const [providers, setProviders] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [testMessage, setTestMessage] = useState('');
  const [testResult, setTestResult] = useState<any>(null);
  const [isTesting, setIsTesting] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      const [templatesRes, providersRes] = await Promise.all([
        fetch('/api/v1/agent-templates'),
        fetch('/api/v1/providers/active'),
      ]);

      const templatesData = await templatesRes.json();
      const providersData = await providersRes.json();

      if (templatesData.success) setTemplates(templatesData.data);
      if (providersData.success) setProviders(providersData.data);
    } catch (error) {
      console.error('Failed to load wizard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const validateStep = (step: number): boolean => {
    const stepErrors: Record<string, string> = {};

    switch (step) {
      case 1:
        if (!wizardData.name.trim()) {
          stepErrors.name = 'Agent name is required';
        }
        if (!wizardData.description.trim()) {
          stepErrors.description = 'Description is required';
        }
        break;

      case 2:
        if (!wizardData.type) {
          stepErrors.type = 'Agent type is required';
        }
        break;

      case 3:
        if (!wizardData.config.personality) {
          stepErrors.personality = 'Personality is required';
        }
        if (!wizardData.config.responseStyle) {
          stepErrors.responseStyle = 'Response style is required';
        }
        if (!wizardData.config.systemPrompt.trim()) {
          stepErrors.systemPrompt = 'System prompt is required';
        }
        break;

      case 4:
        if (!wizardData.primaryProvider) {
          stepErrors.primaryProvider = 'Primary provider is required';
        }
        break;

      case 5:
        if (wizardData.config.capabilities.length === 0) {
          stepErrors.capabilities = 'At least one capability must be selected';
        }
        break;
    }

    setErrors(stepErrors);
    return Object.keys(stepErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 7));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setWizardData(prev => ({
        ...prev,
        templateId: template.id,
        name: template.name,
        type: template.type,
        config: {
          ...prev.config,
          systemPrompt: template.promptTemplate,
          skills: template.skills,
          capabilities: template.skills.includes('vision') ? ['chat', 'vision'] : ['chat'],
        },
        primaryProvider: template.supportedProviders[0] || '',
        fallbackProviders: template.supportedProviders.slice(1) || [],
      }));
    }
  };

  const handleCreateAgent = async () => {
    if (!validateStep(currentStep)) return;

    setIsCreating(true);
    try {
      const response = await fetch('/api/v1/agents/wizard', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(wizardData),
      });

      const result = await response.json();

      if (result.success) {
        window.location.href = '/dashboard/agents';
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Failed to create agent:', error);
      setErrors({ general: error instanceof Error ? error.message : 'Failed to create agent' });
    } finally {
      setIsCreating(false);
    }
  };

  const handleTestAgent = async () => {
    if (!testMessage.trim()) return;

    setIsTesting(true);
    try {
      setTimeout(() => {
        setTestResult({
          success: true,
          output: `This is a test response from your agent. You said: "${testMessage}"`,
          metadata: {
            provider: wizardData.primaryProvider,
            model: 'gpt-3.5-turbo',
            tokens: { input: 10, output: 20, total: 30 },
            duration: 500,
            cost: 0.001,
          },
        });
        setIsTesting(false);
      }, 1000);
    } catch (error) {
      setTestResult({
        success: false,
        error: error instanceof Error ? error.message : 'Test failed',
      });
      setIsTesting(false);
    }
  };

  const getStepProgress = () => {
    return (currentStep / 7) * 100;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading Agent Wizard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.history.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div className="flex-1">
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <Bot className="h-8 w-8 text-blue-600" />
                Agent Wizard
              </h1>
              <p className="text-gray-600 mt-2">Create your AI agent step by step</p>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">
                Step {currentStep} of 7
              </span>
              <span className="text-sm text-gray-500">
                {Math.round(getStepProgress())}% Complete
              </span>
            </div>
            <Progress value={getStepProgress()} className="h-2" />
          </div>
        </div>

        {/* Step Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Step {currentStep}: {getStepTitle(currentStep)}
            </CardTitle>
            <CardDescription>
              {getStepDescription(currentStep)}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {errors.general && (
              <Alert className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{errors.general}</AlertDescription>
              </Alert>
            )}

            {renderStepContent()}

            <Separator className="my-6" />

            {/* Navigation */}
            <div className="flex justify-between">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 1}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>

              <div className="flex gap-2">
                {currentStep < 7 ? (
                  <Button onClick={handleNext}>
                    Next
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                ) : (
                  <Button
                    onClick={handleCreateAgent}
                    disabled={isCreating}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {isCreating ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Creating Agent...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Create Agent
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  function getStepTitle(step: number): string {
    const titles = [
      'Basic Information',
      'Template Selection',
      'Personality & Style',
      'AI Provider',
      'Capabilities',
      'Memory Settings',
      'Test & Validate'
    ];
    return titles[step - 1] || `Step ${step}`;
  }

  function getStepDescription(step: number): string {
    const descriptions = [
      'Set your agent\'s name and basic details',
      'Choose a template or start from scratch',
      'Define your agent\'s personality and response style',
      'Select your preferred AI provider and models',
      'Choose what your agent can do',
      'Configure how your agent remembers conversations',
      'Test your agent with sample conversations'
    ];
    return descriptions[step - 1] || 'Configure your agent';
  }

  function renderStepContent() {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <Label htmlFor="name">Agent Name</Label>
              <Input
                id="name"
                value={wizardData.name}
                onChange={(e) => setWizardData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter your agent's name"
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={wizardData.description}
                onChange={(e) => setWizardData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe what your agent does"
                rows={3}
                className={errors.description ? 'border-red-500' : ''}
              />
              {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div>
              <Label>Agent Type</Label>
              <Select
                value={wizardData.type}
                onValueChange={(value) => setWizardData(prev => ({ ...prev, type: value }))}
              >
                <SelectTrigger className={errors.type ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select agent type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="BASIC">Basic Agent</SelectItem>
                  <SelectItem value="TOOL_DRIVEN">Tool-Driven Agent</SelectItem>
                  <SelectItem value="HYBRID">Hybrid Agent</SelectItem>
                  <SelectItem value="MULTI_TASK">Multi-Task Agent</SelectItem>
                  <SelectItem value="MULTI_PROVIDER">Multi-Provider Agent</SelectItem>
                  <SelectItem value="COLLABORATIVE">Collaborative Agent</SelectItem>
                </SelectContent>
              </Select>
              {errors.type && <p className="text-red-500 text-sm mt-1">{errors.type}</p>}
            </div>

            <div>
              <Label>Agent Template (Optional)</Label>
              <ScrollArea className="h-64">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  {templates.map((template) => {
                    const Icon = categoryIcons[template.category] || Bot;
                    return (
                      <div
                        key={template.id}
                        className={`p-4 border rounded-lg cursor-pointer transition-all ${wizardData.templateId === template.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                          }`}
                        onClick={() => handleTemplateSelect(template.id)}
                      >
                        <div className="flex items-start gap-3">
                          <Icon className="h-5 w-5 text-gray-600 mt-1" />
                          <div className="flex-1">
                            <h3 className="font-medium text-gray-900">{template.name}</h3>
                            <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                            <div className="flex items-center gap-2 mt-2">
                              <Badge variant="outline" className="text-xs">
                                {template.category}
                              </Badge>
                              {template.isPublic && (
                                <Badge variant="secondary" className="text-xs">
                                  Public
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </ScrollArea>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <Label>Personality</Label>
              <Select
                value={wizardData.config.personality}
                onValueChange={(value) => setWizardData(prev => ({
                  ...prev,
                  config: { ...prev.config, personality: value }
                }))}
              >
                <SelectTrigger className={errors.personality ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select personality" />
                </SelectTrigger>
                <SelectContent>
                  {personalityOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div>
                        <div className="font-medium">{option.label}</div>
                        <div className="text-sm text-gray-500">{option.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.personality && <p className="text-red-500 text-sm mt-1">{errors.personality}</p>}
            </div>

            <div>
              <Label>Response Style</Label>
              <Select
                value={wizardData.config.responseStyle}
                onValueChange={(value) => setWizardData(prev => ({
                  ...prev,
                  config: { ...prev.config, responseStyle: value }
                }))}
              >
                <SelectTrigger className={errors.responseStyle ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select response style" />
                </SelectTrigger>
                <SelectContent>
                  {responseStyleOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div>
                        <div className="font-medium">{option.label}</div>
                        <div className="text-sm text-gray-500">{option.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.responseStyle && <p className="text-red-500 text-sm mt-1">{errors.responseStyle}</p>}
            </div>

            <div>
              <Label htmlFor="systemPrompt">System Prompt</Label>
              <Textarea
                id="systemPrompt"
                value={wizardData.config.systemPrompt}
                onChange={(e) => setWizardData(prev => ({
                  ...prev,
                  config: { ...prev.config, systemPrompt: e.target.value }
                }))}
                placeholder="Define your agent's role and behavior..."
                rows={6}
                className={errors.systemPrompt ? 'border-red-500' : ''}
              />
              {errors.systemPrompt && <p className="text-red-500 text-sm mt-1">{errors.systemPrompt}</p>}
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div>
              <Label>Primary Provider</Label>
              <Select
                value={wizardData.primaryProvider}
                onValueChange={(value) => setWizardData(prev => ({ ...prev, primaryProvider: value }))}
              >
                <SelectTrigger className={errors.primaryProvider ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select primary provider" />
                </SelectTrigger>
                <SelectContent>
                  {providers.map((provider) => (
                    <SelectItem key={provider.id} value={provider.id}>
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${provider.performanceMetrics?.healthStatus === 'healthy' ? 'bg-green-500' :
                            provider.performanceMetrics?.healthStatus === 'degraded' ? 'bg-yellow-500' : 'bg-red-500'
                          }`} />
                        {provider.name} ({provider.type})
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.primaryProvider && <p className="text-red-500 text-sm mt-1">{errors.primaryProvider}</p>}
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <div>
              <Label>Capabilities</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                {capabilityOptions.map((capability) => {
                  const Icon = capability.icon;
                  return (
                    <div
                      key={capability.value}
                      className={`p-4 border rounded-lg cursor-pointer transition-all ${wizardData.config.capabilities.includes(capability.value)
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                        }`}
                      onClick={() => {
                        const capabilities = wizardData.config.capabilities.includes(capability.value)
                          ? wizardData.config.capabilities.filter(c => c !== capability.value)
                          : [...wizardData.config.capabilities, capability.value];
                        setWizardData(prev => ({
                          ...prev,
                          config: { ...prev.config, capabilities }
                        }));
                      }}
                    >
                      <div className="flex items-start gap-3">
                        <Icon className="h-5 w-5 text-gray-600 mt-1" />
                        <div>
                          <h3 className="font-medium text-gray-900">{capability.label}</h3>
                          <p className="text-sm text-gray-600 mt-1">{capability.description}</p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
              {errors.capabilities && <p className="text-red-500 text-sm mt-1">{errors.capabilities}</p>}
            </div>
          </div>
        );

      case 6:
        return (
          <div className="space-y-6">
            <div>
              <Label>Memory Token Limit: {wizardData.memoryConfig.maxTokens}</Label>
              <Slider
                value={[wizardData.memoryConfig.maxTokens]}
                onValueChange={([value]) => setWizardData(prev => ({
                  ...prev,
                  memoryConfig: { ...prev.memoryConfig, maxTokens: value }
                }))}
                max={16000}
                min={1000}
                step={1000}
                className="mt-2"
              />
              <p className="text-sm text-gray-600 mt-1">
                How much conversation history to remember
              </p>
            </div>

            <div>
              <Label>Retention Days: {wizardData.memoryConfig.retentionDays}</Label>
              <Slider
                value={[wizardData.memoryConfig.retentionDays]}
                onValueChange={([value]) => setWizardData(prev => ({
                  ...prev,
                  memoryConfig: { ...prev.memoryConfig, retentionDays: value }
                }))}
                max={365}
                min={1}
                step={1}
                className="mt-2"
              />
              <p className="text-sm text-gray-600 mt-1">
                How long to keep conversation history
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="longTerm"
                checked={wizardData.memoryConfig.enableLongTerm}
                onCheckedChange={(checked) => setWizardData(prev => ({
                  ...prev,
                  memoryConfig: { ...prev.memoryConfig, enableLongTerm: checked }
                }))}
              />
              <Label htmlFor="longTerm">Enable Long-term Memory</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isPublic"
                checked={wizardData.isPublic}
                onCheckedChange={(checked) => setWizardData(prev => ({
                  ...prev,
                  isPublic: checked
                }))}
              />
              <Label htmlFor="isPublic">Make Agent Public</Label>
            </div>
          </div>
        );

      case 7:
        return (
          <div className="space-y-6">
            <div>
              <Label htmlFor="testMessage">Test Message</Label>
              <Textarea
                id="testMessage"
                value={testMessage}
                onChange={(e) => setTestMessage(e.target.value)}
                placeholder="Enter a message to test your agent..."
                rows={3}
              />
            </div>

            <Button
              onClick={handleTestAgent}
              disabled={isTesting || !testMessage.trim()}
              className="w-full"
            >
              {isTesting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Testing Agent...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Test Agent
                </>
              )}
            </Button>

            {testResult && (
              <div className="mt-4">
                <Label>Test Result</Label>
                <div className={`p-4 rounded-lg border mt-2 ${testResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
                  }`}>
                  {testResult.success ? (
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="font-medium text-green-800">Success</span>
                      </div>
                      <p className="text-gray-800 mb-2">{testResult.output}</p>
                      {testResult.metadata && (
                        <div className="text-sm text-gray-600">
                          <div>Provider: {testResult.metadata.provider}</div>
                          <div>Model: {testResult.metadata.model}</div>
                          <div>Tokens: {testResult.metadata.tokens?.total}</div>
                          <div>Duration: {testResult.metadata.duration}ms</div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <AlertCircle className="h-4 w-4 text-red-600" />
                        <span className="font-medium text-red-800">Error</span>
                      </div>
                      <p className="text-red-800">{testResult.error}</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        );

      default:
        return <div>Step content not found</div>;
    }
  }
}