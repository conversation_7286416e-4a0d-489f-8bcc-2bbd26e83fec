import { AgentRuntimeService } from '../agents/execution/agent-runtime.service';
import { ToolRuntimeService } from '../tools/execution/tool-runtime.service';
import { WorkflowRuntimeService } from '../workflows/execution/workflow-runtime.service';
import { AIProviderIntegrationService } from '../providers/ai-provider-integration.service';
import { LoggerService } from '../common/services/logger.service';
export declare class ExecutionController {
    private agentRuntime;
    private toolRuntime;
    private workflowRuntime;
    private aiProvider;
    private logger;
    constructor(agentRuntime: AgentRuntimeService, toolRuntime: ToolRuntimeService, workflowRuntime: WorkflowRuntimeService, aiProvider: AIProviderIntegrationService, logger: LoggerService);
    executeAgent(agentId: string, body: {
        input: string;
        sessionId?: string;
        variables?: Record<string, any>;
        tools?: string[];
    }, req: any): Promise<{
        success: boolean;
        data: import("../agents/execution/agent-runtime.service").AgentExecutionResult;
        error?: undefined;
    } | {
        success: boolean;
        error: string;
        data?: undefined;
    }>;
    executeTool(toolId: string, body: {
        parameters: Record<string, any>;
        agentId?: string;
        sessionId?: string;
        timeout?: number;
    }, req: any): Promise<{
        success: boolean;
        data: import("../tools/execution/tool-runtime.service").ToolExecutionResult;
        error?: undefined;
    } | {
        success: boolean;
        error: string;
        data?: undefined;
    }>;
    executeWorkflow(workflowId: string, body: {
        input: Record<string, any>;
        variables?: Record<string, any>;
        sessionId?: string;
    }, req: any): Promise<{
        success: boolean;
        data: import("../workflows/execution/workflow-runtime.service").WorkflowExecutionResult;
        error?: undefined;
    } | {
        success: boolean;
        error: string;
        data?: undefined;
    }>;
    aiComplete(body: {
        provider?: string;
        model: string;
        prompt: string;
        temperature?: number;
        maxTokens?: number;
        stream?: boolean;
    }, req: any): Promise<{
        success: boolean;
        data: import("../providers/ai-provider-integration.service").AIResponse;
        error?: undefined;
    } | {
        success: boolean;
        error: string;
        data?: undefined;
    }>;
    getAgentTools(agentId: string, req: any): Promise<{
        success: boolean;
        data: import("../tools/execution/tool-runtime.service").ToolDefinition[];
        error?: undefined;
    } | {
        success: boolean;
        error: string;
        data?: undefined;
    }>;
    getProviders(req: any): Promise<{
        success: boolean;
        data: any;
        error?: undefined;
    } | {
        success: boolean;
        error: string;
        data?: undefined;
    }>;
    healthCheck(): Promise<{
        success: boolean;
        data: {
            status: string;
            timestamp: string;
            services: {
                agentRuntime: string;
                toolRuntime: string;
                workflowRuntime: string;
                aiProviders: string;
            };
        };
    }>;
}
