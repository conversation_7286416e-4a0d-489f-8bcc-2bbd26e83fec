import { CanActivate, ExecutionContext } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
export interface RateLimitOptions {
    ttl: number;
    limit: number;
    skipIf?: (request: Request) => boolean;
    keyGenerator?: (request: Request) => string;
}
export declare const RATE_LIMIT_KEY = "rate-limit";
export declare const RateLimit: (options: RateLimitOptions) => (target: any, propertyKey?: string | symbol, descriptor?: PropertyDescriptor) => void;
export declare class RateLimitGuard implements CanActivate {
    private reflector;
    private configService;
    private redis;
    constructor(reflector: Reflector, configService: ConfigService);
    canActivate(context: ExecutionContext): Promise<boolean>;
    private getDefaultKey;
}
export declare const ApiRateLimit: (options?: Partial<RateLimitOptions>) => (target: any, propertyKey?: string | symbol, descriptor?: PropertyDescriptor) => void;
