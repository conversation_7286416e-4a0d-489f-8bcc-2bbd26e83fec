import { Injectable, NestMiddleware, ForbiddenException } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { CustomLoggerService } from '../logging/logger.service';

@Injectable()
export class TenantContextMiddleware implements NestMiddleware {
    constructor(private readonly logger: CustomLoggerService) { }

    use(req: Request, res: Response, next: NextFunction) {
        try {
            const tenantId = this.extractTenantId(req);

            if (tenantId) {
                (req as any)['tenantId'] = tenantId;
                this.logger.log(`Tenant context set: ${tenantId}`, 'TenantContextMiddleware');
            }

            next();
        } catch (error) {
            this.logger.error(`Tenant context middleware error: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : '', 'TenantContextMiddleware');
            next();
        }
    }

    private extractTenantId(req: Request): string | null {
        // Extract tenant ID from subdomain
        const host = req.headers.host;
        if (host) {
            const subdomain = host.split('.')[0];
            if (subdomain && subdomain !== 'www' && subdomain !== 'api') {
                return subdomain;
            }
        }

        // Extract tenant ID from header
        const tenantHeader = req.headers['x-tenant-id'] as string;
        if (tenantHeader) {
            return tenantHeader;
        }

        // Extract tenant ID from query parameter
        const tenantQuery = req.query.tenant as string;
        if (tenantQuery) {
            return tenantQuery;
        }

        return null;
    }
} 