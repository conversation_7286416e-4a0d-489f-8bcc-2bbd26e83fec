"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UniversalSDKService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UniversalSDKService = void 0;
const common_1 = require("@nestjs/common");
const ai_provider_integration_service_1 = require("./ai-provider-integration.service");
const smart_routing_service_1 = require("./smart-routing.service");
const logger_service_1 = require("../common/services/logger.service");
const prisma_service_1 = require("../prisma/prisma.service");
let UniversalSDKService = UniversalSDKService_1 = class UniversalSDKService {
    constructor(aiProvider, smartRouting, loggerService, prisma) {
        this.aiProvider = aiProvider;
        this.smartRouting = smartRouting;
        this.loggerService = loggerService;
        this.prisma = prisma;
        this.logger = new common_1.Logger(UniversalSDKService_1.name);
    }
    async complete(request) {
        var _a;
        const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const startTime = Date.now();
        try {
            let selectedProvider = request.provider;
            let selectedModel = request.model;
            let routingInfo = null;
            if (!selectedProvider || !selectedModel) {
                const routingRequest = {
                    organizationId: request.organizationId,
                    priority: request.priority || 'balanced',
                    maxLatency: request.maxLatency,
                    maxCost: request.maxCost,
                    requiresStreaming: request.requiresStreaming,
                };
                const routing = await this.smartRouting.selectOptimalProvider(routingRequest);
                selectedProvider = routing.providerId;
                selectedModel = routing.modelId;
                routingInfo = routing;
            }
            const aiRequest = this.buildAIRequest(request, selectedProvider, selectedModel, requestId);
            const result = await this.executeWithFallback(aiRequest, (routingInfo === null || routingInfo === void 0 ? void 0 : routingInfo.fallbacks) || []);
            const latency = Date.now() - startTime;
            await this.logRequest(request, result, latency, requestId);
            return {
                content: result.content,
                finishReason: result.finishReason,
                provider: result.providerId,
                model: result.modelId,
                usage: result.usage,
                cost: result.cost,
                latency,
                requestId,
                timestamp: new Date(),
                routing: {
                    selected: selectedProvider,
                    confidence: (routingInfo === null || routingInfo === void 0 ? void 0 : routingInfo.confidence) || 1.0,
                    reasoning: (routingInfo === null || routingInfo === void 0 ? void 0 : routingInfo.reasoning) || 'Manually specified',
                    fallbacksAvailable: ((_a = routingInfo === null || routingInfo === void 0 ? void 0 : routingInfo.fallbacks) === null || _a === void 0 ? void 0 : _a.length) || 0,
                },
            };
        }
        catch (error) {
            const latency = Date.now() - startTime;
            this.loggerService.error('Universal AI request failed', {
                requestId,
                organizationId: request.organizationId,
                userId: request.userId,
                error: error instanceof Error ? error.message : 'Unknown error',
                latency,
            });
            throw error;
        }
    }
    async stream(request) {
        const requestId = `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        try {
            let selectedProvider = request.provider;
            let selectedModel = request.model;
            if (!selectedProvider || !selectedModel) {
                const routingRequest = {
                    organizationId: request.organizationId,
                    priority: request.priority || 'speed',
                    requiresStreaming: true,
                };
                const routing = await this.smartRouting.selectOptimalProvider(routingRequest);
                selectedProvider = routing.providerId;
                selectedModel = routing.modelId;
            }
            const aiRequest = this.buildAIRequest(request, selectedProvider, selectedModel, requestId);
            aiRequest.stream = true;
            const stream = await this.aiProvider.streamRequest(aiRequest);
            this.loggerService.business('streaming_started', {
                requestId,
                provider: selectedProvider,
                model: selectedModel,
                organizationId: request.organizationId,
                userId: request.userId,
            });
            return {
                requestId,
                provider: selectedProvider,
                model: selectedModel,
                stream,
            };
        }
        catch (error) {
            this.loggerService.error('Streaming request failed', {
                requestId,
                organizationId: request.organizationId,
                error: error instanceof Error ? error.message : 'Unknown error',
            });
            throw error;
        }
    }
    buildAIRequest(request, provider, model, requestId) {
        const messages = [];
        if (request.systemPrompt) {
            messages.push({ role: 'system', content: request.systemPrompt });
        }
        if (request.context) {
            messages.push(...request.context);
        }
        messages.push({ role: 'user', content: request.prompt });
        return {
            requestId,
            providerId: provider,
            modelId: model,
            messages,
            temperature: request.temperature,
            maxTokens: request.maxTokens,
            topP: request.topP,
            frequencyPenalty: request.frequencyPenalty,
            presencePenalty: request.presencePenalty,
            stop: request.stop,
            organizationId: request.organizationId,
            userId: request.userId,
            sessionId: request.sessionId,
            tools: request.tools,
            stream: false,
        };
    }
    async executeWithFallback(aiRequest, fallbacks) {
        const attempts = [
            { providerId: aiRequest.providerId, modelId: aiRequest.modelId },
            ...fallbacks.map(f => ({ providerId: f.providerId, modelId: f.modelId })),
        ];
        let lastError = null;
        for (let i = 0; i < attempts.length; i++) {
            const attempt = attempts[i];
            try {
                const requestWithProvider = Object.assign(Object.assign({}, aiRequest), { providerId: attempt.providerId, modelId: attempt.modelId });
                const result = await this.aiProvider.processRequest(requestWithProvider);
                if (i > 0) {
                    this.loggerService.business('fallback_success', {
                        requestId: aiRequest.requestId,
                        originalProvider: attempts[0].providerId,
                        successfulProvider: attempt.providerId,
                        attemptNumber: i + 1,
                    });
                }
                return result;
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error('Unknown error');
                this.loggerService.warn('Provider attempt failed', {
                    requestId: aiRequest.requestId,
                    provider: attempt.providerId,
                    model: attempt.modelId,
                    attemptNumber: i + 1,
                    error: lastError.message,
                });
                continue;
            }
        }
        throw new Error(`All providers failed. Last error: ${lastError === null || lastError === void 0 ? void 0 : lastError.message}`);
    }
    async logRequest(request, result, latency, requestId) {
        try {
            await this.prisma.aIRequest.create({
                data: {
                    id: requestId,
                    organizationId: request.organizationId,
                    userId: request.userId,
                    sessionId: request.sessionId,
                    provider: result.providerId,
                    model: result.modelId,
                    prompt: request.prompt,
                    response: result.content,
                    tokens: result.usage,
                    cost: result.cost,
                    latency,
                    status: 'COMPLETED',
                    metadata: {
                        priority: request.priority,
                        temperature: request.temperature,
                        maxTokens: request.maxTokens,
                        tags: request.tags,
                    },
                },
            });
            this.loggerService.business('ai_request_completed', {
                requestId,
                organizationId: request.organizationId,
                userId: request.userId,
                provider: result.providerId,
                model: result.modelId,
                tokens: result.usage.totalTokens,
                cost: result.cost,
                latency,
            });
        }
        catch (error) {
            this.logger.warn(`Failed to log request: ${error}`);
        }
    }
    async getAvailableModels(organizationId) {
        const providers = await this.prisma.aIProvider.findMany({
            where: {
                organizationId,
                isActive: true,
            },
            include: {
                aiModels: {
                    where: { isActive: true },
                },
            },
        });
        return providers.map(provider => ({
            provider: provider.name,
            models: provider.aiModels.map(model => ({
                id: model.id,
                name: model.name,
                capabilities: model.capabilities || [],
                costPerToken: model.costPerToken || 0,
            })),
        }));
    }
    async getUsageStats(organizationId, timeframe = 'day') {
        const timeMap = {
            hour: 1,
            day: 24,
            week: 24 * 7,
            month: 24 * 30,
        };
        const hoursAgo = timeMap[timeframe];
        const since = new Date(Date.now() - hoursAgo * 60 * 60 * 1000);
        const stats = await this.prisma.aIRequest.aggregate({
            where: {
                organizationId,
                createdAt: { gte: since },
                status: 'COMPLETED',
            },
            _sum: {
                cost: true,
            },
            _avg: {
                latency: true,
            },
            _count: {
                id: true,
            },
        });
        const tokenStats = await this.prisma.aIRequest.findMany({
            where: {
                organizationId,
                createdAt: { gte: since },
                status: 'COMPLETED',
            },
            select: {
                tokens: true,
            },
        });
        const totalTokens = tokenStats.reduce((sum, req) => {
            const tokens = req.tokens;
            return sum + ((tokens === null || tokens === void 0 ? void 0 : tokens.totalTokens) || 0);
        }, 0);
        return {
            requests: stats._count.id || 0,
            totalCost: stats._sum.cost || 0,
            totalTokens,
            averageLatency: stats._avg.latency || 0,
            timeframe,
        };
    }
};
exports.UniversalSDKService = UniversalSDKService;
exports.UniversalSDKService = UniversalSDKService = UniversalSDKService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [ai_provider_integration_service_1.AIProviderIntegrationService,
        smart_routing_service_1.SmartRoutingService,
        logger_service_1.LoggerService,
        prisma_service_1.PrismaService])
], UniversalSDKService);
//# sourceMappingURL=universal-sdk.service.js.map