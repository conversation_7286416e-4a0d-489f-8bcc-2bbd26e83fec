"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __await = (this && this.__await) || function (v) { return this instanceof __await ? (this.v = v, this) : new __await(v); }
var __asyncValues = (this && this.__asyncValues) || function (o) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var m = o[Symbol.asyncIterator], i;
    return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function () { return this; }, i);
    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }
    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }
};
var __asyncGenerator = (this && this.__asyncGenerator) || function (thisArg, _arguments, generator) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var g = generator.apply(thisArg, _arguments || []), i, q = [];
    return i = Object.create((typeof AsyncIterator === "function" ? AsyncIterator : Object).prototype), verb("next"), verb("throw"), verb("return", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;
    function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }
    function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }
    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }
    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }
    function fulfill(value) { resume("next", value); }
    function reject(value) { resume("throw", value); }
    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }
};
var AIProviderStreamService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIProviderStreamService = void 0;
const common_1 = require("@nestjs/common");
const ai_provider_manager_service_1 = require("./ai-provider-manager.service");
const ai_provider_selector_service_1 = require("./ai-provider-selector.service");
const openai_1 = require("openai");
const sdk_1 = require("@anthropic-ai/sdk");
const generative_ai_1 = require("@google/generative-ai");
const mistralai_1 = require("@mistralai/mistralai");
const groq_sdk_1 = require("groq-sdk");
let AIProviderStreamService = AIProviderStreamService_1 = class AIProviderStreamService {
    constructor(aiProviderManager, aiProviderSelector) {
        this.aiProviderManager = aiProviderManager;
        this.aiProviderSelector = aiProviderSelector;
        this.logger = new common_1.Logger(AIProviderStreamService_1.name);
        this.providerClients = new Map();
    }
    async streamRequest(request) {
        const startTime = Date.now();
        let selectedProvider;
        try {
            if (!request.providerId) {
                const selection = await this.aiProviderSelector.selectOptimalProvider({
                    organizationId: request.organizationId,
                    capabilities: ['chat'],
                });
                selectedProvider = await this.aiProviderManager.getProviderById(selection.providerId, request.organizationId);
            }
            else {
                selectedProvider = await this.aiProviderManager.getProviderById(request.providerId, request.organizationId);
            }
            if (!selectedProvider) {
                throw new Error('No suitable provider available for streaming');
            }
            const model = selectedProvider.models.find((m) => m.id === request.modelId) || selectedProvider.models[0];
            if (!model) {
                throw new Error('Model not available');
            }
            switch (selectedProvider.type) {
                case 'OPENAI':
                    return this.streamOpenAI(selectedProvider, model, request);
                case 'CLAUDE':
                    return this.streamClaude(selectedProvider, model, request);
                case 'GEMINI':
                    return this.streamGemini(selectedProvider, model, request);
                case 'MISTRAL':
                    return this.streamMistral(selectedProvider, model, request);
                case 'GROQ':
                    return this.streamGroq(selectedProvider, model, request);
                default:
                    throw new Error(`Streaming not supported for provider type: ${selectedProvider.type}`);
            }
        }
        catch (error) {
            this.logger.error(`Streaming request failed: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }
    streamOpenAI(provider, model, request) {
        return __asyncGenerator(this, arguments, function* streamOpenAI_1() {
            var _a, e_1, _b, _c;
            var _d, _e;
            const client = this.getOrCreateClient(provider.id, () => new openai_1.OpenAI({
                apiKey: provider.config.apiKey,
            }));
            const stream = yield __await(client.chat.completions.create({
                model: model.name,
                messages: request.messages,
                temperature: request.temperature || 0.7,
                max_tokens: request.maxTokens || 1000,
                stream: true,
            }));
            try {
                for (var _f = true, stream_1 = __asyncValues(stream), stream_1_1; stream_1_1 = yield __await(stream_1.next()), _a = stream_1_1.done, !_a; _f = true) {
                    _c = stream_1_1.value;
                    _f = false;
                    const chunk = _c;
                    const content = (_e = (_d = chunk.choices[0]) === null || _d === void 0 ? void 0 : _d.delta) === null || _e === void 0 ? void 0 : _e.content;
                    if (content) {
                        yield yield __await(content);
                    }
                }
            }
            catch (e_1_1) { e_1 = { error: e_1_1 }; }
            finally {
                try {
                    if (!_f && !_a && (_b = stream_1.return)) yield __await(_b.call(stream_1));
                }
                finally { if (e_1) throw e_1.error; }
            }
        });
    }
    streamClaude(provider, model, request) {
        return __asyncGenerator(this, arguments, function* streamClaude_1() {
            var _a, e_2, _b, _c;
            const client = this.getOrCreateClient(provider.id, () => new sdk_1.Anthropic({
                apiKey: provider.config.apiKey,
            }));
            const stream = yield __await(client.messages.create({
                model: model.name,
                max_tokens: request.maxTokens || 1000,
                messages: request.messages,
                stream: true,
            }));
            try {
                for (var _d = true, stream_2 = __asyncValues(stream), stream_2_1; stream_2_1 = yield __await(stream_2.next()), _a = stream_2_1.done, !_a; _d = true) {
                    _c = stream_2_1.value;
                    _d = false;
                    const chunk = _c;
                    if (chunk.type === 'content_block_delta') {
                        yield yield __await(chunk.delta.text);
                    }
                }
            }
            catch (e_2_1) { e_2 = { error: e_2_1 }; }
            finally {
                try {
                    if (!_d && !_a && (_b = stream_2.return)) yield __await(_b.call(stream_2));
                }
                finally { if (e_2) throw e_2.error; }
            }
        });
    }
    streamGemini(provider, model, request) {
        return __asyncGenerator(this, arguments, function* streamGemini_1() {
            var _a, e_3, _b, _c;
            const client = this.getOrCreateClient(provider.id, () => new generative_ai_1.GoogleGenerativeAI(provider.config.apiKey));
            const geminiModel = client.getGenerativeModel({ model: model.name });
            const result = yield __await(geminiModel.generateContentStream({
                contents: request.messages.map(msg => ({ role: msg.role, parts: [{ text: msg.content }] })),
                generationConfig: {
                    temperature: request.temperature || 0.7,
                    maxOutputTokens: request.maxTokens || 1000,
                },
            }));
            try {
                for (var _d = true, _e = __asyncValues(result.stream), _f; _f = yield __await(_e.next()), _a = _f.done, !_a; _d = true) {
                    _c = _f.value;
                    _d = false;
                    const chunk = _c;
                    const text = chunk.text();
                    if (text) {
                        yield yield __await(text);
                    }
                }
            }
            catch (e_3_1) { e_3 = { error: e_3_1 }; }
            finally {
                try {
                    if (!_d && !_a && (_b = _e.return)) yield __await(_b.call(_e));
                }
                finally { if (e_3) throw e_3.error; }
            }
        });
    }
    streamMistral(provider, model, request) {
        return __asyncGenerator(this, arguments, function* streamMistral_1() {
            var _a, e_4, _b, _c;
            var _d, _e;
            const client = this.getOrCreateClient(provider.id, () => new mistralai_1.Mistral(provider.config.apiKey));
            const stream = yield __await(client.chat({
                model: model.name,
                messages: request.messages,
                temperature: request.temperature || 0.7,
                maxTokens: request.maxTokens || 1000,
                stream: true,
            }));
            try {
                for (var _f = true, stream_3 = __asyncValues(stream), stream_3_1; stream_3_1 = yield __await(stream_3.next()), _a = stream_3_1.done, !_a; _f = true) {
                    _c = stream_3_1.value;
                    _f = false;
                    const chunk = _c;
                    if ((_e = (_d = chunk.choices[0]) === null || _d === void 0 ? void 0 : _d.delta) === null || _e === void 0 ? void 0 : _e.content) {
                        yield yield __await(chunk.choices[0].delta.content);
                    }
                }
            }
            catch (e_4_1) { e_4 = { error: e_4_1 }; }
            finally {
                try {
                    if (!_f && !_a && (_b = stream_3.return)) yield __await(_b.call(stream_3));
                }
                finally { if (e_4) throw e_4.error; }
            }
        });
    }
    streamGroq(provider, model, request) {
        return __asyncGenerator(this, arguments, function* streamGroq_1() {
            var _a, e_5, _b, _c;
            var _d, _e;
            const client = this.getOrCreateClient(provider.id, () => new groq_sdk_1.Groq({
                apiKey: provider.config.apiKey,
            }));
            const stream = yield __await(client.chat.completions.create({
                model: model.name,
                messages: request.messages,
                temperature: request.temperature || 0.7,
                max_tokens: request.maxTokens || 1000,
                stream: true,
            }));
            try {
                for (var _f = true, stream_4 = __asyncValues(stream), stream_4_1; stream_4_1 = yield __await(stream_4.next()), _a = stream_4_1.done, !_a; _f = true) {
                    _c = stream_4_1.value;
                    _f = false;
                    const chunk = _c;
                    const content = (_e = (_d = chunk.choices[0]) === null || _d === void 0 ? void 0 : _d.delta) === null || _e === void 0 ? void 0 : _e.content;
                    if (content) {
                        yield yield __await(content);
                    }
                }
            }
            catch (e_5_1) { e_5 = { error: e_5_1 }; }
            finally {
                try {
                    if (!_f && !_a && (_b = stream_4.return)) yield __await(_b.call(stream_4));
                }
                finally { if (e_5) throw e_5.error; }
            }
        });
    }
    getOrCreateClient(providerId, clientFactory) {
        if (!this.providerClients.has(providerId)) {
            this.providerClients.set(providerId, clientFactory());
        }
        return this.providerClients.get(providerId);
    }
    async testProvider(providerId, organizationId) {
        var _a, e_6, _b, _c;
        const startTime = Date.now();
        try {
            const testRequest = {
                requestId: `test_${Date.now()}`,
                providerId,
                messages: [{ role: 'user', content: 'Hello, this is a test message.' }],
                maxTokens: 50,
                organizationId,
            };
            const stream = await this.streamRequest(testRequest);
            let content = '';
            try {
                for (var _d = true, stream_5 = __asyncValues(stream), stream_5_1; stream_5_1 = await stream_5.next(), _a = stream_5_1.done, !_a; _d = true) {
                    _c = stream_5_1.value;
                    _d = false;
                    const chunk = _c;
                    content += chunk;
                }
            }
            catch (e_6_1) { e_6 = { error: e_6_1 }; }
            finally {
                try {
                    if (!_d && !_a && (_b = stream_5.return)) await _b.call(stream_5);
                }
                finally { if (e_6) throw e_6.error; }
            }
            return {
                success: true,
                latency: Date.now() - startTime,
            };
        }
        catch (error) {
            return {
                success: false,
                latency: Date.now() - startTime,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
};
exports.AIProviderStreamService = AIProviderStreamService;
exports.AIProviderStreamService = AIProviderStreamService = AIProviderStreamService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [ai_provider_manager_service_1.AIProviderManagerService,
        ai_provider_selector_service_1.AIProviderSelectorService])
], AIProviderStreamService);
//# sourceMappingURL=ai-provider-stream.service.js.map