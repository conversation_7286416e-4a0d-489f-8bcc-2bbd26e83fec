"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ToolRuntimeService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolRuntimeService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const prisma_service_1 = require("../../prisma/prisma.service");
const logger_service_1 = require("../../common/services/logger.service");
const axios_1 = require("axios");
let ToolRuntimeService = ToolRuntimeService_1 = class ToolRuntimeService {
    constructor(prisma, configService, loggerService) {
        this.prisma = prisma;
        this.configService = configService;
        this.loggerService = loggerService;
        this.logger = new common_1.Logger(ToolRuntimeService_1.name);
    }
    async executeTool(context) {
        const startTime = Date.now();
        try {
            const tool = await this.loadTool(context.toolId);
            if (!tool) {
                throw new Error(`Tool ${context.toolId} not found`);
            }
            this.validateParameters(tool, context.parameters);
            const result = await this.executeByType(tool, context);
            const duration = Date.now() - startTime;
            await this.logExecution(context, result, duration);
            return Object.assign(Object.assign({}, result), { duration });
        }
        catch (error) {
            const duration = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.loggerService.error(`Tool execution failed: ${errorMessage}`, {
                toolId: context.toolId,
                agentId: context.agentId,
                error: errorMessage,
                duration,
            });
            return {
                success: false,
                output: null,
                duration,
                error: errorMessage,
            };
        }
    }
    async loadTool(toolId) {
        const tool = await this.prisma.tool.findUnique({
            where: { id: toolId },
        });
        if (!tool)
            return null;
        return {
            id: tool.id,
            name: tool.name,
            type: tool.type,
            config: tool.config,
            schema: tool.schema,
        };
    }
    validateParameters(tool, parameters) {
        var _a, _b;
        const requiredParams = ((_a = tool.config.parameters) === null || _a === void 0 ? void 0 : _a.filter(p => p.required)) || [];
        for (const param of requiredParams) {
            if (!(param.name in parameters)) {
                throw new Error(`Missing required parameter: ${param.name}`);
            }
        }
        for (const [key, value] of Object.entries(parameters)) {
            const paramDef = (_b = tool.config.parameters) === null || _b === void 0 ? void 0 : _b.find(p => p.name === key);
            if (paramDef) {
                this.validateParameterType(key, value, paramDef.type);
            }
        }
    }
    validateParameterType(name, value, expectedType) {
        switch (expectedType) {
            case 'string':
                if (typeof value !== 'string') {
                    throw new Error(`Parameter ${name} must be a string`);
                }
                break;
            case 'number':
                if (typeof value !== 'number') {
                    throw new Error(`Parameter ${name} must be a number`);
                }
                break;
            case 'boolean':
                if (typeof value !== 'boolean') {
                    throw new Error(`Parameter ${name} must be a boolean`);
                }
                break;
            case 'array':
                if (!Array.isArray(value)) {
                    throw new Error(`Parameter ${name} must be an array`);
                }
                break;
            case 'object':
                if (typeof value !== 'object' || value === null) {
                    throw new Error(`Parameter ${name} must be an object`);
                }
                break;
        }
    }
    async executeByType(tool, context) {
        switch (tool.type) {
            case 'API':
                return await this.executeApiTool(tool, context);
            case 'WEBHOOK':
                return await this.executeWebhookTool(tool, context);
            case 'FUNCTION':
                return await this.executeFunctionTool(tool, context);
            case 'DATABASE':
                return await this.executeDatabaseTool(tool, context);
            default:
                throw new Error(`Unsupported tool type: ${tool.type}`);
        }
    }
    async executeApiTool(tool, context) {
        const { config } = tool;
        const timeout = context.timeout || config.timeout || 30000;
        try {
            const headers = Object.assign({}, config.headers);
            if (config.authentication) {
                switch (config.authentication.type) {
                    case 'bearer':
                        headers['Authorization'] = `Bearer ${config.authentication.credentials.token}`;
                        break;
                    case 'api_key':
                        headers[config.authentication.credentials.header || 'X-API-Key'] = config.authentication.credentials.key;
                        break;
                    case 'basic':
                        const auth = Buffer.from(`${config.authentication.credentials.username}:${config.authentication.credentials.password}`).toString('base64');
                        headers['Authorization'] = `Basic ${auth}`;
                        break;
                }
            }
            const response = await (0, axios_1.default)({
                method: config.method || 'POST',
                url: config.endpoint,
                data: context.parameters,
                headers,
                timeout,
            });
            return {
                success: true,
                output: response.data,
                metadata: {
                    status: response.status,
                    headers: response.headers,
                },
            };
        }
        catch (error) {
            throw new Error(`API call failed: ${error.message}`);
        }
    }
    async executeWebhookTool(tool, context) {
        return await this.executeApiTool(tool, context);
    }
    async executeFunctionTool(tool, context) {
        const functionName = tool.config.endpoint;
        try {
            const result = await this.executeCustomFunction(functionName, context.parameters);
            return {
                success: true,
                output: result,
            };
        }
        catch (error) {
            throw new Error(`Function execution failed: ${error.message}`);
        }
    }
    async executeDatabaseTool(tool, context) {
        const query = tool.config.endpoint;
        try {
            const result = await this.executeDatabaseQuery(query, context.parameters);
            return {
                success: true,
                output: result,
            };
        }
        catch (error) {
            throw new Error(`Database query failed: ${error.message}`);
        }
    }
    async executeCustomFunction(functionName, parameters) {
        const functions = {
            'math.add': (params) => params.a + params.b,
            'math.multiply': (params) => params.a * params.b,
            'string.uppercase': (params) => params.text.toUpperCase(),
            'string.lowercase': (params) => params.text.toLowerCase(),
            'date.now': () => new Date().toISOString(),
            'uuid.generate': () => require('crypto').randomUUID(),
        };
        const func = functions[functionName];
        if (!func) {
            throw new Error(`Function ${functionName} not found`);
        }
        return func(parameters);
    }
    async executeDatabaseQuery(query, parameters) {
        return {
            rows: [
                { id: 1, name: 'Sample Data', value: parameters.value || 'default' }
            ],
            count: 1,
        };
    }
    async logExecution(context, result, duration) {
        try {
            await this.prisma.toolExecution.create({
                data: {
                    toolId: context.toolId,
                    agentId: context.agentId,
                    sessionId: context.sessionId,
                    parameters: context.parameters,
                    output: result.output,
                    success: result.success,
                    duration,
                    errorMessage: result.error,
                    metadata: result.metadata || {},
                },
            });
            this.loggerService.business('tool_execution', {
                toolId: context.toolId,
                agentId: context.agentId,
                success: result.success,
                duration,
            });
        }
        catch (error) {
            this.logger.warn(`Failed to log tool execution: ${error}`);
        }
    }
    async getAvailableTools(agentId) {
        const tools = await this.prisma.tool.findMany({
            where: {
                OR: [
                    { isPublic: true },
                    {
                        agentTools: {
                            some: { agentId }
                        }
                    }
                ]
            },
        });
        return tools.map(tool => ({
            id: tool.id,
            name: tool.name,
            type: tool.type,
            config: tool.config,
            schema: tool.schema,
        }));
    }
};
exports.ToolRuntimeService = ToolRuntimeService;
exports.ToolRuntimeService = ToolRuntimeService = ToolRuntimeService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        config_1.ConfigService,
        logger_service_1.LoggerService])
], ToolRuntimeService);
//# sourceMappingURL=tool-runtime.service.js.map