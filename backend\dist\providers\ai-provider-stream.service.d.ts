import { AIProviderManagerService } from './ai-provider-manager.service';
import { AIProviderSelectorService } from './ai-provider-selector.service';
import { AIRequest } from './ai-provider-integration.service';
export declare class AIProviderStreamService {
    private readonly aiProviderManager;
    private readonly aiProviderSelector;
    private readonly logger;
    private readonly providerClients;
    constructor(aiProviderManager: AIProviderManagerService, aiProviderSelector: AIProviderSelectorService);
    streamRequest(request: AIRequest): Promise<AsyncIterable<string>>;
    private streamOpenAI;
    private streamClaude;
    private streamGemini;
    private streamMistral;
    private streamGroq;
    private getOrCreateClient;
    testProvider(providerId: string, organizationId: string): Promise<{
        success: boolean;
        latency: number;
        error?: string;
    }>;
}
