import { IsString, IsEnum, IsBoolean, IsOptional, IsObject, IsArray, ValidateNested, IsNumber, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { ProviderType } from '../../database/entities/ai-provider.entity';

export class ProviderConfigDto {
  @IsString()
  apiKey!: string;

  @IsOptional()
  @IsString()
  baseUrl?: string;

  @IsOptional()
  @IsNumber()
  @Min(1000)
  @Max(120000)
  timeout?: number;
}

export class ModelCapabilitiesDto    {
  @IsBoolean()
  chat!: boolean;

  @IsBoolean()
  completion!: boolean;

  @IsBoolean()
  embedding!: boolean;

  @IsBoolean()
  vision!: boolean;

  @IsBoolean()
  functionCalling!: boolean;

  @IsBoolean()
  codeGeneration!: boolean;

  @IsBoolean()
  analysis!: boolean;

  @IsBoolean()
  multimodal!: boolean;

  @IsBoolean()
  streaming!: boolean;
}

export class ModelDto {
  @IsString()
  name!: string;

  @IsOptional()
  @IsString()
  description?: string = '';

  @IsString()
  version!: string;

  @ValidateNested()
  @Type(() => ModelCapabilitiesDto)
  capabilities!: ModelCapabilitiesDto;

  @IsOptional()
  @IsBoolean()
  fineTuned?: boolean = false ;

  @IsNumber()
  @Min(1)
  maxTokens!: number;

  @IsNumber()
  @Min(0)
  costPer1KInput!: number;

  @IsNumber()
  @Min(0)
  costPer1KOutput!: number;
}

export class CreateProviderDto {
  @IsString()
  name!: string;

  @IsEnum(ProviderType)
  type!: ProviderType;

  @ValidateNested()
  @Type(() => ProviderConfigDto)
  config!: ProviderConfigDto;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean = true;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ModelDto)
  models?: ModelDto[];
}

export class UpdateProviderDto {
  @IsOptional()
  @IsString()
  name?: string = '';

  @IsOptional()
  @IsEnum(ProviderType)
  type!: ProviderType;

  @IsOptional()
  @ValidateNested()
  @Type(() => ProviderConfigDto)
  config?: ProviderConfigDto;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean = true;
}

export class TestProviderDto {
  @IsEnum(ProviderType)
  type!: ProviderType;

  @IsString()
  apiKey!: string;

  @IsOptional()
  @IsString()
  baseUrl?: string = '';

  @IsOptional()
  @IsNumber()
  @Min(1000)
  @Max(120000)
  timeout?: number = 30000;
}

export class ProviderRequestDto {
  @IsOptional()
  @IsArray()
  messages?: any[] = [];

  @IsOptional()
  @IsString()
  prompt?: string = '';

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(4000)
  maxTokens?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature?: number;

  @IsOptional()
  @IsBoolean()
  stream?: boolean;
}

export class ProviderSelectionDto {
  @IsArray()
  @IsString({ each: true })
  capabilities!: string[];

  @IsOptional()
  @IsNumber()
  @Min(0)
  maxLatency?: number = 0;

  @IsOptional()
  @IsNumber()
  @Min(0)
  maxCost?: number = 0;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  preferredProviders?: string[] = [];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  excludeProviders?: string[] = [];
}

export class ProviderAnalyticsDto {
  @IsOptional()
  @IsString()
  timeRange?: string = '7d';
}

export class ProviderUsageDto {
  @IsOptional()
  @IsString()
  timeRange?: string = '30d';
}