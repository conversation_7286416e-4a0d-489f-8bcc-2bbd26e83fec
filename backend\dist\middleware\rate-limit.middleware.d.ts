import { NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { CustomLoggerService } from '../logging/logger.service';
interface RequestWithUser extends Request {
    user?: {
        userId: string;
        organizationId: string;
    };
}
export declare class RateLimitMiddleware implements NestMiddleware {
    private readonly logger;
    private readonly rateLimitStore;
    constructor(logger: CustomLoggerService);
    use(req: RequestWithUser, res: Response, next: NextFunction): void | Response<any, Record<string, any>>;
    private getClientId;
    private getRateLimit;
}
export {};
