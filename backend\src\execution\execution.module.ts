import { Modu<PERSON> } from '@nestjs/common';
import { ExecutionController } from './execution.controller';
import { AgentExecutionModule } from '../agents/execution/execution.module';
import { ToolExecutionModule } from '../tools/execution/execution.module';
import { WorkflowExecutionModule } from '../workflows/execution/execution.module';
import { AIProviderModule } from '../providers/ai-provider.module';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [
    CommonModule,
    AgentExecutionModule,
    ToolExecutionModule,
    WorkflowExecutionModule,
    AIProviderModule,
  ],
  controllers: [ExecutionController],
})
export class ExecutionModule {}
