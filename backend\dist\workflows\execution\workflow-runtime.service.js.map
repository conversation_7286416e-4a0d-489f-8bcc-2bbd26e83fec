{"version": 3, "file": "workflow-runtime.service.js", "sourceRoot": "", "sources": ["../../../src/workflows/execution/workflow-runtime.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,gEAA4D;AAC5D,yEAAqE;AACrE,wFAAmF;AACnF,qFAAgF;AAyDzE,IAAM,sBAAsB,8BAA5B,MAAM,sBAAsB;IAGjC,YACU,MAAqB,EACrB,aAA4B,EAC5B,aAA4B,EAC5B,YAAiC,EACjC,WAA+B;QAJ/B,WAAM,GAAN,MAAM,CAAe;QACrB,kBAAa,GAAb,aAAa,CAAe;QAC5B,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAqB;QACjC,gBAAW,GAAX,WAAW,CAAoB;QAPxB,WAAM,GAAG,IAAI,eAAM,CAAC,wBAAsB,CAAC,IAAI,CAAC,CAAC;IAQ/D,CAAC;IAEJ,KAAK,CAAC,eAAe,CAAC,OAAiC;QACrD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,KAAK,GAAqC,EAAE,CAAC;QAEnD,IAAI,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC7D,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,YAAY,OAAO,CAAC,UAAU,YAAY,CAAC,CAAC;YAC9D,CAAC;YAGD,MAAM,gBAAgB,mCACjB,OAAO,KACV,SAAS,gDACJ,QAAQ,CAAC,SAAS,GAClB,OAAO,CAAC,SAAS,GACjB,OAAO,CAAC,KAAK,IAEnB,CAAC;YAGF,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAG5D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;YAGlF,MAAM,IAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,EAAE,EAAE;gBAC7C,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ;gBAC/C,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,KAAK;gBACL,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBAChC,KAAK,EAAE,MAAM,CAAC,KAAK;aACpB,CAAC,CAAC;YAEH,uCACK,MAAM,KACT,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EAChC,KAAK,IACL;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAE9E,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,8BAA8B,YAAY,EAAE,EAAE;gBACrE,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,KAAK,EAAE,YAAY;gBACnB,QAAQ;aACT,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,QAAQ;gBACR,KAAK;gBACL,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,UAAkB;QAC3C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YACrD,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;SAC1B,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ;YAAE,OAAO,IAAI,CAAC;QAE3B,OAAO;YACL,EAAE,EAAE,QAAQ,CAAC,EAAE;YACf,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,KAAK,EAAE,QAAQ,CAAC,KAAuB;YACvC,KAAK,EAAE,QAAQ,CAAC,KAAuB;YACvC,SAAS,EAAE,QAAQ,CAAC,SAAgC;YACpD,QAAQ,EAAE,QAAQ,CAAC,QAAe;SACnC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAChC,QAA4B,EAC5B,OAAiC,EACjC,KAAuC;QAIvC,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAGD,MAAM,cAAc,GAAG;YACrB,SAAS,oBAAO,OAAO,CAAC,SAAS,CAAE;YACnC,cAAc,EAAE,IAAI,GAAG,EAAU;YACjC,WAAW,EAAE,IAAI,GAAG,EAAe;SACpC,CAAC;QAEF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;QAE5F,OAAO;YACL,OAAO;YACP,MAAM,EAAE,cAAc,CAAC,SAAS;YAChC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,2BAA2B;SACzD,CAAC;IACJ,CAAC;IAEO,aAAa,CAAC,QAA4B;QAChD,MAAM,sBAAsB,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAChF,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC;IACnF,CAAC;IAEO,KAAK,CAAC,WAAW,CACvB,IAAkB,EAClB,QAA4B,EAC5B,OAAiC,EACjC,KAAU,EACV,KAAuC;QAGvC,IAAI,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEjC,IAAI,CAAC;YACH,IAAI,UAAe,CAAC;YACpB,IAAI,OAAO,GAAG,IAAI,CAAC;YAGnB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;gBAClB,KAAK,OAAO;oBACV,UAAU,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;oBAC/D,MAAM;gBACR,KAAK,MAAM;oBACT,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;oBAC9D,MAAM;gBACR,KAAK,WAAW;oBACd,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;oBACnE,MAAM;gBACR,KAAK,UAAU;oBACb,UAAU,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;oBACnF,MAAM;gBACR,KAAK,OAAO;oBACV,UAAU,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;oBAC/D,MAAM;gBACR;oBACE,MAAM,IAAI,KAAK,CAAC,0BAA0B,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAC3D,CAAC;YAGD,KAAK,CAAC,IAAI,CAAC;gBACT,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,OAAO;gBACP,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,aAAa;aACrC,CAAC,CAAC;YAGH,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAClC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;YAG3C,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;gBAC1D,KAAK,CAAC,SAAS,mCAAQ,KAAK,CAAC,SAAS,GAAK,UAAU,CAAE,CAAC;YAC1D,CAAC;YAGD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC9D,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;gBACtF,IAAI,CAAC,WAAW,EAAE,CAAC;oBACjB,OAAO,GAAG,KAAK,CAAC;gBAClB,CAAC;YACH,CAAC;YAED,OAAO,OAAO,CAAC;QAEjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAE9E,KAAK,CAAC,IAAI,CAAC;gBACT,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,IAAI;gBACZ,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,aAAa;gBACpC,KAAK,EAAE,YAAY;aACpB,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,IAAkB,EAAE,OAAiC,EAAE,KAAU;QAC9F,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,WAAW,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;QAEvF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC;YAClD,OAAO;YACP,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,KAAK;YACL,SAAS,EAAE,KAAK,CAAC,SAAS;SAC3B,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,IAAI,wBAAwB,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC;IACrE,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,IAAkB,EAAE,OAAiC,EAAE,KAAU;QAC7F,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAClC,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;QAExF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC;YAChD,MAAM;YACN,OAAO,EAAE,OAAO,CAAC,UAAU;YAC3B,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,UAAU;SACX,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,IAAI,uBAAuB,CAAC,CAAC;QAC3D,CAAC;QAED,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC;IACrE,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,IAAkB,EAAE,OAAiC,EAAE,KAAU;QAClG,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;QACxC,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;QAElE,OAAO,EAAE,gBAAgB,EAAE,MAAM,EAAE,CAAC;IACtC,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAC/B,IAAkB,EAClB,QAA4B,EAC5B,OAAiC,EACjC,KAAU,EACV,KAAuC;QAGvC,MAAM,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;QACpD,MAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,KAAK,EAAE,QAAgB,EAAE,EAAE;YAC/D,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC;YAC/D,IAAI,UAAU,EAAE,CAAC;gBACf,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAC7E,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC5C,OAAO,EAAE,gBAAgB,EAAE,OAAO,EAAE,CAAC;IACvC,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,IAAkB,EAAE,OAAiC,EAAE,KAAU;QAC9F,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC;QACxC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QAEzD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAEO,YAAY,CAAC,MAAc,EAAE,QAA4B,EAAE,KAAU;QAC3E,MAAM,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;QAC5E,MAAM,SAAS,GAAmB,EAAE,CAAC;QAErC,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;YAEjC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;gBAChF,IAAI,CAAC,eAAe,EAAE,CAAC;oBACrB,SAAS;gBACX,CAAC;YACH,CAAC;YAED,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;YACtE,IAAI,QAAQ,EAAE,CAAC;gBACb,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,gBAAgB,CAAC,QAAa,EAAE,SAA8B;QACpE,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,OAAO,QAAQ,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;gBACvD,OAAO,SAAS,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAC/D,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;YACtD,MAAM,QAAQ,GAAQ,EAAE,CAAC;YACzB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACpD,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAC1D,CAAC;YACD,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,iBAAiB,CAAC,SAAiB,EAAE,SAA8B;QAEzE,IAAI,CAAC;YAEH,MAAM,iBAAiB,GAAG,SAAS,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;gBAC3E,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;gBAC7B,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;YAGH,OAAO,QAAQ,CAAC,yBAAyB,iBAAiB,GAAG,CAAC,EAAE,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;YACtE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,OAAiC;QACnE,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;YAChD,IAAI,EAAE;gBACJ,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,MAAM,EAAE,SAAS;gBACjB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,cAAc,EAAE,OAAO,CAAC,cAAc;aACvC;SACF,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,WAAmB,EAAE,IAAS;QAChE,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;YACzC,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE;YAC1B,IAAI;SACL,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AAjWY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;qCAKO,8BAAa;QACN,sBAAa;QACb,8BAAa;QACd,2CAAmB;QACpB,yCAAkB;GAR9B,sBAAsB,CAiWlC"}