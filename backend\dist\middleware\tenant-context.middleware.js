"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TenantContextMiddleware = void 0;
const common_1 = require("@nestjs/common");
const logger_service_1 = require("../logging/logger.service");
let TenantContextMiddleware = class TenantContextMiddleware {
    constructor(logger) {
        this.logger = logger;
    }
    use(req, res, next) {
        try {
            const tenantId = this.extractTenantId(req);
            if (tenantId) {
                req['tenantId'] = tenantId;
                this.logger.log(`Tenant context set: ${tenantId}`, 'TenantContextMiddleware');
            }
            next();
        }
        catch (error) {
            this.logger.error(`Tenant context middleware error: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : '', 'TenantContextMiddleware');
            next();
        }
    }
    extractTenantId(req) {
        const host = req.headers.host;
        if (host) {
            const subdomain = host.split('.')[0];
            if (subdomain && subdomain !== 'www' && subdomain !== 'api') {
                return subdomain;
            }
        }
        const tenantHeader = req.headers['x-tenant-id'];
        if (tenantHeader) {
            return tenantHeader;
        }
        const tenantQuery = req.query.tenant;
        if (tenantQuery) {
            return tenantQuery;
        }
        return null;
    }
};
exports.TenantContextMiddleware = TenantContextMiddleware;
exports.TenantContextMiddleware = TenantContextMiddleware = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [logger_service_1.CustomLoggerService])
], TenantContextMiddleware);
//# sourceMappingURL=tenant-context.middleware.js.map