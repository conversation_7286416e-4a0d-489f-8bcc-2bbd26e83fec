'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import {
  ChevronLeft,
  ChevronRight,
  Save,
  X,
  Plus,
  Trash2,
  Settings,
  Brain,
  MessageSquare,
  Zap,
  Target,
  Palette,
  Code
} from 'lucide-react';
import { AgentTemplate, CreateAgentTemplateDto, agent<PERSON><PERSON>er<PERSON><PERSON> } from '@/lib/agent-builder-api';

interface AgentTemplateWizardProps {
  template?: AgentTemplate | null;
  onSave: (template: AgentTemplate) => void;
  onCancel: () => void;
}

interface WizardStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

const WIZARD_STEPS: WizardStep[] = [
  {
    id: 'basic',
    title: 'Basic Information',
    description: 'Name, description, and category',
    icon: <Brain className="h-5 w-5" />
  },
  {
    id: 'personality',
    title: 'Personality & Behavior',
    description: 'Define agent personality and response style',
    icon: <MessageSquare className="h-5 w-5" />
  },
  {
    id: 'skills',
    title: 'Skills & Capabilities',
    description: 'Configure agent skills and tools',
    icon: <Zap className="h-5 w-5" />
  },
  {
    id: 'goals',
    title: 'Goals & Objectives',
    description: 'Set agent goals and success metrics',
    icon: <Target className="h-5 w-5" />
  },
  {
    id: 'appearance',
    title: 'Appearance & Branding',
    description: 'Customize visual appearance',
    icon: <Palette className="h-5 w-5" />
  },
  {
    id: 'advanced',
    title: 'Advanced Settings',
    description: 'Provider settings and configurations',
    icon: <Settings className="h-5 w-5" />
  }
];

const AGENT_CATEGORIES = [
  'Customer Support',
  'Sales Assistant',
  'Content Creator',
  'Data Analyst',
  'Personal Assistant',
  'Educational Tutor',
  'Technical Support',
  'Marketing Assistant',
  'Research Assistant',
  'Creative Writing',
  'Code Assistant',
  'Other'
];

const PERSONALITY_TRAITS = [
  { id: 'professional', label: 'Professional', description: 'Formal and business-oriented' },
  { id: 'friendly', label: 'Friendly', description: 'Warm and approachable' },
  { id: 'helpful', label: 'Helpful', description: 'Always eager to assist' },
  { id: 'creative', label: 'Creative', description: 'Innovative and imaginative' },
  { id: 'analytical', label: 'Analytical', description: 'Data-driven and logical' },
  { id: 'empathetic', label: 'Empathetic', description: 'Understanding and caring' },
  { id: 'enthusiastic', label: 'Enthusiastic', description: 'Energetic and positive' },
  { id: 'patient', label: 'Patient', description: 'Calm and understanding' }
];

const AVAILABLE_SKILLS = [
  { id: 'web_search', name: 'Web Search', description: 'Search the internet for information' },
  { id: 'email_send', name: 'Send Email', description: 'Send emails to users' },
  { id: 'calendar_manage', name: 'Calendar Management', description: 'Manage calendar events' },
  { id: 'file_process', name: 'File Processing', description: 'Process and analyze files' },
  { id: 'data_analysis', name: 'Data Analysis', description: 'Analyze data and generate insights' },
  { id: 'image_generate', name: 'Image Generation', description: 'Generate images from text' },
  { id: 'code_execute', name: 'Code Execution', description: 'Execute code snippets' },
  { id: 'api_call', name: 'API Calls', description: 'Make external API calls' }
];

export function AgentTemplateWizard({ template, onSave, onCancel }: AgentTemplateWizardProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setSaving] = useState(false);
  const [formData, setFormData] = useState<CreateAgentTemplateDto>({
    name: template?.name || '',
    category: template?.category || '',
    description: template?.description || '',
    config: template?.config || {
      personality: {
        traits: [],
        tone: 'professional',
        responseStyle: 'detailed'
      },
      skills: [],
      goals: [],
      appearance: {
        avatar: '',
        color: '#3B82F6',
        theme: 'modern'
      },
      provider: {
        preferred: 'openai',
        model: 'gpt-4',
        temperature: 0.7,
        maxTokens: 2000
      },
      memory: {
        enabled: true,
        contextWindow: 10,
        longTermMemory: false
      },
      safety: {
        contentFilter: true,
        rateLimiting: true,
        maxRequestsPerHour: 100
      }
    },
    skills: template?.skills || [],
    isPublic: template?.isPublic || false,
    tags: template?.tags || []
  });

  const { toast } = useToast();

  const updateFormData = (path: string, value: any) => {
    setFormData(prev => {
      const newData = { ...prev };
      const keys = path.split('.');
      let current: any = newData;

      for (let i = 0; i < keys.length - 1; i++) {
        if (!(keys[i] in current)) {
          current[keys[i]] = {};
        }
        current = current[keys[i]];
      }

      current[keys[keys.length - 1]] = value;
      return newData;
    });
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      let result;
      if (template) {
        result = await agentBuilderApi.updateAgentTemplate(template.id, formData);
      } else {
        result = await agentBuilderApi.createAgentTemplate(formData);
      }

      if (result.success) {
        toast({
          title: 'Success',
          description: `Agent template ${template ? 'updated' : 'created'} successfully.`,
        });
        onSave(result.data);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to ${template ? 'update' : 'create'} agent template. Please try again.`,
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  const renderBasicStep = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <div>
          <Label htmlFor="name">Agent Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => updateFormData('name', e.target.value)}
            placeholder="Enter agent name"
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="category">Category *</Label>
          <Select value={formData.category} onValueChange={(value) => updateFormData('category', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {AGENT_CATEGORIES.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="description">Description *</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => updateFormData('description', e.target.value)}
            placeholder="Describe what this agent does and how it helps users"
            className="mt-1"
            rows={4}
          />
        </div>

        <div>
          <Label htmlFor="tags">Tags</Label>
          <Input
            id="tags"
            value={formData.tags?.join(', ') || ''}
            onChange={(e) => updateFormData('tags', e.target.value.split(',').map(t => t.trim()).filter(Boolean))}
            placeholder="Enter tags separated by commas"
            className="mt-1"
          />
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="isPublic"
            checked={formData.isPublic}
            onCheckedChange={(checked) => updateFormData('isPublic', checked)}
          />
          <Label htmlFor="isPublic">Make this template public</Label>
        </div>
      </div>
    </div>
  );

  const renderPersonalityStep = () => (
    <div className="space-y-6">
      <div>
        <Label className="text-base font-medium">Personality Traits</Label>
        <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
          Select traits that define your agent's personality
        </p>
        <div className="grid grid-cols-2 gap-3">
          {PERSONALITY_TRAITS.map((trait) => (
            <Card
              key={trait.id}
              className={`cursor-pointer transition-all ${formData.config.personality?.traits?.includes(trait.id)
                ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20'
                : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                }`}
              onClick={() => {
                const currentTraits = formData.config.personality?.traits || [];
                const newTraits = currentTraits.includes(trait.id)
                  ? currentTraits.filter((t: string) => t !== trait.id)
                  : [...currentTraits, trait.id];
                updateFormData('config.personality.traits', newTraits);
              }}
            >
              <CardContent className="p-4">
                <h4 className="font-medium">{trait.label}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">{trait.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label>Communication Tone</Label>
          <Select
            value={formData.config.personality?.tone || 'professional'}
            onValueChange={(value) => updateFormData('config.personality.tone', value)}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="professional">Professional</SelectItem>
              <SelectItem value="casual">Casual</SelectItem>
              <SelectItem value="formal">Formal</SelectItem>
              <SelectItem value="friendly">Friendly</SelectItem>
              <SelectItem value="humorous">Humorous</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label>Response Style</Label>
          <Select
            value={formData.config.personality?.responseStyle || 'detailed'}
            onValueChange={(value) => updateFormData('config.personality.responseStyle', value)}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="concise">Concise</SelectItem>
              <SelectItem value="detailed">Detailed</SelectItem>
              <SelectItem value="conversational">Conversational</SelectItem>
              <SelectItem value="structured">Structured</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );

  const renderSkillsStep = () => (
    <div className="space-y-6">
      <div>
        <Label className="text-base font-medium">Available Skills</Label>
        <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
          Select skills that your agent can use to help users
        </p>
        <div className="grid grid-cols-1 gap-3">
          {AVAILABLE_SKILLS.map((skill) => (
            <Card
              key={skill.id}
              className={`cursor-pointer transition-all ${formData.skills?.includes(skill.id)
                ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20'
                : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                }`}
              onClick={() => {
                const currentSkills = formData.skills || [];
                const newSkills = currentSkills.includes(skill.id)
                  ? currentSkills.filter((s: string) => s !== skill.id)
                  : [...currentSkills, skill.id];
                updateFormData('skills', newSkills);
              }}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">{skill.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">{skill.description}</p>
                  </div>
                  {formData.skills?.includes(skill.id) && (
                    <Badge variant="default">Selected</Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );

  const renderGoalsStep = () => (
    <div className="space-y-6">
      <div>
        <Label className="text-base font-medium">Agent Goals</Label>
        <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
          Define what your agent should accomplish
        </p>
        <div className="space-y-3">
          {(formData.config.goals || []).map((goal: string, index: number) => (
            <div key={index} className="flex items-center gap-2">
              <Input
                value={goal}
                onChange={(e) => {
                  const newGoals = [...(formData.config.goals || [])];
                  newGoals[index] = e.target.value;
                  updateFormData('config.goals', newGoals);
                }}
                placeholder="Enter a goal for your agent"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const newGoals = (formData.config.goals || []).filter((_: string, i: number) => i !== index);
                  updateFormData('config.goals', newGoals);
                }}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          ))}
          <Button
            variant="outline"
            onClick={() => {
              const newGoals = [...(formData.config.goals || []), ''];
              updateFormData('config.goals', newGoals);
            }}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Goal
          </Button>
        </div>
      </div>
    </div>
  );

  const renderAppearanceStep = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label>Avatar URL</Label>
          <Input
            value={formData.config.appearance?.avatar || ''}
            onChange={(e) => updateFormData('config.appearance.avatar', e.target.value)}
            placeholder="https://example.com/avatar.png"
            className="mt-1"
          />
        </div>

        <div>
          <Label>Primary Color</Label>
          <Input
            type="color"
            value={formData.config.appearance?.color || '#3B82F6'}
            onChange={(e) => updateFormData('config.appearance.color', e.target.value)}
            className="mt-1 h-10"
          />
        </div>
      </div>

      <div>
        <Label>Theme</Label>
        <Select
          value={formData.config.appearance?.theme || 'modern'}
          onValueChange={(value) => updateFormData('config.appearance.theme', value)}
        >
          <SelectTrigger className="mt-1">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="modern">Modern</SelectItem>
            <SelectItem value="classic">Classic</SelectItem>
            <SelectItem value="minimal">Minimal</SelectItem>
            <SelectItem value="playful">Playful</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );

  const renderAdvancedStep = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label>Preferred AI Provider</Label>
          <Select
            value={formData.config.provider?.preferred || 'openai'}
            onValueChange={(value) => updateFormData('config.provider.preferred', value)}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="openai">OpenAI</SelectItem>
              <SelectItem value="claude">Claude</SelectItem>
              <SelectItem value="gemini">Gemini</SelectItem>
              <SelectItem value="mistral">Mistral</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label>Model</Label>
          <Select
            value={formData.config.provider?.model || 'gpt-4'}
            onValueChange={(value) => updateFormData('config.provider.model', value)}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="gpt-4">GPT-4</SelectItem>
              <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
              <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
              <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label>Temperature ({formData.config.provider?.temperature || 0.7})</Label>
          <input
            type="range"
            min="0"
            max="2"
            step="0.1"
            value={formData.config.provider?.temperature || 0.7}
            onChange={(e) => updateFormData('config.provider.temperature', parseFloat(e.target.value))}
            className="mt-1 w-full"
          />
        </div>

        <div>
          <Label>Max Tokens</Label>
          <Input
            type="number"
            value={formData.config.provider?.maxTokens || 2000}
            onChange={(e) => updateFormData('config.provider.maxTokens', parseInt(e.target.value))}
            className="mt-1"
          />
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Switch
            checked={formData.config.memory?.enabled || true}
            onCheckedChange={(checked) => updateFormData('config.memory.enabled', checked)}
          />
          <Label>Enable Memory</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            checked={formData.config.safety?.contentFilter || true}
            onCheckedChange={(checked) => updateFormData('config.safety.contentFilter', checked)}
          />
          <Label>Content Filtering</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            checked={formData.config.safety?.rateLimiting || true}
            onCheckedChange={(checked) => updateFormData('config.safety.rateLimiting', checked)}
          />
          <Label>Rate Limiting</Label>
        </div>
      </div>
    </div>
  );

  const renderStepContent = () => {
    switch (WIZARD_STEPS[currentStep].id) {
      case 'basic':
        return renderBasicStep();
      case 'personality':
        return renderPersonalityStep();
      case 'skills':
        return renderSkillsStep();
      case 'goals':
        return renderGoalsStep();
      case 'appearance':
        return renderAppearanceStep();
      case 'advanced':
        return renderAdvancedStep();
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Progress Steps */}
      <div className="flex items-center justify-between">
        {WIZARD_STEPS.map((step, index) => (
          <div
            key={step.id}
            className={`flex items-center ${index < WIZARD_STEPS.length - 1 ? 'flex-1' : ''}`}
          >
            <div
              className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${index <= currentStep
                ? 'bg-blue-600 border-blue-600 text-white'
                : 'border-gray-300 text-gray-400'
                }`}
            >
              {index < currentStep ? (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              ) : (
                step.icon
              )}
            </div>
            {index < WIZARD_STEPS.length - 1 && (
              <div className={`flex-1 h-0.5 mx-4 ${index < currentStep ? 'bg-blue-600' : 'bg-gray-300'
                }`} />
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {WIZARD_STEPS[currentStep].icon}
            {WIZARD_STEPS[currentStep].title}
          </CardTitle>
          <CardDescription>
            {WIZARD_STEPS[currentStep].description}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {renderStepContent()}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
          disabled={currentStep === 0}
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>

        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={onCancel}>
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>

          {currentStep === WIZARD_STEPS.length - 1 ? (
            <Button onClick={handleSave} disabled={loading}>
              <Save className="h-4 w-4 mr-2" />
              {loading ? 'Saving...' : template ? 'Update Template' : 'Create Template'}
            </Button>
          ) : (
            <Button
              onClick={() => setCurrentStep(Math.min(WIZARD_STEPS.length - 1, currentStep + 1))}
            >
              Next
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}