import { Repository } from 'typeorm';
import { AgentExecution } from '../database/entities/agent-execution.entity';
import { Agent } from '../database/entities/agent.entity';
export interface AgentAnalytics {
    agentId: string;
    totalExecutions: number;
    successRate: number;
    averageResponseTime: number;
    totalCost: number;
    providerDistribution: Record<string, number>;
    errorRate: number;
    lastExecuted: Date;
    performanceTrend: {
        period: string;
        executions: number;
        successRate: number;
        averageTime: number;
        cost: number;
    }[];
}
export declare class AgentAnalyticsService {
    private executionRepository;
    private agentRepository;
    private readonly logger;
    constructor(executionRepository: Repository<AgentExecution>, agentRepository: Repository<Agent>);
    trackExecution(execution: AgentExecution): Promise<void>;
    getAgentAnalytics(agent: Agent, organizationId: string): Promise<AgentAnalytics>;
    getOrganizationAnalytics(organizationId: string): Promise<{
        totalAgents: number;
        totalExecutions: number;
        totalCost: number;
        averageSuccessRate: number;
        topPerformingAgents: Array<{
            agentId: string;
            name: string;
            executions: number;
            successRate: number;
        }>;
        providerUsage: Record<string, number>;
        dailyExecutions: Array<{
            date: string;
            executions: number;
            cost: number;
        }>;
    }>;
    private generatePerformanceTrend;
    private getDailyExecutions;
    getProviderPerformance(organizationId: string): Promise<Array<{
        provider: string;
        executions: number;
        successRate: number;
        averageResponseTime: number;
        totalCost: number;
        reliability: number;
    }>>;
}
