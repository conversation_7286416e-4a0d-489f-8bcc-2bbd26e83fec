import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { WsException } from '@nestjs/websockets';
import { Socket } from 'socket.io';

@Injectable()
export class ApiXEventGuard implements CanActivate {
    canActivate(context: ExecutionContext): boolean {
        const client: Socket = context.switchToWs().getClient();
        const token = client.handshake.auth?.token;


        if (!token) {
            throw new WsException('Authentication token required');
        }

        return true;
    }
} 