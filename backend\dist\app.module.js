"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const jwt_1 = require("@nestjs/jwt");
const passport_1 = require("@nestjs/passport");
const event_emitter_1 = require("@nestjs/event-emitter");
const database_module_1 = require("./database/database.module");
const auth_module_1 = require("./auth/auth.module");
const users_module_1 = require("./users/users.module");
const health_module_1 = require("./health/health.module");
const workflows_module_1 = require("./workflows/workflows.module");
const redis_service_1 = require("./cache/redis.service");
const logger_service_1 = require("./logging/logger.service");
const rate_limit_middleware_1 = require("./middleware/rate-limit.middleware");
const tenant_context_middleware_1 = require("./middleware/tenant-context.middleware");
const configuration_1 = require("./config/configuration");
let AppModule = class AppModule {
    configure(consumer) {
        consumer
            .apply(rate_limit_middleware_1.RateLimitMiddleware)
            .forRoutes('*')
            .apply(tenant_context_middleware_1.TenantContextMiddleware)
            .forRoutes({ path: 'auth/*', method: common_1.RequestMethod.ALL }, { path: 'users/*', method: common_1.RequestMethod.ALL }, { path: 'api/*', method: common_1.RequestMethod.ALL });
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                load: [configuration_1.default],
                envFilePath: ['.env.local', '.env'],
            }),
            database_module_1.DatabaseModule,
            event_emitter_1.EventEmitterModule.forRoot({
                wildcard: true,
                delimiter: '.',
                maxListeners: 20,
                verboseMemoryLeak: true,
                ignoreErrors: false,
            }),
            passport_1.PassportModule.register({ defaultStrategy: 'jwt' }),
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => ({
                    secret: configService.get('app.jwt.secret'),
                    signOptions: {
                        expiresIn: configService.get('app.jwt.expiresIn'),
                        issuer: configService.get('app.jwt.issuer'),
                        audience: configService.get('app.jwt.audience'),
                    },
                }),
                inject: [config_1.ConfigService],
            }),
            auth_module_1.AuthModule,
            users_module_1.UserModule,
            health_module_1.HealthModule,
            workflows_module_1.WorkflowsModule,
        ],
        providers: [
            redis_service_1.RedisService,
            logger_service_1.CustomLoggerService,
        ],
        exports: [
            redis_service_1.RedisService,
            logger_service_1.CustomLoggerService,
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map