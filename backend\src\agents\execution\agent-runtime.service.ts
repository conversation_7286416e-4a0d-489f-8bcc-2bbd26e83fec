import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { LoggerService } from '../../common/services/logger.service';

export interface AgentExecutionContext {
  agentId: string;
  sessionId?: string;
  input: string;
  variables?: Record<string, any>;
  tools?: string[];
  memory?: {
    shortTerm: any[];
    longTerm: any[];
    context: Record<string, any>;
  };
}

export interface AgentExecutionResult {
  success: boolean;
  output: string;
  tokens: {
    input: number;
    output: number;
    total: number;
  };
  cost: number;
  duration: number;
  provider: string;
  model: string;
  error?: string;
  metadata?: Record<string, any>;
}

export interface AgentMemory {
  shortTerm: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
  }>;
  longTerm: Array<{
    summary: string;
    importance: number;
    timestamp: Date;
  }>;
  context: Record<string, any>;
}

@Injectable()
export class AgentRuntimeService {
  private readonly logger = new Logger(AgentRuntimeService.name);

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private loggerService: LoggerService,
  ) {}

  async executeAgent(context: AgentExecutionContext): Promise<AgentExecutionResult> {
    const startTime = Date.now();
    
    try {
      // 1. Load agent configuration
      const agent = await this.loadAgent(context.agentId);
      if (!agent) {
        throw new Error(`Agent ${context.agentId} not found`);
      }

      // 2. Prepare execution context
      const executionContext = await this.prepareExecutionContext(agent, context);

      // 3. Load and apply memory
      const memory = await this.loadAgentMemory(context.agentId, context.sessionId);
      
      // 4. Execute the agent logic
      const result = await this.runAgentLogic(agent, executionContext, memory);

      // 5. Update memory
      await this.updateAgentMemory(context.agentId, context.sessionId, {
        input: context.input,
        output: result.output,
        context: executionContext.variables || {},
      });

      // 6. Log execution
      const duration = Date.now() - startTime;
      await this.logExecution(context.agentId, context, result, duration);

      return {
        ...result,
        duration,
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      this.loggerService.error(`Agent execution failed: ${errorMessage}`, {
        agentId: context.agentId,
        sessionId: context.sessionId,
        error: errorMessage,
        duration,
      });

      return {
        success: false,
        output: `Error: ${errorMessage}`,
        tokens: { input: 0, output: 0, total: 0 },
        cost: 0,
        duration,
        provider: 'unknown',
        model: 'unknown',
        error: errorMessage,
      };
    }
  }

  private async loadAgent(agentId: string) {
    return await this.prisma.agent.findUnique({
      where: { id: agentId },
      include: {
        template: true,
      },
    });
  }

  private async prepareExecutionContext(agent: any, context: AgentExecutionContext) {
    // Merge agent config with execution context
    const baseConfig = agent.template?.config || agent.config || {};
    
    return {
      agentId: agent.id,
      name: agent.name,
      type: agent.type,
      provider: agent.primaryProvider,
      model: baseConfig.model || 'gpt-3.5-turbo',
      temperature: baseConfig.temperature || 0.7,
      maxTokens: baseConfig.maxTokens || 1000,
      promptTemplate: agent.template?.promptTemplate || agent.promptTemplate,
      variables: {
        ...baseConfig.variables,
        ...context.variables,
        input: context.input,
        agent_name: agent.name,
        timestamp: new Date().toISOString(),
      },
      tools: context.tools || [],
    };
  }

  private async loadAgentMemory(agentId: string, sessionId?: string): Promise<AgentMemory> {
    if (!sessionId) {
      return {
        shortTerm: [],
        longTerm: [],
        context: {},
      };
    }

    try {
      // Load from session memory service
      const session = await this.prisma.agentSession.findUnique({
        where: { id: sessionId },
        include: {
          messages: {
            orderBy: { createdAt: 'desc' },
            take: 10, // Last 10 messages for short-term memory
          },
        },
      });

      if (!session) {
        return {
          shortTerm: [],
          longTerm: [],
          context: {},
        };
      }

      return {
        shortTerm: session.messages.map(msg => ({
          role: msg.role as 'user' | 'assistant' | 'system',
          content: msg.content,
          timestamp: msg.createdAt,
        })),
        longTerm: session.longTermMemory as any[] || [],
        context: session.context as Record<string, any> || {},
      };

    } catch (error) {
      this.logger.warn(`Failed to load memory for agent ${agentId}: ${error}`);
      return {
        shortTerm: [],
        longTerm: [],
        context: {},
      };
    }
  }

  private async runAgentLogic(agent: any, context: any, memory: AgentMemory): Promise<Omit<AgentExecutionResult, 'duration'>> {
    // Build the prompt from template and context
    const prompt = this.buildPrompt(context.promptTemplate, context.variables, memory);
    
    // Determine which AI provider to use
    const provider = this.getProviderService(context.provider);
    
    // Execute the AI call
    const result = await provider.complete({
      model: context.model,
      prompt,
      temperature: context.temperature,
      maxTokens: context.maxTokens,
      tools: context.tools,
    });

    return {
      success: true,
      output: result.content,
      tokens: result.tokens,
      cost: result.cost,
      provider: context.provider,
      model: context.model,
      metadata: {
        promptLength: prompt.length,
        memoryItems: memory.shortTerm.length,
      },
    };
  }

  private buildPrompt(template: string, variables: Record<string, any>, memory: AgentMemory): string {
    let prompt = template;

    // Replace variables in template
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      prompt = prompt.replace(new RegExp(placeholder, 'g'), String(value));
    });

    // Add memory context if available
    if (memory.shortTerm.length > 0) {
      const memoryContext = memory.shortTerm
        .slice(-5) // Last 5 messages
        .map(msg => `${msg.role}: ${msg.content}`)
        .join('\n');
      
      prompt = `Previous conversation:\n${memoryContext}\n\n${prompt}`;
    }

    return prompt;
  }

  private getProviderService(provider: string) {
    // This would return the appropriate AI provider service
    // For now, return a mock implementation
    return {
      complete: async (params: any) => ({
        content: `Mock response for: ${params.prompt.substring(0, 100)}...`,
        tokens: { input: 100, output: 50, total: 150 },
        cost: 0.001,
      }),
    };
  }

  private async updateAgentMemory(agentId: string, sessionId: string | undefined, data: any) {
    if (!sessionId) return;

    try {
      await this.prisma.agentSession.update({
        where: { id: sessionId },
        data: {
          messages: {
            create: [
              {
                role: 'user',
                content: data.input,
                metadata: data.context,
              },
              {
                role: 'assistant', 
                content: data.output,
                metadata: { agentId },
              },
            ],
          },
          context: data.context,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      this.logger.warn(`Failed to update memory: ${error}`);
    }
  }

  private async logExecution(agentId: string, context: AgentExecutionContext, result: AgentExecutionResult, duration: number) {
    try {
      await this.prisma.agentExecution.create({
        data: {
          agentId,
          sessionId: context.sessionId,
          input: context.input,
          output: result.output,
          status: result.success ? 'COMPLETED' : 'FAILED',
          provider: result.provider,
          model: result.model,
          tokens: result.tokens,
          cost: result.cost,
          duration,
          metadata: result.metadata || {},
          errorMessage: result.error,
        },
      });

      this.loggerService.business('agent_execution', {
        agentId,
        sessionId: context.sessionId,
        success: result.success,
        duration,
        tokens: result.tokens.total,
        cost: result.cost,
      });

    } catch (error) {
      this.logger.warn(`Failed to log execution: ${error}`);
    }
  }
}
