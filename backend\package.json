{"name": "synapseai-backend", "version": "1.0.0", "description": "SynapseAI Universal AI Orchestration Backend", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@anthropic-ai/sdk": "^0.57.0", "@google/generative-ai": "^0.24.1", "@mistralai/mistralai": "^1.7.5", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.0.0", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^7.4.2", "@nestjs/typeorm": "^10.0.0", "@nestjs/websockets": "^10.0.0", "@prisma/client": "^6.13.0", "axios": "^1.11.0", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "groq-sdk": "^0.29.0", "helmet": "^8.1.0", "ioredis": "^5.7.0", "openai": "^5.11.0", "passport": "^0.6.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "pg": "^8.11.0", "prisma": "^6.13.0", "redis": "^4.6.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.7.0", "typeorm": "^0.3.17", "winston": "^3.17.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcryptjs": "^2.4.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/passport-google-oauth20": "^2.0.11", "@types/passport-jwt": "^3.0.9", "@types/supertest": "^2.0.12", "@types/winston": "^2.4.4", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}}