import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { LoggerService } from '../common/services/logger.service';
import { Cron, CronExpression } from '@nestjs/schedule';

export interface ProviderPerformanceReport {
  providerId: string;
  providerName: string;
  timeframe: string;
  metrics: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    successRate: number;
    averageLatency: number;
    p95Latency: number;
    p99Latency: number;
    totalTokens: number;
    totalCost: number;
    costPerToken: number;
    availability: number;
  };
  trends: {
    requestsChange: number;
    latencyChange: number;
    costChange: number;
    successRateChange: number;
  };
  recommendations: string[];
}

export interface OrganizationUsageReport {
  organizationId: string;
  timeframe: string;
  summary: {
    totalRequests: number;
    totalCost: number;
    totalTokens: number;
    averageLatency: number;
    topProvider: string;
    topModel: string;
  };
  providerBreakdown: Array<{
    provider: string;
    requests: number;
    cost: number;
    tokens: number;
    percentage: number;
  }>;
  costOptimization: {
    potentialSavings: number;
    recommendations: string[];
  };
}

@Injectable()
export class ProviderAnalyticsService {
  private readonly logger = new Logger(ProviderAnalyticsService.name);

  constructor(
    private prisma: PrismaService,
    private loggerService: LoggerService,
  ) {}

  async generateProviderReport(
    providerId: string,
    timeframe: 'hour' | 'day' | 'week' | 'month' = 'day'
  ): Promise<ProviderPerformanceReport> {
    const { since, previousSince } = this.getTimeframes(timeframe);

    // Current period metrics
    const currentMetrics = await this.calculateProviderMetrics(providerId, since);
    
    // Previous period metrics for trend analysis
    const previousMetrics = await this.calculateProviderMetrics(providerId, previousSince, since);

    // Get provider info
    const provider = await this.prisma.aIProvider.findUnique({
      where: { id: providerId },
      select: { name: true },
    });

    // Calculate trends
    const trends = this.calculateTrends(currentMetrics, previousMetrics);

    // Generate recommendations
    const recommendations = this.generateRecommendations(currentMetrics, trends);

    return {
      providerId,
      providerName: provider?.name || 'Unknown',
      timeframe,
      metrics: currentMetrics,
      trends,
      recommendations,
    };
  }

  async generateOrganizationReport(
    organizationId: string,
    timeframe: 'hour' | 'day' | 'week' | 'month' = 'day'
  ): Promise<OrganizationUsageReport> {
    const { since } = this.getTimeframes(timeframe);

    // Get all requests for the organization in the timeframe
    const requests = await this.prisma.aIRequest.findMany({
      where: {
        organizationId,
        createdAt: { gte: since },
        status: 'COMPLETED',
      },
      select: {
        provider: true,
        model: true,
        cost: true,
        tokens: true,
        latency: true,
      },
    });

    if (requests.length === 0) {
      return this.getEmptyReport(organizationId, timeframe);
    }

    // Calculate summary metrics
    const totalRequests = requests.length;
    const totalCost = requests.reduce((sum, req) => sum + req.cost, 0);
    const totalTokens = requests.reduce((sum, req) => {
      const tokens = req.tokens as any;
      return sum + (tokens?.totalTokens || 0);
    }, 0);
    const averageLatency = requests.reduce((sum, req) => sum + req.latency, 0) / totalRequests;

    // Find top provider and model
    const providerCounts = this.groupBy(requests, 'provider');
    const modelCounts = this.groupBy(requests, 'model');
    const topProvider = Object.keys(providerCounts).reduce((a, b) => 
      providerCounts[a].length > providerCounts[b].length ? a : b
    );
    const topModel = Object.keys(modelCounts).reduce((a, b) => 
      modelCounts[a].length > modelCounts[b].length ? a : b
    );

    // Provider breakdown
    const providerBreakdown = Object.entries(providerCounts).map(([provider, reqs]) => {
      const providerCost = reqs.reduce((sum, req) => sum + req.cost, 0);
      const providerTokens = reqs.reduce((sum, req) => {
        const tokens = req.tokens as any;
        return sum + (tokens?.totalTokens || 0);
      }, 0);

      return {
        provider,
        requests: reqs.length,
        cost: providerCost,
        tokens: providerTokens,
        percentage: (reqs.length / totalRequests) * 100,
      };
    }).sort((a, b) => b.requests - a.requests);

    // Cost optimization analysis
    const costOptimization = await this.analyzeCostOptimization(requests, organizationId);

    return {
      organizationId,
      timeframe,
      summary: {
        totalRequests,
        totalCost,
        totalTokens,
        averageLatency,
        topProvider,
        topModel,
      },
      providerBreakdown,
      costOptimization,
    };
  }

  private async calculateProviderMetrics(providerId: string, since: Date, until?: Date) {
    const whereClause: any = {
      provider: providerId,
      createdAt: { gte: since },
    };

    if (until) {
      whereClause.createdAt.lt = until;
    }

    const requests = await this.prisma.aIRequest.findMany({
      where: whereClause,
      select: {
        status: true,
        latency: true,
        cost: true,
        tokens: true,
      },
    });

    if (requests.length === 0) {
      return this.getEmptyMetrics();
    }

    const successful = requests.filter(r => r.status === 'COMPLETED');
    const failed = requests.filter(r => r.status === 'FAILED');

    const latencies = successful.map(r => r.latency).sort((a, b) => a - b);
    const totalTokens = requests.reduce((sum, req) => {
      const tokens = req.tokens as any;
      return sum + (tokens?.totalTokens || 0);
    }, 0);
    const totalCost = requests.reduce((sum, req) => sum + req.cost, 0);

    return {
      totalRequests: requests.length,
      successfulRequests: successful.length,
      failedRequests: failed.length,
      successRate: successful.length / requests.length,
      averageLatency: latencies.reduce((sum, l) => sum + l, 0) / latencies.length,
      p95Latency: latencies[Math.floor(latencies.length * 0.95)] || 0,
      p99Latency: latencies[Math.floor(latencies.length * 0.99)] || 0,
      totalTokens,
      totalCost,
      costPerToken: totalTokens > 0 ? totalCost / totalTokens : 0,
      availability: this.calculateAvailability(requests),
    };
  }

  private calculateTrends(current: any, previous: any) {
    const safeDivide = (a: number, b: number) => b === 0 ? 0 : ((a - b) / b) * 100;

    return {
      requestsChange: safeDivide(current.totalRequests, previous.totalRequests),
      latencyChange: safeDivide(current.averageLatency, previous.averageLatency),
      costChange: safeDivide(current.totalCost, previous.totalCost),
      successRateChange: safeDivide(current.successRate, previous.successRate),
    };
  }

  private generateRecommendations(metrics: any, trends: any): string[] {
    const recommendations: string[] = [];

    // Success rate recommendations
    if (metrics.successRate < 0.95) {
      recommendations.push('Success rate is below 95%. Consider investigating error patterns.');
    }

    // Latency recommendations
    if (metrics.averageLatency > 5000) {
      recommendations.push('Average latency is high (>5s). Consider optimizing requests or switching providers.');
    }

    if (trends.latencyChange > 20) {
      recommendations.push('Latency has increased significantly. Monitor provider performance.');
    }

    // Cost recommendations
    if (trends.costChange > 50) {
      recommendations.push('Costs have increased significantly. Review usage patterns and consider optimization.');
    }

    if (metrics.costPerToken > 0.00005) {
      recommendations.push('Cost per token is high. Consider using more cost-effective models.');
    }

    // Availability recommendations
    if (metrics.availability < 0.99) {
      recommendations.push('Provider availability is below 99%. Consider adding fallback providers.');
    }

    if (recommendations.length === 0) {
      recommendations.push('Provider performance is within acceptable ranges.');
    }

    return recommendations;
  }

  private async analyzeCostOptimization(requests: any[], organizationId: string) {
    // Simple cost optimization analysis
    const totalCost = requests.reduce((sum, req) => sum + req.cost, 0);
    
    // Estimate potential savings by using cheaper models
    const potentialSavings = totalCost * 0.3; // Assume 30% potential savings
    
    const recommendations = [
      'Consider using GPT-3.5-turbo instead of GPT-4 for simple tasks',
      'Implement request caching to reduce duplicate API calls',
      'Use shorter prompts to reduce token usage',
      'Batch similar requests when possible',
    ];

    return {
      potentialSavings,
      recommendations,
    };
  }

  private getTimeframes(timeframe: string) {
    const now = new Date();
    const timeMap = {
      hour: 1,
      day: 24,
      week: 24 * 7,
      month: 24 * 30,
    };

    const hours = timeMap[timeframe as keyof typeof timeMap] || 24;
    const since = new Date(now.getTime() - hours * 60 * 60 * 1000);
    const previousSince = new Date(since.getTime() - hours * 60 * 60 * 1000);

    return { since, previousSince };
  }

  private getEmptyMetrics() {
    return {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      successRate: 0,
      averageLatency: 0,
      p95Latency: 0,
      p99Latency: 0,
      totalTokens: 0,
      totalCost: 0,
      costPerToken: 0,
      availability: 1,
    };
  }

  private getEmptyReport(organizationId: string, timeframe: string): OrganizationUsageReport {
    return {
      organizationId,
      timeframe,
      summary: {
        totalRequests: 0,
        totalCost: 0,
        totalTokens: 0,
        averageLatency: 0,
        topProvider: 'None',
        topModel: 'None',
      },
      providerBreakdown: [],
      costOptimization: {
        potentialSavings: 0,
        recommendations: ['No usage data available for the selected timeframe'],
      },
    };
  }

  private groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
    return array.reduce((groups, item) => {
      const group = String(item[key]);
      groups[group] = groups[group] || [];
      groups[group].push(item);
      return groups;
    }, {} as Record<string, T[]>);
  }

  private calculateAvailability(requests: any[]): number {
    const recentFailures = requests.filter(r => 
      r.status === 'FAILED' && 
      Date.now() - new Date(r.createdAt).getTime() < 15 * 60 * 1000
    );

    if (recentFailures.length === 0) return 1.0;
    if (recentFailures.length >= 5) return 0.8;
    return 0.95;
  }

  // Scheduled task to generate daily reports
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async generateDailyReports() {
    try {
      this.logger.log('Starting daily analytics report generation');

      const organizations = await this.prisma.organization.findMany({
        select: { id: true, name: true },
      });

      for (const org of organizations) {
        const report = await this.generateOrganizationReport(org.id, 'day');
        
        // Store report or send notifications based on thresholds
        if (report.summary.totalCost > 100) { // $100 threshold
          this.loggerService.business('high_usage_alert', {
            organizationId: org.id,
            dailyCost: report.summary.totalCost,
            requests: report.summary.totalRequests,
          });
        }
      }

      this.logger.log(`Generated daily reports for ${organizations.length} organizations`);
    } catch (error) {
      this.logger.error(`Failed to generate daily reports: ${error}`);
    }
  }
}
