import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WorkflowsController } from './workflows.controller';
import { WorkflowsService } from './workflows.service';
import { Workflow } from './workflow.entity';
import { WorkflowExecutionService } from './workflow-execution.service';
import { WorkflowValidationService } from './workflow-validation.service';
import { WorkflowAnalyticsService } from './workflow-analytics.service';

@Module({
    imports: [TypeOrmModule.forFeature([Workflow])],
    controllers: [WorkflowsController],
    providers: [
        WorkflowsService,
        WorkflowExecutionService,
        WorkflowValidationService,
        WorkflowAnalyticsService,
    ],
    exports: [
        WorkflowsService,
        WorkflowExecutionService,
        WorkflowValidationService,
        WorkflowAnalyticsService,
    ],
})
export class WorkflowsModule { } 