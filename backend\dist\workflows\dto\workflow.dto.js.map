{"version": 3, "file": "workflow.dto.js", "sourceRoot": "", "sources": ["../../../src/workflows/dto/workflow.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAA8J;AAC9J,yDAAyC;AACzC,6CAAmE;AACnE,wDAAyE;AAEzE,MAAa,eAAe;CA2B3B;AA3BD,0CA2BC;AAxBC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gCAAgC,EAAE,CAAC;IAC9D,IAAA,0BAAQ,GAAE;;2CACC;AAIZ;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IAChD,IAAA,0BAAQ,GAAE;;6CACG;AAId;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IAChD,IAAA,0BAAQ,GAAE;;6CACG;AAId;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,4BAA4B,EAAE,CAAC;IAC1D,IAAA,0BAAQ,GAAE;;+CACkB;AAM7B;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,4BAA4B,EAAE,CAAC;IAClE,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;qDACD;AAKxB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,+BAA+B,EAAE,CAAC;IACrE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mDACsB;AAGnC,MAAa,kBAAkB;CAQ9B;AARD,gDAQC;AALC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,iBAAiB,EAAE,IAAI,EAAE,qCAAmB,EAAE,CAAC;IAC1E,IAAA,wBAAM,EAAC,qCAAmB,CAAC;;gDACD;AAI3B;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,+BAA+B,EAAE,CAAC;IAC7D,IAAA,0BAAQ,GAAE;;kDACkB;AAG/B,MAAa,wBAAwB;CAiBpC;AAjBD,4DAiBC;AAZC;IAJC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,0BAA0B,EAAE,CAAC;IACxD,IAAA,uBAAK,GAAE;IACP,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,EAAE,CAAC;;4DACY;AAKpB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,uCAAuC,EAAE,CAAC;IACrE,IAAA,uBAAK,GAAE;IACP,IAAA,qBAAG,EAAC,IAAI,CAAC;;4DACU;AAMpB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,sCAAsC,EAAE,CAAC;IAC5E,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;+DACA;AAG3B,MAAa,qBAAqB;CAyBjC;AAzBD,sDAyBC;AAtBC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,oCAAoC,EAAE,CAAC;IAClE,IAAA,0BAAQ,GAAE;;sDACM;AAMjB;IAJC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,uBAAuB,EAAE,IAAI,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC;IAC9E,IAAA,yBAAO,GAAE;IACT,IAAA,gCAAc,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IAC9B,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,eAAe,CAAC;;oDACF;AAM1B;IAJC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,2BAA2B,EAAE,IAAI,EAAE,CAAC,kBAAkB,CAAC,EAAE,CAAC;IACrF,IAAA,yBAAO,GAAE;IACT,IAAA,gCAAc,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IAC9B,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC;;uDACC;AAIhC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,qCAAqC,EAAE,CAAC;IACnE,IAAA,0BAAQ,GAAE;;wDACqB;AAKhC;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,8BAA8B,EAAE,CAAC;IAC5D,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,wBAAwB,CAAC;8BACrB,wBAAwB;4DAAC;AAG3C,MAAa,mBAAmB;CAqB/B;AArBD,kDAqBC;AAjBC;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,uBAAuB,EAAE,CAAC;IACrD,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;iDACT;AAIhB;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,0BAA0B,EAAE,CAAC;IACxD,IAAA,0BAAQ,GAAE;;qDACO;AAIlB;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,CAAC;IAC3F,IAAA,0BAAQ,GAAE;;qDACuC;AAIlD;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,+BAA+B,EAAE,CAAC;IAC7D,IAAA,0BAAQ,GAAE;;8DACgB;AAI3B;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;IAC9C,IAAA,0BAAQ,GAAE;;yDACW;AAGxB,MAAa,sBAAsB;CAmBlC;AAnBD,wDAmBC;AAfC;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,iCAAiC,EAAE,CAAC;IAC/D,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;sDACP;AAKlB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,kCAAkC,EAAE,CAAC;IAChE,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;uDACN;AAKnB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,kCAAkC,EAAE,CAAC;IAChE,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;uDACN;AAInB;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gCAAgC,EAAE,CAAC;IAC9D,IAAA,2BAAS,GAAE;;sDACK;AAGnB,MAAa,iBAAiB;CA0B7B;AA1BD,8CA0BC;AAvBC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,CAAC;IACpD,IAAA,0BAAQ,GAAE;;+CACG;AAKd;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,6BAA6B,EAAE,CAAC;IACnE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDACU;AAKrB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,CAAC;IACnD,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,qBAAqB,CAAC;8BACrB,qBAAqB;qDAAC;AAMnC;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,2BAA2B,EAAE,CAAC;IACjE,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,mBAAmB,CAAC;8BACrB,mBAAmB;mDAAC;AAM/B;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,8BAA8B,EAAE,CAAC;IACpE,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,sBAAsB,CAAC;8BACrB,sBAAsB;sDAAC;AAGvC,MAAa,iBAAiB;CAiC7B;AAjCD,8CAiCC;AA7BC;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,CAAC;IAC5D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;+CACG;AAKd;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,6BAA6B,EAAE,CAAC;IACnE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDACU;AAMrB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,CAAC;IAC3D,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,qBAAqB,CAAC;8BACrB,qBAAqB;qDAAC;AAMnC;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,2BAA2B,EAAE,CAAC;IACjE,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,mBAAmB,CAAC;8BACrB,mBAAmB;mDAAC;AAM/B;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,8BAA8B,EAAE,CAAC;IACpE,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,sBAAsB,CAAC;8BACrB,sBAAsB;sDAAC;AAKrC;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,yCAAyC,EAAE,CAAC;IAC/E,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;sDACU;AAGxB,MAAa,kBAAkB;CAgD9B;AAhDD,gDAgDC;AA5CC;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,8CAA8C,EAAE,CAAC;IACpF,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;kDACK;AAKhB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,2BAA2B,EAAE,IAAI,EAAE,gCAAc,EAAE,CAAC;IACvF,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,gCAAc,CAAC;;kDACC;AAKxB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,mBAAmB,EAAE,CAAC;IACzD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,GAAE;;kDACO;AAKhB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,+BAA+B,EAAE,CAAC;IACrE,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;wDACO;AAKtB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,gCAAgC,EAAE,CAAC;IACtE,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;yDACQ;AAMvB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,4BAA4B,EAAE,CAAC;IAClE,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,GAAE;IACP,IAAA,qBAAG,EAAC,CAAC,CAAC;;gDACO;AAOd;IALC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,0BAA0B,EAAE,CAAC;IAChE,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,GAAE;IACP,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,GAAG,CAAC;;iDACM;AAKf;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACxD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;kDACK;AAKhB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC;IACzE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;qDACgB"}