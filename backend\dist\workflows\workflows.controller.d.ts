import { Request as ExpressRequest } from 'express';
import { WorkflowsService } from './workflows.service';
import { WorkflowExecutionService } from './workflow-execution.service';
import { WorkflowAnalyticsService } from './workflow-analytics.service';
import { CreateWorkflowDto, UpdateWorkflowDto, WorkflowFiltersDto } from './dto/workflow.dto';
import { WorkflowStatus } from './workflow.entity';
interface RequestWithUser extends ExpressRequest {
    user: {
        userId: string;
        organizationId: string;
    };
}
export declare class WorkflowsController {
    private readonly workflowsService;
    private readonly executionService;
    private readonly analyticsService;
    constructor(workflowsService: WorkflowsService, executionService: WorkflowExecutionService, analyticsService: WorkflowAnalyticsService);
    createWorkflow(createWorkflowDto: CreateWorkflowDto, req: RequestWithUser): Promise<import("./workflow.entity").Workflow>;
    getWorkflows(filters: WorkflowFiltersDto, req: RequestWithUser): Promise<{
        workflows: import("./workflow.entity").Workflow[];
        total: number;
    }>;
    getWorkflowById(id: string, req: RequestWithUser): Promise<import("./workflow.entity").Workflow>;
    updateWorkflow(id: string, updateWorkflowDto: UpdateWorkflowDto, req: RequestWithUser): Promise<import("./workflow.entity").Workflow>;
    deleteWorkflow(id: string, req: RequestWithUser): Promise<void>;
    changeWorkflowStatus(id: string, body: {
        status: WorkflowStatus;
    }, req: RequestWithUser): Promise<import("./workflow.entity").Workflow>;
    duplicateWorkflow(id: string, req: RequestWithUser): Promise<import("./workflow.entity").Workflow>;
    executeWorkflow(id: string, body: {
        inputVariables?: Record<string, any>;
    }, req: RequestWithUser): Promise<{
        executionId: string;
        message: string;
        status: string;
    }>;
    getExecutionStatus(id: string, executionId: string, req: RequestWithUser): Promise<any>;
    pauseExecution(id: string, executionId: string, req: RequestWithUser): Promise<{
        message: string;
    }>;
    resumeExecution(id: string, executionId: string, req: RequestWithUser): Promise<{
        message: string;
    }>;
    cancelExecution(id: string, executionId: string, req: RequestWithUser): Promise<{
        message: string;
    }>;
    getWorkflowAnalytics(id: string, req: RequestWithUser): Promise<any>;
    getRealTimeMetrics(id: string, req: RequestWithUser): Promise<any>;
    getOrganizationAnalytics(startDate?: string, endDate?: string, req: RequestWithUser): Promise<any>;
    validateWorkflow(id: string, req: RequestWithUser): Promise<{
        valid: boolean;
        message: string;
        workflow: {
            id: string;
            name: string;
            steps: number;
            triggers: number;
        };
    }>;
    exportWorkflow(id: string, req: RequestWithUser): Promise<{
        id: string;
        name: string;
        description: string;
        definition: {
            version: string;
            steps: Array<{
                id: string;
                type: string;
                name: string;
                config: Record<string, any>;
                dependencies?: string[];
                conditions?: Record<string, any>;
            }>;
            triggers: Array<{
                type: import("./workflow.entity").WorkflowTriggerType;
                config: Record<string, any>;
            }>;
            variables: Record<string, any>;
            errorHandling: {
                retryCount: number;
                retryDelay: number;
                fallbackSteps?: string[];
            };
        };
        metadata: {
            tags: string[];
            category: string;
            priority: "low" | "medium" | "high" | "critical";
            estimatedDuration: number;
            costEstimate: number;
        };
        version: string;
        exportedAt: string;
    }>;
    importWorkflow(importData: {
        name: string;
        description?: string;
        definition: any;
        metadata?: any;
    }, req: RequestWithUser): Promise<import("./workflow.entity").Workflow>;
}
export {};
