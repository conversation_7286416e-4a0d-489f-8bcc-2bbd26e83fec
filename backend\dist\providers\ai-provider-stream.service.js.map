{"version": 3, "file": "ai-provider-stream.service.js", "sourceRoot": "", "sources": ["../../src/providers/ai-provider-stream.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+EAAyE;AACzE,iFAAqG;AAGrG,mCAAgC;AAChC,2CAA8C;AAC9C,yDAA2D;AAC3D,oDAA+C;AAC/C,uCAAgC;AAGzB,IAAM,uBAAuB,+BAA7B,MAAM,uBAAuB;IAIhC,YACqB,iBAA2C,EAC3C,kBAA6C;QAD7C,sBAAiB,GAAjB,iBAAiB,CAA0B;QAC3C,uBAAkB,GAAlB,kBAAkB,CAA2B;QALjD,WAAM,GAAG,IAAI,eAAM,CAAC,yBAAuB,CAAC,IAAI,CAAC,CAAC;QAClD,oBAAe,GAAG,IAAI,GAAG,EAAe,CAAC;IAKtD,CAAC;IAEL,KAAK,CAAC,aAAa,CAAC,OAAkB;QAClC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,gBAAqB,CAAC;QAE1B,IAAI,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;gBACtB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC;oBAClE,cAAc,EAAE,OAAO,CAAC,cAAc;oBACtC,YAAY,EAAE,CAAC,MAAM,CAAC;iBACzB,CAAC,CAAC;gBACH,gBAAgB,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;YAClH,CAAC;iBAAM,CAAC;gBACJ,gBAAgB,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;YAChH,CAAC;YAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;YACpE,CAAC;YAGD,MAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAU,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,OAAO,CAAC,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACnH,IAAI,CAAC,KAAK,EAAE,CAAC;gBACT,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;YAC3C,CAAC;YAGD,QAAQ,gBAAgB,CAAC,IAAI,EAAE,CAAC;gBAC5B,KAAK,QAAQ;oBACT,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;gBAC/D,KAAK,QAAQ;oBACT,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;gBAC/D,KAAK,QAAQ;oBACT,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;gBAC/D,KAAK,SAAS;oBACV,OAAO,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;gBAChE,KAAK,MAAM;oBACP,OAAO,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;gBAC7D;oBACI,MAAM,IAAI,KAAK,CAAC,8CAA8C,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAC;YAC/F,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACzG,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAEc,YAAY,CAAC,QAAa,EAAE,KAAU,EAAE,OAAkB;;;;YACrE,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,IAAI,eAAM,CAAC;gBAChE,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,MAAM;aACjC,CAAC,CAAC,CAAC;YAEJ,MAAM,MAAM,GAAG,cAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAChD,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,GAAG;gBACvC,UAAU,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI;gBACrC,MAAM,EAAE,IAAI;aACf,CAAC,CAAA,CAAC;;gBAEH,KAA0B,eAAA,WAAA,cAAA,MAAM,CAAA,YAAA,qFAAE,CAAC;oBAAT,sBAAM;oBAAN,WAAM;oBAArB,MAAM,KAAK,KAAA,CAAA;oBAClB,MAAM,OAAO,GAAG,MAAA,MAAA,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,0CAAE,KAAK,0CAAE,OAAO,CAAC;oBACjD,IAAI,OAAO,EAAE,CAAC;wBACV,oBAAM,OAAO,CAAA,CAAC;oBAClB,CAAC;gBACL,CAAC;;;;;;;;;QACL,CAAC;KAAA;IAEc,YAAY,CAAC,QAAa,EAAE,KAAU,EAAE,OAAkB;;;YACrE,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,IAAI,eAAS,CAAC;gBACnE,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,MAAM;aACjC,CAAC,CAAC,CAAC;YAEJ,MAAM,MAAM,GAAG,cAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACxC,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,UAAU,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI;gBACrC,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,MAAM,EAAE,IAAI;aACf,CAAC,CAAA,CAAC;;gBAEH,KAA0B,eAAA,WAAA,cAAA,MAAM,CAAA,YAAA,qFAAE,CAAC;oBAAT,sBAAM;oBAAN,WAAM;oBAArB,MAAM,KAAK,KAAA,CAAA;oBAClB,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAqB,EAAE,CAAC;wBACvC,oBAAM,KAAK,CAAC,KAAK,CAAC,IAAI,CAAA,CAAC;oBAC3B,CAAC;gBACL,CAAC;;;;;;;;;QACL,CAAC;KAAA;IAEc,YAAY,CAAC,QAAa,EAAE,KAAU,EAAE,OAAkB;;;YACrE,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,IAAI,kCAAkB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;YAEzG,MAAM,WAAW,GAAG,MAAM,CAAC,kBAAkB,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YACrE,MAAM,MAAM,GAAG,cAAM,WAAW,CAAC,qBAAqB,CAAC;gBACnD,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC3F,gBAAgB,EAAE;oBACd,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,GAAG;oBACvC,eAAe,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI;iBAC7C;aACJ,CAAC,CAAA,CAAC;;gBAEH,KAA0B,eAAA,KAAA,cAAA,MAAM,CAAC,MAAM,CAAA,IAAA,+DAAE,CAAC;oBAAhB,cAAa;oBAAb,WAAa;oBAA5B,MAAM,KAAK,KAAA,CAAA;oBAClB,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;oBAC1B,IAAI,IAAI,EAAE,CAAC;wBACP,oBAAM,IAAI,CAAA,CAAC;oBACf,CAAC;gBACL,CAAC;;;;;;;;;QACL,CAAC;KAAA;IAEc,aAAa,CAAC,QAAa,EAAE,KAAU,EAAE,OAAkB;;;;YACtE,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,IAAI,mBAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;YAE9F,MAAM,MAAM,GAAG,cAAM,MAAM,CAAC,IAAI,CAAC;gBAC7B,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,GAAG;gBACvC,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI;gBACpC,MAAM,EAAE,IAAI;aACf,CAAC,CAAA,CAAC;;gBAEH,KAA0B,eAAA,WAAA,cAAA,MAAM,CAAA,YAAA,qFAAE,CAAC;oBAAT,sBAAM;oBAAN,WAAM;oBAArB,MAAM,KAAK,KAAA,CAAA;oBAClB,IAAI,MAAA,MAAA,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,0CAAE,KAAK,0CAAE,OAAO,EAAE,CAAC;wBACnC,oBAAM,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAA,CAAC;oBACzC,CAAC;gBACL,CAAC;;;;;;;;;QACL,CAAC;KAAA;IAEc,UAAU,CAAC,QAAa,EAAE,KAAU,EAAE,OAAkB;;;;YACnE,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,IAAI,eAAI,CAAC;gBAC9D,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,MAAM;aACjC,CAAC,CAAC,CAAC;YAEJ,MAAM,MAAM,GAAG,cAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAChD,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,GAAG;gBACvC,UAAU,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI;gBACrC,MAAM,EAAE,IAAI;aACf,CAAC,CAAA,CAAC;;gBAEH,KAA0B,eAAA,WAAA,cAAA,MAAM,CAAA,YAAA,qFAAE,CAAC;oBAAT,sBAAM;oBAAN,WAAM;oBAArB,MAAM,KAAK,KAAA,CAAA;oBAClB,MAAM,OAAO,GAAG,MAAA,MAAA,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,0CAAE,KAAK,0CAAE,OAAO,CAAC;oBACjD,IAAI,OAAO,EAAE,CAAC;wBACV,oBAAM,OAAO,CAAA,CAAC;oBAClB,CAAC;gBACL,CAAC;;;;;;;;;QACL,CAAC;KAAA;IAEO,iBAAiB,CAAC,UAAkB,EAAE,aAAwB;QAClE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;YACxC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,EAAE,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,cAAsB;;QAKzD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACD,MAAM,WAAW,GAAc;gBAC3B,SAAS,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC/B,UAAU;gBACV,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC;gBACvE,SAAS,EAAE,EAAE;gBACb,cAAc;aACjB,CAAC;YAGF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;YACrD,IAAI,OAAO,GAAG,EAAE,CAAC;;gBACjB,KAA0B,eAAA,WAAA,cAAA,MAAM,CAAA,YAAA,4EAAE,CAAC;oBAAT,sBAAM;oBAAN,WAAM;oBAArB,MAAM,KAAK,KAAA,CAAA;oBAClB,OAAO,IAAI,KAAK,CAAC;gBACrB,CAAC;;;;;;;;;YAED,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aAClC,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBAC/B,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAClE,CAAC;QACN,CAAC;IACL,CAAC;CACJ,CAAA;AArMY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;qCAM+B,sDAAwB;QACvB,wDAAyB;GANzD,uBAAuB,CAqMnC"}