import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AgentOrchestratorService, AgentWizardData, AgentTestResult } from './agent-orchestrator.service';
import { Agent, AgentExecution } from '../database/entities/agent.entity';
import { CreateAgentInstanceDto, UpdateAgentInstanceDto, ExecuteAgentDto } from './dto/agent.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { PermissionGuard } from '../auth/permission.guard';
import { RequirePermissions } from '../auth/permissions.decorator';

@ApiTags('Agents')
@Controller('agents')
@UseGuards(JwtAuthGuard, PermissionGuard)
@ApiBearerAuth()
export class AgentsController {
  constructor(private readonly agentOrchestratorService: AgentOrchestratorService) { }

  @Post()
  @RequirePermissions(['agents:create'])
  @ApiOperation({ summary: 'Create a new agent' })
  @ApiResponse({ status: 201, description: 'Agent created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async createAgent(
    @Body() createAgentDto: CreateAgentInstanceDto,
    @Request() req: any,
  ): Promise<Agent> {
    return this.agentOrchestratorService.createAgent(
      createAgentDto,
      req.user.organizationId,
      req.user.id,
    );
  }

  @Post('wizard')
  @RequirePermissions(['agents:create'])
  @ApiOperation({ summary: 'Create agent using wizard' })
  @ApiResponse({ status: 201, description: 'Agent created successfully from wizard' })
  @ApiResponse({ status: 400, description: 'Invalid wizard data' })
  async createAgentFromWizard(
    @Body() wizardData: AgentWizardData,
    @Request() req: any,
  ): Promise<Agent> {
    return this.agentOrchestratorService.createAgentFromWizard(
      wizardData,
      req.user.organizationId,
      req.user.id,
    );
  }

  @Get('wizard/steps')
  @RequirePermissions(['agents:read'])
  @ApiOperation({ summary: 'Get agent wizard steps' })
  @ApiResponse({ status: 200, description: 'Wizard steps retrieved successfully' })
  async getAgentWizardSteps(): Promise<any[]> {
    return this.agentOrchestratorService.getAgentWizardSteps();
  }

  @Get()
  @RequirePermissions(['agents:read'])
  @ApiOperation({ summary: 'Get all agents for organization' })
  @ApiResponse({ status: 200, description: 'Agents retrieved successfully' })
  async getAgents(@Request() req: any): Promise<Agent[]> {
    return this.agentOrchestratorService.getAgentsByOrganization(req.user.organizationId);
  }

  @Get(':id')
  @RequirePermissions(['agents:read'])
  @ApiOperation({ summary: 'Get agent by ID' })
  @ApiResponse({ status: 200, description: 'Agent retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  async getAgent(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<Agent> {
    return this.agentOrchestratorService.getAgentById(id, req.user.organizationId);
  }

  @Put(':id')
  @RequirePermissions(['agents:update'])
  @ApiOperation({ summary: 'Update agent' })
  @ApiResponse({ status: 200, description: 'Agent updated successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  async updateAgent(
    @Param('id') id: string,
    @Body() updateAgentDto: UpdateAgentInstanceDto,
    @Request() req: any,
  ): Promise<Agent> {
    return this.agentOrchestratorService.updateAgent(id, updateAgentDto, req.user.organizationId);
  }

  @Delete(':id')
  @RequirePermissions(['agents:delete'])
  @ApiOperation({ summary: 'Delete agent' })
  @ApiResponse({ status: 200, description: 'Agent deleted successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  async deleteAgent(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<void> {
    return this.agentOrchestratorService.deleteAgent(id, req.user.organizationId);
  }

  @Post(':id/execute')
  @RequirePermissions(['agents:execute'])
  @ApiOperation({ summary: 'Execute agent' })
  @ApiResponse({ status: 200, description: 'Agent executed successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  async executeAgent(
    @Param('id') id: string,
    @Body() executeDto: ExecuteAgentDto,
    @Request() req: any,
  ): Promise<AgentExecution> {
    return this.agentOrchestratorService.executeAgent(id, executeDto, req.user.organizationId);
  }

  @Post(':id/test')
  @RequirePermissions(['agents:execute'])
  @ApiOperation({ summary: 'Test agent without saving execution' })
  @ApiResponse({ status: 200, description: 'Agent test completed' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  async testAgent(
    @Param('id') id: string,
    @Body() body: { message: string },
    @Request() req: any,
  ): Promise<AgentTestResult> {
    return this.agentOrchestratorService.testAgent(id, body.message, req.user.organizationId);
  }

  @Post(':id/duplicate')
  @RequirePermissions(['agents:create'])
  @ApiOperation({ summary: 'Duplicate agent' })
  @ApiResponse({ status: 201, description: 'Agent duplicated successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  async duplicateAgent(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<Agent> {
    return this.agentOrchestratorService.duplicateAgent(id, req.user.organizationId, req.user.id);
  }

  @Get(':id/executions')
  @RequirePermissions(['agents:read'])
  @ApiOperation({ summary: 'Get agent executions' })
  @ApiResponse({ status: 200, description: 'Executions retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  async getAgentExecutions(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<AgentExecution[]> {
    return this.agentOrchestratorService.getAgentExecutions(id, req.user.organizationId);
  }

  @Get(':id/analytics')
  @RequirePermissions(['agents:read'])
  @ApiOperation({ summary: 'Get agent analytics' })
  @ApiResponse({ status: 200, description: 'Analytics retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  async getAgentAnalytics(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<any> {
    return this.agentOrchestratorService.getAgentAnalytics(id, req.user.organizationId);
  }

  @Post('collaborations')
  @RequirePermissions(['agents:create'])
  @ApiOperation({ summary: 'Create agent collaboration' })
  @ApiResponse({ status: 201, description: 'Collaboration created successfully' })
  async createCollaboration(
    @Body() body: {
      name: string;
      agentIds: string[];
      coordinatorId: string;
      workflow: Record<string, any>;
    },
    @Request() req: any,
  ): Promise<any> {
    return this.agentOrchestratorService.createCollaboration(
      body.name,
      body.agentIds,
      body.coordinatorId,
      body.workflow,
      req.user.organizationId,
      req.user.id,
    );
  }

  @Post('collaborations/:id/execute')
  @RequirePermissions(['agents:execute'])
  @ApiOperation({ summary: 'Execute agent collaboration' })
  @ApiResponse({ status: 200, description: 'Collaboration executed successfully' })
  @ApiResponse({ status: 404, description: 'Collaboration not found' })
  async executeCollaboration(
    @Param('id') id: string,
    @Body() body: { input: string },
    @Request() req: any,
  ): Promise<any> {
    return this.agentOrchestratorService.executeCollaboration(id, body.input, req.user.organizationId);
  }

  @Get('collaborations')
  @RequirePermissions(['agents:read'])
  @ApiOperation({ summary: 'Get agent collaborations' })
  @ApiResponse({ status: 200, description: 'Collaborations retrieved successfully' })
  async getCollaborations(@Request() req: any): Promise<any[]> {
    return this.agentOrchestratorService.getCollaborationsByOrganization(req.user.organizationId);
  }

  @Get('stats/overview')
  @RequirePermissions(['agents:read'])
  @ApiOperation({ summary: 'Get agents overview statistics' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  async getAgentsOverview(@Request() req: any): Promise<any> {
    const agents = await this.agentOrchestratorService.getAgentsByOrganization(req.user.organizationId);

    const stats = {
      totalAgents: agents.length,
      activeAgents: agents.filter(a => a.status === 'ACTIVE').length,
      inactiveAgents: agents.filter(a => a.status === 'INACTIVE').length,
      errorAgents: agents.filter(a => a.status === 'ERROR').length,
      totalExecutions: agents.reduce((sum, agent) => sum + (agent.performanceMetrics?.totalExecutions || 0), 0),
      averageSuccessRate: agents.length > 0
        ? agents.reduce((sum, agent) => sum + (agent.performanceMetrics?.successRate || 0), 0) / agents.length
        : 0,
      averageResponseTime: agents.length > 0
        ? agents.reduce((sum, agent) => sum + (agent.performanceMetrics?.averageResponseTime || 0), 0) / agents.length
        : 0,
      agentTypes: agents.reduce((acc, agent) => {
        acc[agent.type] = (acc[agent.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      providers: agents.reduce((acc, agent) => {
        acc[agent.primaryProvider] = (acc[agent.primaryProvider] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    };

    return stats;
  }

  @Get('stats/performance')
  @RequirePermissions(['agents:read'])
  @ApiOperation({ summary: 'Get agents performance statistics' })
  @ApiResponse({ status: 200, description: 'Performance statistics retrieved successfully' })
  async getAgentsPerformance(@Request() req: any): Promise<any> {
    const agents = await this.agentOrchestratorService.getAgentsByOrganization(req.user.organizationId);

    const performanceStats = agents.map(agent => ({
      id: agent.id,
      name: agent.name,
      type: agent.type,
      status: agent.status,
      totalExecutions: agent.performanceMetrics?.totalExecutions || 0,
      successRate: agent.performanceMetrics?.successRate || 0,
      averageResponseTime: agent.performanceMetrics?.averageResponseTime || 0,
      lastExecuted: agent.performanceMetrics?.lastExecuted,
      primaryProvider: agent.primaryProvider,
    }));

    return {
      agents: performanceStats,
      summary: {
        totalExecutions: performanceStats.reduce((sum, agent) => sum + agent.totalExecutions, 0),
        averageSuccessRate: performanceStats.length > 0
          ? performanceStats.reduce((sum, agent) => sum + agent.successRate, 0) / performanceStats.length
          : 0,
        averageResponseTime: performanceStats.length > 0
          ? performanceStats.reduce((sum, agent) => sum + agent.averageResponseTime, 0) / performanceStats.length
          : 0,
      },
    };
  }

  @Get('search')
  @RequirePermissions(['agents:read'])
  @ApiOperation({ summary: 'Search agents' })
  @ApiResponse({ status: 200, description: 'Search results retrieved successfully' })
  async searchAgents(
    @Query('q') query: string,
    @Query('type') type?: string,
    @Query('status') status?: string,
    @Query('provider') provider?: string,
    @Request() req: any,
  ): Promise<Agent[]> {
    const agents = await this.agentOrchestratorService.getAgentsByOrganization(req.user.organizationId);

    let filteredAgents = agents;

    // Apply search query
    if (query) {
      const searchTerm = query.toLowerCase();
      filteredAgents = filteredAgents.filter(agent =>
        agent.name.toLowerCase().includes(searchTerm) ||
        agent.type.toLowerCase().includes(searchTerm) ||
        agent.primaryProvider.toLowerCase().includes(searchTerm)
      );
    }

    // Apply type filter
    if (type) {
      filteredAgents = filteredAgents.filter(agent => agent.type === type);
    }

    // Apply status filter
    if (status) {
      filteredAgents = filteredAgents.filter(agent => agent.status === status);
    }

    // Apply provider filter
    if (provider) {
      filteredAgents = filteredAgents.filter(agent => agent.primaryProvider === provider);
    }

    return filteredAgents;
  }

  @Post(':id/enable')
  @RequirePermissions(['agents:update'])
  @ApiOperation({ summary: 'Enable agent' })
  @ApiResponse({ status: 200, description: 'Agent enabled successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  async enableAgent(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<Agent> {
    return this.agentOrchestratorService.updateAgent(
      id,
      { status: 'ACTIVE' },
      req.user.organizationId,
    );
  }

  @Post(':id/disable')
  @RequirePermissions(['agents:update'])
  @ApiOperation({ summary: 'Disable agent' })
  @ApiResponse({ status: 200, description: 'Agent disabled successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  async disableAgent(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<Agent> {
    return this.agentOrchestratorService.updateAgent(
      id,
      { status: 'INACTIVE' },
      req.user.organizationId,
    );
  }

  @Get('templates/:templateId/preview')
  @RequirePermissions(['agents:read'])
  @ApiOperation({ summary: 'Preview agent template' })
  @ApiResponse({ status: 200, description: 'Template preview retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Template not found' })
  async previewTemplate(
    @Param('templateId') templateId: string,
    @Request() req: any,
  ): Promise<any> {
    // This would typically fetch template details and return a preview
    // For now, return a mock preview structure
    return {
      templateId,
      preview: {
        name: 'Template Preview',
        description: 'This is a preview of the selected template',
        capabilities: ['chat', 'analysis'],
        estimatedCost: '$0.01 per message',
        setupTime: '2-3 minutes',
      },
    };
  }

  @Post('bulk/actions')
  @RequirePermissions(['agents:update'])
  @ApiOperation({ summary: 'Perform bulk actions on agents' })
  @ApiResponse({ status: 200, description: 'Bulk actions completed successfully' })
  async bulkActions(
    @Body() body: {
      agentIds: string[];
      action: 'enable' | 'disable' | 'delete';
    },
    @Request() req: any,
  ): Promise<any> {
    const { agentIds, action } = body;
    const results = [];

    for (const agentId of agentIds) {
      try {
        switch (action) {
          case 'enable':
            await this.agentOrchestratorService.updateAgent(
              agentId,
              { status: 'ACTIVE' },
              req.user.organizationId,
            );
            break;
          case 'disable':
            await this.agentOrchestratorService.updateAgent(
              agentId,
              { status: 'INACTIVE' },
              req.user.organizationId,
            );
            break;
          case 'delete':
            await this.agentOrchestratorService.deleteAgent(agentId, req.user.organizationId);
            break;
        }
        results.push({ agentId, success: true });
      } catch (error) {
        results.push({ agentId, success: false, error: error.message });
      }
    }

    return {
      action,
      totalAgents: agentIds.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results,
    };
  }
}