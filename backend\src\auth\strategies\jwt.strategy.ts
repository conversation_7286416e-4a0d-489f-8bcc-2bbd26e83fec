import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';

export interface JwtPayload {
    sub: string;
    email: string;
    roles: string[];
    tenantId?: string;
    iat?: number;
    exp?: number;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
    constructor(private configService: ConfigService) {
        super({
            jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configService.get<string>('JWT_SECRET'),
            passReqToCallback: true,
        });
    }

    async validate(request: Request, payload: JwtPayload) {
        // Extract tenant from request headers or subdomain
        const tenantId = this.extractTenantFromRequest(request);

        // Validate tenant access if tenantId is provided in payload
        if (payload.tenantId && tenantId && payload.tenantId !== tenantId) {
            throw new UnauthorizedException('Tenant access denied');
        }

        return {
            userId: payload.sub,
            email: payload.email,
            roles: payload.roles,
            tenantId: payload.tenantId || tenantId,
        };
    }

    private extractTenantFromRequest(request: Request): string | undefined {
        // Extract tenant from subdomain
        const host = request.headers.host;
        if (host) {
            const subdomain = host.split('.')[0];
            if (subdomain && subdomain !== 'www' && subdomain !== 'api') {
                return subdomain;
            }
        }

        // Extract tenant from custom header
        const tenantHeader = request.headers['x-tenant-id'] as string;
        if (tenantHeader) {
            return tenantHeader;
        }

        return undefined;
    }
} 