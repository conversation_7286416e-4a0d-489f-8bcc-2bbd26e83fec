import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';

export interface LogContext {
  userId?: string;
  organizationId?: string;
  requestId?: string;
  ip?: string;
  userAgent?: string;
  [key: string]: any;
}

@Injectable()
export class LoggerService implements NestLoggerService {
  private logger: winston.Logger;

  constructor(private configService: ConfigService) {
    this.logger = winston.createLogger({
      level: this.configService.get('app.monitoring.logLevel', 'info'),
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json(),
      ),
      defaultMeta: {
        service: 'synapseai-backend',
        environment: this.configService.get('app.server.nodeEnv'),
      },
      transports: [
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple(),
          ),
        }),
      ],
    });

    // Add file transport for production
    if (this.configService.get('app.server.nodeEnv') === 'production') {
      this.logger.add(
        new winston.transports.File({
          filename: 'logs/error.log',
          level: 'error',
        }),
      );
      this.logger.add(
        new winston.transports.File({
          filename: 'logs/combined.log',
        }),
      );
    }
  }

  log(message: string, context?: LogContext): void {
    this.logger.info(message, context);
  }

  error(message: string, trace?: string, context?: LogContext): void {
    this.logger.error(message, { trace, ...context });
  }

  warn(message: string, context?: LogContext): void {
    this.logger.warn(message, context);
  }

  debug(message: string, context?: LogContext): void {
    this.logger.debug(message, context);
  }

  verbose(message: string, context?: LogContext): void {
    this.logger.verbose(message, context);
  }

  // Audit logging for security events
  audit(event: string, details: any, context?: LogContext): void {
    this.logger.info('AUDIT', {
      event,
      details,
      ...context,
      audit: true,
    });
  }

  // Performance logging
  performance(operation: string, duration: number, context?: LogContext): void {
    this.logger.info('PERFORMANCE', {
      operation,
      duration,
      ...context,
      performance: true,
    });
  }

  // Business logic logging
  business(event: string, details: any, context?: LogContext): void {
    this.logger.info('BUSINESS', {
      event,
      details,
      ...context,
      business: true,
    });
  }

  // Security logging
  security(event: string, details: any, context?: LogContext): void {
    this.logger.warn('SECURITY', {
      event,
      details,
      ...context,
      security: true,
    });
  }
}
