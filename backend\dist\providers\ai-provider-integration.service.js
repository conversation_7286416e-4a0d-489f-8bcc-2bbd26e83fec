"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AIProviderIntegrationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIProviderIntegrationService = void 0;
const common_1 = require("@nestjs/common");
const ai_provider_manager_service_1 = require("./ai-provider-manager.service");
const ai_provider_selector_service_1 = require("./ai-provider-selector.service");
const apix_gateway_1 = require("../websocket/apix.gateway");
const config_1 = require("@nestjs/config");
const openai_1 = require("openai");
const sdk_1 = require("@anthropic-ai/sdk");
const generative_ai_1 = require("@google/generative-ai");
const mistralai_1 = require("@mistralai/mistralai");
const groq_sdk_1 = require("groq-sdk");
let AIProviderIntegrationService = AIProviderIntegrationService_1 = class AIProviderIntegrationService {
    constructor(providerManager, providerSelector, apixGateway, configService) {
        this.providerManager = providerManager;
        this.providerSelector = providerSelector;
        this.apixGateway = apixGateway;
        this.configService = configService;
        this.logger = new common_1.Logger(AIProviderIntegrationService_1.name);
        this.providerClients = new Map();
    }
    async processRequest(request) {
        var _a, _b;
        const startTime = Date.now();
        let selectedProvider;
        let selectedModel;
        try {
            this.apixGateway.emitToOrganization(request.organizationId, 'provider.request.start', {
                requestId: request.requestId,
                providerId: request.providerId,
                modelId: request.modelId,
                input: request.messages,
                timestamp: new Date(),
            });
            if (!request.providerId) {
                const selection = await this.providerSelector.selectOptimalProvider({
                    organizationId: request.organizationId,
                    capabilities: ['chat'],
                });
                request.providerId = selection.providerId;
                request.modelId = selection.modelId;
            }
            selectedProvider = await this.providerManager.getCachedProvider(request.providerId);
            if (!selectedProvider) {
                selectedProvider = await this.providerManager.getProviderById(request.providerId, request.organizationId);
            }
            if (!selectedProvider || !selectedProvider.isActive) {
                throw new Error('Provider not available');
            }
            selectedModel = ((_a = selectedProvider.models) === null || _a === void 0 ? void 0 : _a.find((m) => m.id === request.modelId)) ||
                ((_b = selectedProvider.models) === null || _b === void 0 ? void 0 : _b[0]);
            if (!selectedModel) {
                throw new Error('Model not available');
            }
            const response = await this.executeWithRetry(() => this.callProvider(selectedProvider, selectedModel, request), 3, request.organizationId);
            const latency = Date.now() - startTime;
            const cost = this.calculateCost(selectedProvider.type, response.usage.promptTokens, response.usage.completionTokens);
            const finalResponse = {
                requestId: request.requestId,
                providerId: selectedProvider.id,
                modelId: selectedModel.id,
                content: response.content,
                usage: response.usage,
                cost,
                latency,
                metadata: response.metadata,
            };
            await this.providerManager.trackUsage(selectedProvider.id, {
                requests: 1,
                tokensUsed: response.usage.totalTokens,
                costInCents: Math.round(cost * 100),
            });
            await this.providerSelector.updateProviderPerformance(selectedProvider.id, {
                latency,
                cost,
                success: true,
            });
            this.apixGateway.emitToOrganization(request.organizationId, 'provider.request.complete', {
                requestId: request.requestId,
                output: response.content,
                durationMs: latency,
                timestamp: new Date(),
            });
            this.logger.log(`Request ${request.requestId} completed successfully with provider ${selectedProvider.id}`);
            return finalResponse;
        }
        catch (error) {
            const latency = Date.now() - startTime;
            if (selectedProvider) {
                await this.providerSelector.updateProviderPerformance(selectedProvider.id, {
                    latency,
                    cost: 0,
                    success: false,
                });
            }
            this.apixGateway.emitToOrganization(request.organizationId, 'provider.request.error', {
                requestId: request.requestId,
                error: error instanceof Error ? error.message : String(error),
                retryable: this.isRetryableError(error),
                timestamp: new Date(),
            });
            this.logger.error(`Request ${request.requestId} failed: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async callProvider(provider, model, request) {
        switch (provider.type) {
            case 'OPENAI':
                return this.callOpenAI(provider, model, request);
            case 'CLAUDE':
                return this.callClaude(provider, model, request);
            case 'GEMINI':
                return this.callGemini(provider, model, request);
            case 'MISTRAL':
                return this.callMistral(provider, model, request);
            case 'GROQ':
                return this.callGroq(provider, model, request);
            default:
                throw new Error(`Unsupported provider type: ${provider.type}`);
        }
    }
    async callOpenAI(provider, model, request) {
        var _a, _b, _c, _d, _e, _f;
        const client = this.getOrCreateClient(provider.id, () => new openai_1.OpenAI({ apiKey: provider.config.apiKey }));
        const completion = await client.chat.completions.create({
            model: model.name,
            messages: request.messages,
            temperature: request.temperature || 0.7,
            max_tokens: request.maxTokens || 1000,
            stream: false,
        });
        return {
            content: ((_b = (_a = completion.choices[0]) === null || _a === void 0 ? void 0 : _a.message) === null || _b === void 0 ? void 0 : _b.content) || '',
            usage: {
                promptTokens: ((_c = completion.usage) === null || _c === void 0 ? void 0 : _c.prompt_tokens) || 0,
                completionTokens: ((_d = completion.usage) === null || _d === void 0 ? void 0 : _d.completion_tokens) || 0,
                totalTokens: ((_e = completion.usage) === null || _e === void 0 ? void 0 : _e.total_tokens) || 0,
            },
            metadata: {
                model: completion.model,
                finishReason: (_f = completion.choices[0]) === null || _f === void 0 ? void 0 : _f.finish_reason,
            },
        };
    }
    async callClaude(provider, model, request) {
        var _a;
        const client = this.getOrCreateClient(provider.id, () => new sdk_1.Anthropic({ apiKey: provider.config.apiKey }));
        const systemMessage = request.messages.find(m => m.role === 'system');
        const conversationMessages = request.messages.filter(m => m.role !== 'system');
        const message = await client.messages.create({
            model: model.name,
            max_tokens: request.maxTokens || 1000,
            temperature: request.temperature || 0.7,
            system: systemMessage === null || systemMessage === void 0 ? void 0 : systemMessage.content,
            messages: conversationMessages.map(m => ({
                role: m.role,
                content: m.content,
            })),
        });
        const content = ((_a = message.content[0]) === null || _a === void 0 ? void 0 : _a.type) === 'text' ? message.content[0].text : '';
        return {
            content,
            usage: {
                promptTokens: message.usage.input_tokens,
                completionTokens: message.usage.output_tokens,
                totalTokens: message.usage.input_tokens + message.usage.output_tokens,
            },
            metadata: {
                model: message.model,
                stopReason: message.stop_reason,
            },
        };
    }
    async callGemini(provider, model, request) {
        const apiKey = this.configService.get('app.aiProviders.google.apiKey');
        if (!apiKey) {
            throw new Error('Google AI API key not configured');
        }
        const client = this.getOrCreateClient(provider.id, () => new generative_ai_1.GoogleGenerativeAI(apiKey));
        const genModel = client.getGenerativeModel({ model: model.name });
        const prompt = request.messages.map(msg => msg.content).join('\n');
        const result = await genModel.generateContent(prompt);
        const response = await result.response;
        const text = response.text();
        const estimatedInputTokens = Math.ceil(prompt.length / 4);
        const estimatedOutputTokens = Math.ceil(text.length / 4);
        return {
            content: text,
            usage: {
                promptTokens: estimatedInputTokens,
                completionTokens: estimatedOutputTokens,
                totalTokens: estimatedInputTokens + estimatedOutputTokens,
            },
            cost: this.calculateCost(provider.id, model.name, estimatedInputTokens + estimatedOutputTokens),
        };
    }
    async callMistral(provider, model, request) {
        const client = this.getOrCreateClient(provider.id, () => new mistralai_1.Mistral(provider.config.apiKey));
        const response = await client.chat({
            model: model.name,
            messages: request.messages,
            temperature: request.temperature || 0.7,
            maxTokens: request.maxTokens || 1000,
        });
        const choice = response.choices[0];
        const usage = response.usage;
        return {
            content: choice.message.content,
            usage: {
                promptTokens: usage.prompt_tokens,
                completionTokens: usage.completion_tokens,
                totalTokens: usage.total_tokens,
            },
            metadata: {
                model: response.model,
                finishReason: choice.finish_reason,
            },
        };
    }
    async callGroq(provider, model, request) {
        const apiKey = this.configService.get('app.aiProviders.groq.apiKey');
        if (!apiKey) {
            throw new Error('Groq API key not configured');
        }
        const client = this.getOrCreateClient(provider.id, () => new groq_sdk_1.Groq({ apiKey }));
        const response = await client.chat.completions.create({
            model: model.name,
            messages: request.messages,
            temperature: request.temperature || 0.7,
            max_tokens: request.maxTokens || 1000,
        });
        const choice = response.choices[0];
        const usage = response.usage;
        return {
            content: choice.message.content,
            usage: {
                promptTokens: usage.prompt_tokens,
                completionTokens: usage.completion_tokens,
                totalTokens: usage.total_tokens,
            },
            cost: this.calculateCost(provider.id, model.name, usage.total_tokens),
        };
    }
    getOrCreateClient(providerId, clientFactory) {
        if (!this.providerClients.has(providerId)) {
            this.providerClients.set(providerId, clientFactory());
        }
        return this.providerClients.get(providerId);
    }
    async executeWithRetry(operation, maxRetries, organizationId, delay = 1000) {
        let lastError = null;
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await operation();
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));
                if (attempt === maxRetries || !this.isRetryableError(error)) {
                    break;
                }
                this.logger.warn(`Attempt ${attempt} failed, retrying in ${delay}ms: ${error instanceof Error ? error.message : String(error)}`);
                await new Promise(resolve => setTimeout(resolve, delay));
                delay *= 2;
            }
        }
        throw lastError || new Error('Unknown error occurred');
    }
    isRetryableError(error) {
        const retryableErrors = [
            'ECONNRESET',
            'ETIMEDOUT',
            'ENOTFOUND',
            'rate_limit_exceeded',
            'server_error',
            'service_unavailable',
        ];
        return retryableErrors.some(retryableError => {
            var _a, _b;
            return ((_a = error.message) === null || _a === void 0 ? void 0 : _a.toLowerCase().includes(retryableError.toLowerCase())) ||
                ((_b = error.code) === null || _b === void 0 ? void 0 : _b.toLowerCase().includes(retryableError.toLowerCase()));
        });
    }
    calculateCost(providerType, promptTokens, completionTokens) {
        const costPer1KTokens = {
            OPENAI: { input: 0.0015, output: 0.002 },
            CLAUDE: { input: 0.008, output: 0.024 },
            GEMINI: { input: 0.00025, output: 0.0005 },
            MISTRAL: { input: 0.0007, output: 0.0007 },
            GROQ: { input: 0.0001, output: 0.0001 },
        };
        const pricing = costPer1KTokens[providerType] || { input: 0.001, output: 0.001 };
        return (promptTokens / 1000) * pricing.input + (completionTokens / 1000) * pricing.output;
    }
};
exports.AIProviderIntegrationService = AIProviderIntegrationService;
exports.AIProviderIntegrationService = AIProviderIntegrationService = AIProviderIntegrationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [ai_provider_manager_service_1.AIProviderManagerService,
        ai_provider_selector_service_1.AIProviderSelectorService,
        apix_gateway_1.ApixGateway,
        config_1.ConfigService])
], AIProviderIntegrationService);
//# sourceMappingURL=ai-provider-integration.service.js.map