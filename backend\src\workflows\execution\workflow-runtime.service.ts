import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { LoggerService } from '../../common/services/logger.service';
import { AgentRuntimeService } from '../../agents/execution/agent-runtime.service';
import { ToolRuntimeService } from '../../tools/execution/tool-runtime.service';

export interface WorkflowNode {
  id: string;
  type: 'agent' | 'tool' | 'condition' | 'parallel' | 'delay';
  config: Record<string, any>;
  position: { x: number; y: number };
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  condition?: string;
  label?: string;
}

export interface WorkflowDefinition {
  id: string;
  name: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  variables: Record<string, any>;
  settings: {
    timeout?: number;
    retryPolicy?: {
      maxRetries: number;
      backoffStrategy: 'linear' | 'exponential';
    };
  };
}

export interface WorkflowExecutionContext {
  workflowId: string;
  input: Record<string, any>;
  variables?: Record<string, any>;
  sessionId?: string;
  userId?: string;
  organizationId?: string;
}

export interface WorkflowExecutionResult {
  success: boolean;
  output: Record<string, any>;
  duration: number;
  steps: Array<{
    nodeId: string;
    type: string;
    success: boolean;
    output: any;
    duration: number;
    error?: string;
  }>;
  error?: string;
}

@Injectable()
export class WorkflowRuntimeService {
  private readonly logger = new Logger(WorkflowRuntimeService.name);

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private loggerService: LoggerService,
    private agentRuntime: AgentRuntimeService,
    private toolRuntime: ToolRuntimeService,
  ) {}

  async executeWorkflow(context: WorkflowExecutionContext): Promise<WorkflowExecutionResult> {
    const startTime = Date.now();
    const steps: WorkflowExecutionResult['steps'] = [];

    try {
      // 1. Load workflow definition
      const workflow = await this.loadWorkflow(context.workflowId);
      if (!workflow) {
        throw new Error(`Workflow ${context.workflowId} not found`);
      }

      // 2. Initialize execution context
      const executionContext = {
        ...context,
        variables: {
          ...workflow.variables,
          ...context.variables,
          ...context.input,
        },
      };

      // 3. Create execution record
      const execution = await this.createExecutionRecord(context);

      // 4. Execute workflow
      const result = await this.executeWorkflowNodes(workflow, executionContext, steps);

      // 5. Update execution record
      await this.updateExecutionRecord(execution.id, {
        status: result.success ? 'COMPLETED' : 'FAILED',
        output: result.output,
        steps,
        duration: Date.now() - startTime,
        error: result.error,
      });

      return {
        ...result,
        duration: Date.now() - startTime,
        steps,
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      this.loggerService.error(`Workflow execution failed: ${errorMessage}`, error instanceof Error ? error.stack : undefined);

      return {
        success: false,
        output: {},
        duration,
        steps,
        error: errorMessage,
      };
    }
  }

  private async loadWorkflow(workflowId: string): Promise<WorkflowDefinition | null> {
    const workflow = await this.prisma.workflow.findUnique({
      where: { id: workflowId },
    });

    if (!workflow) return null;

    return {
      id: workflow.id,
      name: workflow.name,
      nodes: workflow.nodes as WorkflowNode[],
      edges: workflow.edges as WorkflowEdge[],
      variables: workflow.variables as Record<string, any>,
      settings: workflow.settings as any,
    };
  }

  private async executeWorkflowNodes(
    workflow: WorkflowDefinition,
    context: WorkflowExecutionContext,
    steps: WorkflowExecutionResult['steps']
  ): Promise<Omit<WorkflowExecutionResult, 'duration' | 'steps'>> {
    
    // Find start node (node with no incoming edges)
    const startNode = this.findStartNode(workflow);
    if (!startNode) {
      throw new Error('No start node found in workflow');
    }

    // Execute nodes starting from start node
    const executionState = {
      variables: { ...context.variables },
      completedNodes: new Set<string>(),
      nodeOutputs: new Map<string, any>(),
    };

    const success = await this.executeNode(startNode, workflow, context, executionState, steps);

    return {
      success,
      output: executionState.variables,
      error: success ? undefined : 'Workflow execution failed',
    };
  }

  private findStartNode(workflow: WorkflowDefinition): WorkflowNode | null {
    const nodesWithIncomingEdges = new Set(workflow.edges.map(edge => edge.target));
    return workflow.nodes.find(node => !nodesWithIncomingEdges.has(node.id)) || null;
  }

  private async executeNode(
    node: WorkflowNode,
    workflow: WorkflowDefinition,
    context: WorkflowExecutionContext,
    state: any,
    steps: WorkflowExecutionResult['steps']
  ): Promise<boolean> {
    
    if (state.completedNodes.has(node.id)) {
      return true; // Already executed
    }

    const stepStartTime = Date.now();

    try {
      let stepResult: any;
      let success = true;

      // Execute based on node type
      switch (node.type) {
        case 'agent':
          stepResult = await this.executeAgentNode(node, context, state);
          break;
        case 'tool':
          stepResult = await this.executeToolNode(node, context, state);
          break;
        case 'condition':
          stepResult = await this.executeConditionNode(node, context, state);
          break;
        case 'parallel':
          stepResult = await this.executeParallelNode(node, workflow, context, state, steps);
          break;
        case 'delay':
          stepResult = await this.executeDelayNode(node, context, state);
          break;
        default:
          throw new Error(`Unsupported node type: ${node.type}`);
      }

      // Record step execution
      steps.push({
        nodeId: node.id,
        type: node.type,
        success,
        output: stepResult,
        duration: Date.now() - stepStartTime,
      });

      // Update state
      state.completedNodes.add(node.id);
      state.nodeOutputs.set(node.id, stepResult);

      // Merge output into variables if it's an object
      if (typeof stepResult === 'object' && stepResult !== null) {
        state.variables = { ...state.variables, ...stepResult };
      }

      // Execute next nodes
      const nextNodes = this.getNextNodes(node.id, workflow, state);
      for (const nextNode of nextNodes) {
        const nextSuccess = await this.executeNode(nextNode, workflow, context, state, steps);
        if (!nextSuccess) {
          success = false;
        }
      }

      return success;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      steps.push({
        nodeId: node.id,
        type: node.type,
        success: false,
        output: null,
        duration: Date.now() - stepStartTime,
        error: errorMessage,
      });

      return false;
    }
  }

  private async executeAgentNode(node: WorkflowNode, context: WorkflowExecutionContext, state: any): Promise<any> {
    const agentId = node.config.agentId;
    const input = this.resolveVariables(node.config.input || '{{input}}', state.variables);

    const result = await this.agentRuntime.executeAgent({
      agentId,
      sessionId: context.sessionId,
      input,
      variables: state.variables,
    });

    if (!result.success) {
      throw new Error(result.error || 'Agent execution failed');
    }

    return { [node.config.outputVariable || 'output']: result.output };
  }

  private async executeToolNode(node: WorkflowNode, context: WorkflowExecutionContext, state: any): Promise<any> {
    const toolId = node.config.toolId;
    const parameters = this.resolveVariables(node.config.parameters || {}, state.variables);

    const result = await this.toolRuntime.executeTool({
      toolId,
      agentId: context.workflowId, // Use workflow ID as agent ID for tools
      sessionId: context.sessionId,
      parameters,
    });

    if (!result.success) {
      throw new Error(result.error || 'Tool execution failed');
    }

    return { [node.config.outputVariable || 'output']: result.output };
  }

  private async executeConditionNode(node: WorkflowNode, context: WorkflowExecutionContext, state: any): Promise<any> {
    const condition = node.config.condition;
    const result = this.evaluateCondition(condition, state.variables);
    
    return { condition_result: result };
  }

  private async executeParallelNode(
    node: WorkflowNode,
    workflow: WorkflowDefinition,
    context: WorkflowExecutionContext,
    state: any,
    steps: WorkflowExecutionResult['steps']
  ): Promise<any> {
    // Execute multiple branches in parallel
    const parallelBranches = node.config.branches || [];
    const promises = parallelBranches.map(async (branchId: string) => {
      const branchNode = workflow.nodes.find(n => n.id === branchId);
      if (branchNode) {
        return await this.executeNode(branchNode, workflow, context, state, steps);
      }
      return false;
    });

    const results = await Promise.all(promises);
    return { parallel_results: results };
  }

  private async executeDelayNode(node: WorkflowNode, context: WorkflowExecutionContext, state: any): Promise<any> {
    const delay = node.config.delay || 1000; // Default 1 second
    await new Promise(resolve => setTimeout(resolve, delay));
    
    return { delayed: true };
  }

  private getNextNodes(nodeId: string, workflow: WorkflowDefinition, state: any): WorkflowNode[] {
    const outgoingEdges = workflow.edges.filter(edge => edge.source === nodeId);
    const nextNodes: WorkflowNode[] = [];

    for (const edge of outgoingEdges) {
      // Check condition if present
      if (edge.condition) {
        const conditionResult = this.evaluateCondition(edge.condition, state.variables);
        if (!conditionResult) {
          continue; // Skip this edge
        }
      }

      const nextNode = workflow.nodes.find(node => node.id === edge.target);
      if (nextNode) {
        nextNodes.push(nextNode);
      }
    }

    return nextNodes;
  }

  private resolveVariables(template: any, variables: Record<string, any>): any {
    if (typeof template === 'string') {
      return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
        return variables[key] !== undefined ? variables[key] : match;
      });
    }

    if (typeof template === 'object' && template !== null) {
      const resolved: any = {};
      for (const [key, value] of Object.entries(template)) {
        resolved[key] = this.resolveVariables(value, variables);
      }
      return resolved;
    }

    return template;
  }

  private evaluateCondition(condition: string, variables: Record<string, any>): boolean {
    // Simple condition evaluation - in production, use a proper expression parser
    try {
      // Replace variables in condition
      const resolvedCondition = condition.replace(/\{\{(\w+)\}\}/g, (match, key) => {
        const value = variables[key];
        return typeof value === 'string' ? `"${value}"` : String(value);
      });

      // Evaluate simple conditions like "{{status}} === 'success'"
      return Function(`"use strict"; return (${resolvedCondition})`)();
    } catch (error) {
      this.logger.warn(`Failed to evaluate condition: ${condition}`, error);
      return false;
    }
  }

  private async createExecutionRecord(context: WorkflowExecutionContext) {
    return await this.prisma.workflowExecution.create({
      data: {
        workflowId: context.workflowId,
        input: context.input,
        status: 'RUNNING',
        userId: context.userId,
        organizationId: context.organizationId,
      },
    });
  }

  private async updateExecutionRecord(executionId: string, data: any) {
    await this.prisma.workflowExecution.update({
      where: { id: executionId },
      data,
    });
  }
}
