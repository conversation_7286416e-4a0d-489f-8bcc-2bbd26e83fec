"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowFiltersDto = exports.UpdateWorkflowDto = exports.CreateWorkflowDto = exports.WorkflowPermissionsDto = exports.WorkflowMetadataDto = exports.WorkflowDefinitionDto = exports.WorkflowErrorHandlingDto = exports.WorkflowTriggerDto = exports.WorkflowStepDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
const workflow_entity_1 = require("../workflow.entity");
class WorkflowStepDto {
}
exports.WorkflowStepDto = WorkflowStepDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unique identifier for the step' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkflowStepDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Type of the step' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkflowStepDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Name of the step' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkflowStepDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Configuration for the step' }),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], WorkflowStepDto.prototype, "config", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Dependencies for this step' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], WorkflowStepDto.prototype, "dependencies", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Conditions for step execution' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], WorkflowStepDto.prototype, "conditions", void 0);
class WorkflowTriggerDto {
}
exports.WorkflowTriggerDto = WorkflowTriggerDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Type of trigger', enum: workflow_entity_1.WorkflowTriggerType }),
    (0, class_validator_1.IsEnum)(workflow_entity_1.WorkflowTriggerType),
    __metadata("design:type", String)
], WorkflowTriggerDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Configuration for the trigger' }),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], WorkflowTriggerDto.prototype, "config", void 0);
class WorkflowErrorHandlingDto {
}
exports.WorkflowErrorHandlingDto = WorkflowErrorHandlingDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of retry attempts' }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(10),
    __metadata("design:type", Number)
], WorkflowErrorHandlingDto.prototype, "retryCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Delay between retries in milliseconds' }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1000),
    __metadata("design:type", Number)
], WorkflowErrorHandlingDto.prototype, "retryDelay", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Fallback steps to execute on failure' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], WorkflowErrorHandlingDto.prototype, "fallbackSteps", void 0);
class WorkflowDefinitionDto {
}
exports.WorkflowDefinitionDto = WorkflowDefinitionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Version of the workflow definition' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkflowDefinitionDto.prototype, "version", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Steps in the workflow', type: [WorkflowStepDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => WorkflowStepDto),
    __metadata("design:type", Array)
], WorkflowDefinitionDto.prototype, "steps", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Triggers for the workflow', type: [WorkflowTriggerDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => WorkflowTriggerDto),
    __metadata("design:type", Array)
], WorkflowDefinitionDto.prototype, "triggers", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Variables available in the workflow' }),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], WorkflowDefinitionDto.prototype, "variables", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Error handling configuration' }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => WorkflowErrorHandlingDto),
    __metadata("design:type", WorkflowErrorHandlingDto)
], WorkflowDefinitionDto.prototype, "errorHandling", void 0);
class WorkflowMetadataDto {
}
exports.WorkflowMetadataDto = WorkflowMetadataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tags for the workflow' }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], WorkflowMetadataDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Category of the workflow' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkflowMetadataDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Priority level', enum: ['low', 'medium', 'high', 'critical'] }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkflowMetadataDto.prototype, "priority", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Estimated duration in seconds' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], WorkflowMetadataDto.prototype, "estimatedDuration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Estimated cost' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], WorkflowMetadataDto.prototype, "costEstimate", void 0);
class WorkflowPermissionsDto {
}
exports.WorkflowPermissionsDto = WorkflowPermissionsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User IDs with owner permissions' }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], WorkflowPermissionsDto.prototype, "owners", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User IDs with editor permissions' }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], WorkflowPermissionsDto.prototype, "editors", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User IDs with viewer permissions' }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], WorkflowPermissionsDto.prototype, "viewers", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether the workflow is public' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], WorkflowPermissionsDto.prototype, "public", void 0);
class CreateWorkflowDto {
}
exports.CreateWorkflowDto = CreateWorkflowDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Name of the workflow' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWorkflowDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Description of the workflow' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWorkflowDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Workflow definition' }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => WorkflowDefinitionDto),
    __metadata("design:type", WorkflowDefinitionDto)
], CreateWorkflowDto.prototype, "definition", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Metadata for the workflow' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => WorkflowMetadataDto),
    __metadata("design:type", WorkflowMetadataDto)
], CreateWorkflowDto.prototype, "metadata", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Permissions for the workflow' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => WorkflowPermissionsDto),
    __metadata("design:type", WorkflowPermissionsDto)
], CreateWorkflowDto.prototype, "permissions", void 0);
class UpdateWorkflowDto {
}
exports.UpdateWorkflowDto = UpdateWorkflowDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Name of the workflow' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateWorkflowDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Description of the workflow' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateWorkflowDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Workflow definition' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => WorkflowDefinitionDto),
    __metadata("design:type", WorkflowDefinitionDto)
], UpdateWorkflowDto.prototype, "definition", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Metadata for the workflow' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => WorkflowMetadataDto),
    __metadata("design:type", WorkflowMetadataDto)
], UpdateWorkflowDto.prototype, "metadata", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Permissions for the workflow' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => WorkflowPermissionsDto),
    __metadata("design:type", WorkflowPermissionsDto)
], UpdateWorkflowDto.prototype, "permissions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Force update even if workflow is active' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateWorkflowDto.prototype, "forceUpdate", void 0);
class WorkflowFiltersDto {
}
exports.WorkflowFiltersDto = WorkflowFiltersDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Search term for workflow name or description' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkflowFiltersDto.prototype, "search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Filter by workflow status', enum: workflow_entity_1.WorkflowStatus }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(workflow_entity_1.WorkflowStatus),
    __metadata("design:type", String)
], WorkflowFiltersDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Filter by user ID' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], WorkflowFiltersDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Filter by creation date after' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], WorkflowFiltersDto.prototype, "createdAfter", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Filter by creation date before' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], WorkflowFiltersDto.prototype, "createdBefore", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Page number for pagination' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], WorkflowFiltersDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of items per page' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], WorkflowFiltersDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Field to sort by' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkflowFiltersDto.prototype, "sortBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Sort order', enum: ['ASC', 'DESC'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkflowFiltersDto.prototype, "sortOrder", void 0);
//# sourceMappingURL=workflow.dto.js.map