import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import {
    BarChart3,
    TrendingUp,
    DollarSign,
    Target,
    Activity,
    Gauge,
    CheckCircle,
    Sparkles
} from 'lucide-react';

interface ProviderAnalytics {
    totalProviders: number;
    activeProviders: number;
    totalRequests: number;
    totalCost: number;
    averageLatency: number;
    overallReliability: number;
    requestsToday: number;
    costToday: number;
    topProviders: Array<{
        name: string;
        requests: number;
        cost: number;
        latency: number;
        reliability: number;
    }>;
    performanceTrends: Array<{
        date: string;
        requests: number;
        cost: number;
        latency: number;
    }>;
    costBreakdown: Array<{
        provider: string;
        cost: number;
        percentage: number;
    }>;
    recommendations: Array<{
        type: 'cost' | 'performance' | 'reliability';
        title: string;
        description: string;
        impact: 'high' | 'medium' | 'low';
        action: string;
    }>;
}

export default function ProviderAnalytics() {
    const [analytics, setAnalytics] = useState<ProviderAnalytics | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [timeRange, setTimeRange] = useState('7d');
    const { toast } = useToast();

    useEffect(() => {
        loadAnalytics();
    }, [timeRange]);

    const loadAnalytics = async () => {
        setIsLoading(true);
        try {
            const response = await fetch(`/api/v1/providers/analytics?range=${timeRange}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            if (data.success) {
                setAnalytics(data.data);
            } else {
                throw new Error(data.error || 'Failed to load analytics');
            }
        } catch (error) {
            console.error('Failed to load analytics:', error);
            toast({
                title: "Error",
                description: "Failed to load analytics data. Please try again.",
                variant: "destructive"
            });
        } finally {
            setIsLoading(false);
        }
    };

    const getRecommendationIcon = (type: string) => {
        switch (type) {
            case 'cost': return <DollarSign className="h-4 w-4" />;
            case 'performance': return <Gauge className="h-4 w-4" />;
            case 'reliability': return <CheckCircle className="h-4 w-4" />;
            default: return <Sparkles className="h-4 w-4" />;
        }
    };

    const getRecommendationColor = (impact: string) => {
        switch (impact) {
            case 'high': return 'border-red-200 bg-red-50';
            case 'medium': return 'border-yellow-200 bg-yellow-50';
            case 'low': return 'border-blue-200 bg-blue-50';
            default: return 'border-gray-200 bg-gray-50';
        }
    };

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-96">
                <div className="text-center">
                    <Activity className="h-8 w-8 animate-spin mx-auto mb-4" />
                    <p className="text-gray-600">Loading Analytics...</p>
                </div>
            </div>
        );
    }

    if (!analytics) {
        return (
            <div className="text-center py-12">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Analytics Available</h3>
                <p className="text-gray-600">Analytics will appear once you start using AI providers</p>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                        <BarChart3 className="h-6 w-6 text-blue-600" />
                        Provider Analytics
                    </h2>
                    <p className="text-gray-600 mt-1">Monitor performance and optimize your AI provider usage</p>
                </div>
                <div className="flex gap-2">
                    <select
                        value={timeRange}
                        onChange={(e) => setTimeRange(e.target.value)}
                        className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
                    >
                        <option value="24h">Last 24 hours</option>
                        <option value="7d">Last 7 days</option>
                        <option value="30d">Last 30 days</option>
                        <option value="90d">Last 90 days</option>
                    </select>
                </div>
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-blue-600">Total Requests</p>
                                <p className="text-2xl font-bold text-blue-900">{analytics.totalRequests.toLocaleString()}</p>
                                <p className="text-xs text-blue-700 mt-1">+{analytics.requestsToday} today</p>
                            </div>
                            <div className="bg-blue-100 p-3 rounded-full">
                                <Activity className="h-6 w-6 text-blue-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-green-600">Total Cost</p>
                                <p className="text-2xl font-bold text-green-900">${analytics.totalCost.toFixed(2)}</p>
                                <p className="text-xs text-green-700 mt-1">+${analytics.costToday.toFixed(2)} today</p>
                            </div>
                            <div className="bg-green-100 p-3 rounded-full">
                                <DollarSign className="h-6 w-6 text-green-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-purple-600">Avg Latency</p>
                                <p className="text-2xl font-bold text-purple-900">{Math.round(analytics.averageLatency)}ms</p>
                                <p className="text-xs text-purple-700 mt-1">Across all providers</p>
                            </div>
                            <div className="bg-purple-100 p-3 rounded-full">
                                <Gauge className="h-6 w-6 text-purple-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-orange-600">Reliability</p>
                                <p className="text-2xl font-bold text-orange-900">{Math.round(analytics.overallReliability * 100)}%</p>
                                <p className="text-xs text-orange-700 mt-1">Success rate</p>
                            </div>
                            <div className="bg-orange-100 p-3 rounded-full">
                                <CheckCircle className="h-6 w-6 text-orange-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Top Providers */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Target className="h-5 w-5" />
                            Top Providers
                        </CardTitle>
                        <CardDescription>
                            Most used providers by requests and cost
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {analytics.topProviders.map((provider, index) => (
                                <div key={provider.name} className="flex items-center justify-between p-4 border rounded-lg">
                                    <div className="flex items-center gap-3">
                                        <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                                            <span className="text-sm font-medium text-blue-600">{index + 1}</span>
                                        </div>
                                        <div>
                                            <h4 className="font-medium text-gray-900">{provider.name}</h4>
                                            <p className="text-sm text-gray-600">{provider.requests.toLocaleString()} requests</p>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <p className="font-medium text-gray-900">${provider.cost.toFixed(2)}</p>
                                        <p className="text-sm text-gray-600">{provider.latency}ms avg</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>

                {/* Cost Breakdown */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <DollarSign className="h-5 w-5" />
                            Cost Breakdown
                        </CardTitle>
                        <CardDescription>
                            Cost distribution across providers
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {analytics.costBreakdown.map((item) => (
                                <div key={item.provider} className="space-y-2">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm font-medium text-gray-700">{item.provider}</span>
                                        <span className="text-sm text-gray-600">${item.cost.toFixed(2)} ({item.percentage}%)</span>
                                    </div>
                                    <Progress value={item.percentage} className="h-2" />
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Performance Trends */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <TrendingUp className="h-5 w-5" />
                        Performance Trends
                    </CardTitle>
                    <CardDescription>
                        Request volume and cost over time
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="h-64 flex items-end justify-between gap-2">
                        {analytics.performanceTrends.map((trend, index) => (
                            <div key={index} className="flex flex-col items-center gap-2">
                                <div className="w-8 bg-blue-500 rounded-t" style={{ height: `${(trend.requests / Math.max(...analytics.performanceTrends.map(t => t.requests))) * 200}px` }}></div>
                                <div className="w-8 bg-green-500 rounded-t" style={{ height: `${(trend.cost / Math.max(...analytics.performanceTrends.map(t => t.cost))) * 100}px` }}></div>
                                <span className="text-xs text-gray-600">{new Date(trend.date).toLocaleDateString()}</span>
                            </div>
                        ))}
                    </div>
                    <div className="flex items-center gap-4 mt-4 text-sm">
                        <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-blue-500 rounded"></div>
                            <span>Requests</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-green-500 rounded"></div>
                            <span>Cost</span>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Recommendations */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Sparkles className="h-5 w-5" />
                        AI Recommendations
                    </CardTitle>
                    <CardDescription>
                        Intelligent suggestions to optimize your provider usage
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        {analytics.recommendations.map((recommendation, index) => (
                            <div
                                key={index}
                                className={`p-4 border rounded-lg ${getRecommendationColor(recommendation.impact)}`}
                            >
                                <div className="flex items-start gap-3">
                                    <div className="mt-1">
                                        {getRecommendationIcon(recommendation.type)}
                                    </div>
                                    <div className="flex-1">
                                        <h4 className="font-medium text-gray-900 mb-1">{recommendation.title}</h4>
                                        <p className="text-sm text-gray-600 mb-2">{recommendation.description}</p>
                                        <div className="flex items-center gap-2">
                                            <Badge variant="outline" className="text-xs">
                                                {recommendation.impact} impact
                                            </Badge>
                                            <span className="text-sm text-gray-600">{recommendation.action}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
} 