'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import AIProviderDashboard from '@/components/providers/AIProviderDashboard';
import ProviderAnalytics from '@/components/providers/ProviderAnalytics';
import ProviderConfigModal from '@/components/providers/ProviderConfigModal';
import { useToast } from '@/components/ui/use-toast';

export default function ProvidersPage() {
    const [activeTab, setActiveTab] = useState('dashboard');
    const [isConfigModalOpen, setIsConfigModalOpen] = useState(false);
    const { toast } = useToast();

    const handleSaveProvider = async (config: any) => {
        try {
            const response = await fetch('/api/v1/providers', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    name: config.name,
                    type: config.type,
                    config: {
                        apiKey: config.apiKey,
                        baseUrl: config.baseUrl,
                        timeout: config.timeout
                    },
                    isActive: config.isActive
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            if (result.success) {
                toast({
                    title: "Success",
                    description: "Provider created successfully."
                });
                // Refresh the dashboard
                window.location.reload();
            } else {
                throw new Error(result.error || 'Failed to save provider');
            }
        } catch (error) {
            console.error('Failed to save provider:', error);
            toast({
                title: "Error",
                description: error instanceof Error ? error.message : "Failed to save provider",
                variant: "destructive"
            });
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
            <div className="container mx-auto p-6">
                <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="dashboard">Provider Dashboard</TabsTrigger>
                        <TabsTrigger value="analytics">Analytics</TabsTrigger>
                    </TabsList>

                    <TabsContent value="dashboard">
                        <AIProviderDashboard />
                    </TabsContent>

                    <TabsContent value="analytics">
                        <ProviderAnalytics />
                    </TabsContent>
                </Tabs>

                <ProviderConfigModal
                    isOpen={isConfigModalOpen}
                    onClose={() => setIsConfigModalOpen(false)}
                    onSave={handleSaveProvider}
                />
            </div>
        </div>
    );
} 