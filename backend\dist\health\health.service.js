"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const redis_service_1 = require("../cache/redis.service");
const logger_service_1 = require("../logging/logger.service");
const typeorm_1 = require("typeorm");
let HealthService = class HealthService {
    constructor(configService, redisService, dataSource, logger) {
        this.configService = configService;
        this.redisService = redisService;
        this.dataSource = dataSource;
        this.logger = logger;
    }
    async checkHealth() {
        const startTime = Date.now();
        const checks = {
            database: await this.checkDatabase(),
            redis: await this.checkRedis(),
            memory: await this.checkMemory(),
            disk: await this.checkDisk(),
        };
        const overallStatus = this.determineOverallStatus(checks);
        const responseTime = Date.now() - startTime;
        const result = {
            status: overallStatus,
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            version: process.env.npm_package_version || '1.0.0',
            environment: this.configService.get('app.environment') || 'development',
            checks,
        };
        this.logger.logWithMeta('Health check completed', {
            status: overallStatus,
            responseTime,
            checks,
        }, 'HealthService');
        return result;
    }
    async checkDatabase() {
        const startTime = Date.now();
        try {
            await this.dataSource.query('SELECT 1');
            const responseTime = Date.now() - startTime;
            return {
                status: 'healthy',
                responseTime,
                details: {
                    type: 'postgres',
                    host: this.configService.get('app.database.host'),
                    port: this.configService.get('app.database.port'),
                    database: this.configService.get('app.database.database'),
                },
            };
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            this.logger.error('Database health check failed', error instanceof Error ? error.stack : 'Unknown error', 'HealthService');
            return {
                status: 'unhealthy',
                responseTime,
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                },
            };
        }
    }
    async checkRedis() {
        const startTime = Date.now();
        try {
            const ping = await this.redisService.ping();
            const responseTime = Date.now() - startTime;
            if (ping === 'PONG') {
                return {
                    status: 'healthy',
                    responseTime,
                    details: {
                        host: this.configService.get('app.redis.host'),
                        port: this.configService.get('app.redis.port'),
                    },
                };
            }
            else {
                throw new Error('Redis ping failed');
            }
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            this.logger.error('Redis health check failed', error instanceof Error ? error.stack : 'Unknown error', 'HealthService');
            return {
                status: 'unhealthy',
                responseTime,
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                },
            };
        }
    }
    async checkMemory() {
        const startTime = Date.now();
        try {
            const memUsage = process.memoryUsage();
            const responseTime = Date.now() - startTime;
            const memoryUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
            const isHealthy = memoryUsagePercent < 90;
            return {
                status: isHealthy ? 'healthy' : 'unhealthy',
                responseTime,
                details: {
                    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
                    heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
                    external: Math.round(memUsage.external / 1024 / 1024),
                    rss: Math.round(memUsage.rss / 1024 / 1024),
                    usagePercent: Math.round(memoryUsagePercent),
                },
            };
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            this.logger.error('Memory health check failed', error instanceof Error ? error.stack : 'Unknown error', 'HealthService');
            return {
                status: 'unhealthy',
                responseTime,
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                },
            };
        }
    }
    async checkDisk() {
        const startTime = Date.now();
        try {
            const responseTime = Date.now() - startTime;
            return {
                status: 'healthy',
                responseTime,
                details: {
                    message: 'Disk space check passed',
                },
            };
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            this.logger.error('Disk health check failed', error instanceof Error ? error.stack : 'Unknown error', 'HealthService');
            return {
                status: 'unhealthy',
                responseTime,
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                },
            };
        }
    }
    determineOverallStatus(checks) {
        const statuses = Object.values(checks).map(check => check.status);
        if (statuses.every(status => status === 'healthy')) {
            return 'healthy';
        }
        if (statuses.some(status => status === 'unhealthy')) {
            return 'unhealthy';
        }
        return 'degraded';
    }
    async getDetailedHealth() {
        const basicHealth = await this.checkHealth();
        return Object.assign(Object.assign({}, basicHealth), { system: {
                nodeVersion: process.version,
                platform: process.platform,
                arch: process.arch,
                pid: process.pid,
                memoryUsage: process.memoryUsage(),
                cpuUsage: process.cpuUsage(),
                env: this.configService.get('app.environment'),
            } });
    }
};
exports.HealthService = HealthService;
exports.HealthService = HealthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        redis_service_1.RedisService,
        typeorm_1.DataSource,
        logger_service_1.CustomLoggerService])
], HealthService);
//# sourceMappingURL=health.service.js.map