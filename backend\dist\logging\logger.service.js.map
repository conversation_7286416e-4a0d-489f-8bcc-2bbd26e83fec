{"version": 3, "file": "logger.service.js", "sourceRoot": "", "sources": ["../../src/logging/logger.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA2D;AAC3D,2CAA+C;AAC/C,mCAAmC;AACnC,6DAA6D;AAC7D,6BAA6B;AAGtB,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAG5B,YAAoB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAC5C,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAEO,gBAAgB;QACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,MAAM,CAAC;QACvE,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAClF,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,0BAA0B,CAAC,IAAI,MAAM,CAAC;QAElF,MAAM,UAAU,GAAwB;YACpC,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;gBAC3B,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO,CAC1B,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,EAC1B,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,EACzB,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAsD,EAAE,EAAE;wBAA1D,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,OAAW,EAAN,IAAI,cAApD,qDAAsD,CAAF;oBACvE,IAAI,GAAG,GAAG,GAAG,SAAS,KAAK,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,EAAE,CAAC;oBAChF,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC/B,GAAG,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;oBACtC,CAAC;oBACD,IAAI,KAAK,EAAE,CAAC;wBACR,GAAG,IAAI,KAAK,KAAK,EAAE,CAAC;oBACxB,CAAC;oBACD,OAAO,GAAG,CAAC;gBACf,CAAC,CAAC,CACL;aACJ,CAAC;SACL,CAAC;QAEF,IAAI,iBAAiB,EAAE,CAAC;YAEpB,UAAU,CAAC,IAAI,CACX,IAAI,eAAe,CAAC;gBAChB,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,kBAAkB,CAAC;gBACrD,WAAW,EAAE,YAAY;gBACzB,KAAK,EAAE,OAAO;gBACd,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,KAAK;gBACf,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO,CAC1B,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,EAC1B,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EACtC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CACxB;aACJ,CAAC,CACL,CAAC;YAGF,UAAU,CAAC,IAAI,CACX,IAAI,eAAe,CAAC;gBAChB,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,qBAAqB,CAAC;gBACxD,WAAW,EAAE,YAAY;gBACzB,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,KAAK;gBACf,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO,CAC1B,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,EAC1B,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CACxB;aACJ,CAAC,CACL,CAAC;QACN,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC;YAC/B,KAAK,EAAE,QAAQ;YACf,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO,CAC1B,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,EAC1B,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EACtC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CACxB;YACD,WAAW,EAAE,EAAE,OAAO,EAAE,aAAa,EAAE;YACvC,UAAU;SACb,CAAC,CAAC;IACP,CAAC;IAED,GAAG,CAAC,OAAe,EAAE,OAAgB;QACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,OAAe,EAAE,KAAc,EAAE,OAAgB;QACnD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;IACnD,CAAC;IAED,IAAI,CAAC,OAAe,EAAE,OAAgB;QAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,OAAe,EAAE,OAAgB;QACnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAC5C,CAAC;IAED,OAAO,CAAC,OAAe,EAAE,OAAgB;QACrC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAC9C,CAAC;IAGD,WAAW,CAAC,OAAe,EAAE,IAAyB,EAAE,OAAgB;QACpE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,kBAAI,OAAO,IAAK,IAAI,EAAG,CAAC;IACpD,CAAC;IAED,gBAAgB,CAAC,OAAe,EAAE,KAAY,EAAE,OAA4B,EAAE,EAAE,OAAgB;QAC5F,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,kBACrB,OAAO,EACP,KAAK,EAAE;gBACH,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,KAAK,EAAE,KAAK,CAAC,KAAK;aACrB,IACE,IAAI,EACT,CAAC;IACP,CAAC;IAED,cAAc,CAAC,SAAiB,EAAE,QAAgB,EAAE,OAA4B,EAAE,EAAE,OAAgB;QAChG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,SAAS,EAAE,kBACxC,OAAO;YACP,SAAS;YACT,QAAQ,IACL,IAAI,EACT,CAAC;IACP,CAAC;IAED,WAAW,CAAC,KAAa,EAAE,OAA4B,EAAE,EAAE,OAAgB;QACvE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,KAAK,EAAE,kBACvC,OAAO;YACP,KAAK,IACF,IAAI,EACT,CAAC;IACP,CAAC;IAED,WAAW,CAAC,KAAa,EAAE,QAAgB,EAAE,OAA4B,EAAE,EAAE,OAAgB;QACzF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,KAAK,EAAE,kBACxC,OAAO;YACP,KAAK;YACL,QAAQ,IACL,IAAI,EACT,CAAC;IACP,CAAC;IAED,aAAa,CAAC,MAAc,EAAE,GAAW,EAAE,UAAkB,EAAE,QAAgB,EAAE,OAA4B,EAAE,EAAE,OAAgB;QAC7H,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,MAAM,IAAI,GAAG,EAAE,kBAC5C,OAAO;YACP,MAAM;YACN,GAAG;YACH,UAAU;YACV,QAAQ,IACL,IAAI,EACT,CAAC;IACP,CAAC;CACJ,CAAA;AApJY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAI0B,sBAAa;GAHvC,mBAAmB,CAoJ/B"}