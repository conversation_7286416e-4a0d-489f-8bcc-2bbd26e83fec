'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { apixSDK, AIProviderEventPayload, AIProviderEventType } from '@/lib/apix/apix-sdk';
import { useToast } from '@/components/ui/use-toast';

interface UseApixOptions {
  autoConnect?: boolean;
  organizationId?: string;
  userId?: string;
}

interface ProviderRequestState {
  requestId: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  progress?: number;
  message?: string;
  result?: any;
  error?: string;
  startTime?: number;
  duration?: number;
}

export function useApix(options: UseApixOptions = {}) {
  const { autoConnect = true, organizationId, userId } = options;
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [requests, setRequests] = useState<Map<string, ProviderRequestState>>(new Map());
  const unsubscribeRef = useRef<Array<() => void>>([]);
  const { toast } = useToast();

  // Connect to APIX
  const connect = useCallback(async () => {
    if (isConnecting || isConnected) return;

    try {
      setIsConnecting(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      await apixSDK.connect(token, organizationId, userId);
      setIsConnected(true);

      toast({
        title: "Connected",
        description: "Real-time connection established",
      });
    } catch (error) {
      console.error('APIX connection failed:', error);
      toast({
        title: "Connection Failed",
        description: "Failed to establish real-time connection",
        variant: "destructive"
      });
    } finally {
      setIsConnecting(false);
    }
  }, [isConnecting, isConnected, organizationId, userId, toast]);

  // Disconnect from APIX
  const disconnect = useCallback(() => {
    apixSDK.disconnect();
    setIsConnected(false);
    setRequests(new Map());

    // Clean up subscriptions
    unsubscribeRef.current.forEach(unsub => unsub());
    unsubscribeRef.current = [];
  }, []);

  // Subscribe to provider events
  const subscribeToProvider = useCallback((
    providerId: string,
    handlers: {
      onRequestStart?: (payload: AIProviderEventPayload<'provider.request.start'>) => void;
      onRequestProgress?: (payload: AIProviderEventPayload<'provider.request.progress'>) => void;
      onRequestComplete?: (payload: AIProviderEventPayload<'provider.request.complete'>) => void;
      onRequestError?: (payload: AIProviderEventPayload<'provider.request.error'>) => void;
      onHealthUpdate?: (payload: AIProviderEventPayload<'provider.health_update'>) => void;
    } = {}
  ) => {
    const unsubscribe = apixSDK.subscribeToProviderEvents(providerId, {
      onRequestStart: (payload) => {
        setRequests(prev => new Map(prev).set(payload.requestId, {
          requestId: payload.requestId,
          status: 'running',
          startTime: Date.now()
        }));

        handlers.onRequestStart?.(payload);
      },
      onRequestProgress: (payload) => {
        setRequests(prev => {
          const newMap = new Map(prev);
          const existing = newMap.get(payload.requestId);
          if (existing) {
            newMap.set(payload.requestId, {
              ...existing,
              status: 'running',
              progress: payload.progress,
              message: payload.message
            });
          }
          return newMap;
        });

        handlers.onRequestProgress?.(payload);
      },
      onRequestComplete: (payload) => {
        setRequests(prev => {
          const newMap = new Map(prev);
          const existing = newMap.get(payload.requestId);
          if (existing) {
            newMap.set(payload.requestId, {
              ...existing,
              status: 'completed',
              result: payload.output,
              duration: payload.durationMs
            });
          }
          return newMap;
        });

        handlers.onRequestComplete?.(payload);
      },
      onRequestError: (payload) => {
        setRequests(prev => {
          const newMap = new Map(prev);
          const existing = newMap.get(payload.requestId);
          if (existing) {
            newMap.set(payload.requestId, {
              ...existing,
              status: 'error',
              error: payload.error
            });
          }
          return newMap;
        });

        handlers.onRequestError?.(payload);
      },
      onHealthUpdate: (payload) => {
        handlers.onHealthUpdate?.(payload);
      }
    });

    unsubscribeRef.current.push(unsubscribe);
    return unsubscribe;
  }, []);

  // Subscribe to organization events
  const subscribeToOrganization = useCallback((
    orgId: string,
    handlers: {
      onProviderCreated?: (payload: AIProviderEventPayload<'provider.created'>) => void;
      onProviderUpdated?: (payload: AIProviderEventPayload<'provider.updated'>) => void;
      onProviderDeleted?: (payload: AIProviderEventPayload<'provider.deleted'>) => void;
      onUsageReport?: (payload: AIProviderEventPayload<'provider.usage.report'>) => void;
    } = {}
  ) => {
    const unsubscribe = apixSDK.subscribeToOrganizationEvents(orgId, {
      onProviderCreated: (payload) => {
        toast({
          title: "Provider Created",
          description: `Provider ${payload.name} has been created`,
        });
        handlers.onProviderCreated?.(payload);
      },
      onProviderUpdated: (payload) => {
        toast({
          title: "Provider Updated",
          description: `Provider has been updated`,
        });
        handlers.onProviderUpdated?.(payload);
      },
      onProviderDeleted: (payload) => {
        toast({
          title: "Provider Deleted",
          description: "Provider has been deleted",
        });
        handlers.onProviderDeleted?.(payload);
      },
      onUsageReport: (payload) => {
        handlers.onUsageReport?.(payload);
      }
    });

    unsubscribeRef.current.push(unsubscribe);
    return unsubscribe;
  }, [toast]);

  // Generic event subscription
  const subscribe = useCallback(<T extends AIProviderEventType>(
    channel: string,
    eventType: T,
    handler: (payload: AIProviderEventPayload<T>) => void
  ) => {
    const unsubscribe = apixSDK.subscribe(channel, eventType, handler);
    unsubscribeRef.current.push(unsubscribe);
    return unsubscribe;
  }, []);

  // Send provider request with real-time tracking
  const sendProviderRequest = useCallback(async (
    providerId: string,
    modelId: string,
    requestData: {
      messages?: any[];
      prompt?: string;
      maxTokens?: number;
      temperature?: number;
      stream?: boolean;
    }
  ) => {
    try {
      const response = await fetch(`/api/v1/providers/${providerId}/models/${modelId}/request`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success && result.data?.requestId && result.data?.response) {
        // The request is now being tracked via APIX events
        return result.data.response;
      } else {
        throw new Error(result.error || 'Request failed');
      }
    } catch (error) {
      console.error('Provider request failed:', error);
      throw error;
    }
  }, []);

  // Get request status
  const getRequestStatus = useCallback((requestId: string) => {
    return requests.get(requestId);
  }, [requests]);

  // Clear completed requests
  const clearCompletedRequests = useCallback(() => {
    setRequests(prev => {
      const newMap = new Map();
      for (const [requestId, request] of Array.from(prev.entries())) {
        if (request.status === 'running' || request.status === 'pending') {
          newMap.set(requestId, request);
        }
      }
      return newMap;
    });
  }, []);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [autoConnect, connect, disconnect]);

  // Monitor connection status
  useEffect(() => {
    const checkConnection = () => {
      const connected = apixSDK.isConnectedStatus();
      if (connected !== isConnected) {
        setIsConnected(connected);
      }
    };

    const interval = setInterval(checkConnection, 1000);
    return () => clearInterval(interval);
  }, [isConnected]);

  return {
    // Connection
    isConnected,
    isConnecting,
    connect,
    disconnect,

    // Subscriptions
    subscribeToProvider,
    subscribeToOrganization,
    subscribe,

    // Requests
    sendProviderRequest,
    getRequestStatus,
    clearCompletedRequests,
    requests: Array.from(requests.values()),

    // Utilities
    sessionId: apixSDK.getSessionId()
  };
}

// Specialized hook for AI Provider monitoring
export function useProviderMonitoring(providerId: string, organizationId?: string) {
  const [providerHealth, setProviderHealth] = useState<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    latency: number;
    lastUpdate: Date;
  } | null>(null);

  const [activeRequests, setActiveRequests] = useState<Array<{
    requestId: string;
    status: 'running' | 'completed' | 'error';
    progress?: number;
    message?: string;
    startTime: number;
  }>>([]);

  const { subscribeToProvider } = useApix({ organizationId });

  useEffect(() => {
    const unsubscribe = subscribeToProvider(providerId, {
      onRequestStart: (payload) => {
        setActiveRequests(prev => [...prev, {
          requestId: payload.requestId,
          status: 'running',
          startTime: Date.now()
        }]);
      },
      onRequestProgress: (payload) => {
        setActiveRequests(prev =>
          prev.map(req =>
            req.requestId === payload.requestId
              ? { ...req, progress: payload.progress, message: payload.message }
              : req
          )
        );
      },
      onRequestComplete: (payload) => {
        setActiveRequests(prev =>
          prev.map(req =>
            req.requestId === payload.requestId
              ? { ...req, status: 'completed' as const }
              : req
          )
        );
      },
      onRequestError: (payload) => {
        setActiveRequests(prev =>
          prev.map(req =>
            req.requestId === payload.requestId
              ? { ...req, status: 'error' as const }
              : req
          )
        );
      },
      onHealthUpdate: (payload) => {
        setProviderHealth({
          status: payload.status,
          latency: payload.latency,
          lastUpdate: new Date()
        });
      }
    });

    return unsubscribe;
  }, [providerId, subscribeToProvider]);

  // Clean up completed requests after 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
      setActiveRequests(prev =>
        prev.filter(req =>
          req.status === 'running' || req.startTime > fiveMinutesAgo
        )
      );
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, []);

  return {
    providerHealth,
    activeRequests,
    isHealthy: providerHealth?.status === 'healthy',
    isDegraded: providerHealth?.status === 'degraded',
    isUnhealthy: providerHealth?.status === 'unhealthy'
  };
}