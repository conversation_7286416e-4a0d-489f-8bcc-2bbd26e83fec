import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, BaseEntity, ManyToOne } from 'typeorm';
import { Workflow } from '../workflows/workflow.entity';
import { Agent } from '../agents/agent.entity';
import { Tool } from '../tools/tool.entity';
import { Tenant } from '../tenants/entities/tenant.entity';
import { Organization } from '../database/entities/organization.entity';

export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  VIEWER = 'viewer',
}

export enum AuthProvider {
  LOCAL = 'local',
  GOOGLE = 'google',
  GITHUB = 'github',
}

@Entity('users')
export class User extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ unique: true })
  email!: string;

  @Column()
  firstName!: string;

  @Column()
  lastName!: string;

  @Column({ nullable: true })
  password!: string;

  @Column({ nullable: true })
  avatar!: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.USER, 
  })
  role!: UserRole;

  @Column({
    type: 'enum',
    enum: AuthProvider,
    default: AuthProvider.LOCAL,
  })
  authProvider!: AuthProvider;

  @Column({ nullable: true })
  authToken!: string;

  @Column({ nullable: true })
  googleId!: string;

  @Column({ nullable: true })
  githubId!: string;

  @Column({ default: true })
  isActive!: boolean;

  @Column({ nullable: true })
  lastLoginAt!: Date;

  @Column({ type: 'jsonb', nullable: true })
  preferences!: Record<string, any>;

  @OneToMany(() => Workflow, workflow => workflow.user)
  workflows!: Workflow[];

  @OneToMany(() => Agent, agent => agent.user)
  agents!: Agent[];

  @OneToMany(() => Tool, tool => tool.user)
  tools!: Tool[];

  @Column({ nullable: true })
  tenantId!: string;

  @ManyToOne(() => Tenant, tenant => tenant.users)
  tenant!: Tenant;

  @Column({ nullable: true })
  organizationId!: string;

  @ManyToOne(() => Organization, organization => organization.users)
  organization!: Organization;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}