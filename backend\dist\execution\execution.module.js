"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExecutionModule = void 0;
const common_1 = require("@nestjs/common");
const execution_controller_1 = require("./execution.controller");
const execution_module_1 = require("../agents/execution/execution.module");
const execution_module_2 = require("../tools/execution/execution.module");
const execution_module_3 = require("../workflows/execution/execution.module");
const ai_provider_module_1 = require("../providers/ai-provider.module");
const common_module_1 = require("../common/common.module");
let ExecutionModule = class ExecutionModule {
};
exports.ExecutionModule = ExecutionModule;
exports.ExecutionModule = ExecutionModule = __decorate([
    (0, common_1.Module)({
        imports: [
            common_module_1.CommonModule,
            execution_module_1.AgentExecutionModule,
            execution_module_2.ToolExecutionModule,
            execution_module_3.WorkflowExecutionModule,
            ai_provider_module_1.AIProviderModule,
        ],
        controllers: [execution_controller_1.ExecutionController],
    })
], ExecutionModule);
//# sourceMappingURL=execution.module.js.map