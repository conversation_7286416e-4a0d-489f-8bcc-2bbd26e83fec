{"version": 3, "file": "tenant-context.middleware.js", "sourceRoot": "", "sources": ["../../src/middleware/tenant-context.middleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAgF;AAEhF,8DAAgE;AAGzD,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IAChC,YAA6B,MAA2B;QAA3B,WAAM,GAAN,MAAM,CAAqB;IAAI,CAAC;IAE7D,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QAC/C,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAE3C,IAAI,QAAQ,EAAE,CAAC;gBACV,GAAW,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;gBACpC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,QAAQ,EAAE,EAAE,yBAAyB,CAAC,CAAC;YAClF,CAAC;YAED,IAAI,EAAE,CAAC;QACX,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,yBAAyB,CAAC,CAAC;YACxL,IAAI,EAAE,CAAC;QACX,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,GAAY;QAEhC,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;QAC9B,IAAI,IAAI,EAAE,CAAC;YACP,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,SAAS,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,KAAK,KAAK,EAAE,CAAC;gBAC1D,OAAO,SAAS,CAAC;YACrB,CAAC;QACL,CAAC;QAGD,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAW,CAAC;QAC1D,IAAI,YAAY,EAAE,CAAC;YACf,OAAO,YAAY,CAAC;QACxB,CAAC;QAGD,MAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,MAAgB,CAAC;QAC/C,IAAI,WAAW,EAAE,CAAC;YACd,OAAO,WAAW,CAAC;QACvB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ,CAAA;AA3CY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;qCAE4B,oCAAmB;GAD/C,uBAAuB,CA2CnC"}