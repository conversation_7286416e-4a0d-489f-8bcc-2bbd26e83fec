import { NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { CustomLoggerService } from '../logging/logger.service';
export declare class TenantContextMiddleware implements NestMiddleware {
    private readonly logger;
    constructor(logger: CustomLoggerService);
    use(req: Request, res: Response, next: NextFunction): void;
    private extractTenantId;
}
