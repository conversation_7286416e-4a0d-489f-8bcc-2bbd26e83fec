"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowValidationService = void 0;
const common_1 = require("@nestjs/common");
const logger_service_1 = require("../logging/logger.service");
let WorkflowValidationService = class WorkflowValidationService {
    constructor(logger) {
        this.logger = logger;
    }
    async validateWorkflowDefinition(definition) {
        try {
            if (!definition.version || !this.isValidVersion(definition.version)) {
                throw new common_1.BadRequestException('Invalid workflow version format');
            }
            if (!definition.steps || definition.steps.length === 0) {
                throw new common_1.BadRequestException('Workflow must have at least one step');
            }
            const stepIds = definition.steps.map(step => step.id);
            const uniqueStepIds = new Set(stepIds);
            if (stepIds.length !== uniqueStepIds.size) {
                throw new common_1.BadRequestException('Step IDs must be unique');
            }
            for (const step of definition.steps) {
                await this.validateStep(step);
            }
            if (!definition.triggers || definition.triggers.length === 0) {
                throw new common_1.BadRequestException('Workflow must have at least one trigger');
            }
            for (const trigger of definition.triggers) {
                await this.validateTrigger(trigger);
            }
            await this.validateDependencies(definition.steps);
            await this.validateErrorHandling(definition.errorHandling);
            this.logger.log('Workflow definition validation passed', 'WorkflowValidationService');
        }
        catch (error) {
            this.logger.error(`Workflow validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : 'Unknown error', 'WorkflowValidationService');
            throw error;
        }
    }
    async validateStep(step) {
        if (!step.id || typeof step.id !== 'string') {
            throw new common_1.BadRequestException('Step must have a valid string ID');
        }
        if (!step.type || typeof step.type !== 'string') {
            throw new common_1.BadRequestException('Step must have a valid type');
        }
        if (!step.name || typeof step.name !== 'string') {
            throw new common_1.BadRequestException('Step must have a valid name');
        }
        if (!step.config || typeof step.config !== 'object') {
            throw new common_1.BadRequestException('Step must have a valid configuration object');
        }
        await this.validateStepTypeConfig(step.type, step.config);
        if (step.dependencies) {
            if (!Array.isArray(step.dependencies)) {
                throw new common_1.BadRequestException('Step dependencies must be an array');
            }
            for (const dep of step.dependencies) {
                if (typeof dep !== 'string') {
                    throw new common_1.BadRequestException('Step dependencies must be string IDs');
                }
            }
        }
        if (step.conditions) {
            if (typeof step.conditions !== 'object') {
                throw new common_1.BadRequestException('Step conditions must be an object');
            }
        }
    }
    async validateTrigger(trigger) {
        if (!trigger.type) {
            throw new common_1.BadRequestException('Trigger must have a valid type');
        }
        if (!trigger.config || typeof trigger.config !== 'object') {
            throw new common_1.BadRequestException('Trigger must have a valid configuration object');
        }
        await this.validateTriggerTypeConfig(trigger.type, trigger.config);
    }
    async validateErrorHandling(errorHandling) {
        if (!errorHandling) {
            throw new common_1.BadRequestException('Error handling configuration is required');
        }
        if (typeof errorHandling.retryCount !== 'number' || errorHandling.retryCount < 0) {
            throw new common_1.BadRequestException('Retry count must be a non-negative number');
        }
        if (typeof errorHandling.retryDelay !== 'number' || errorHandling.retryDelay < 0) {
            throw new common_1.BadRequestException('Retry delay must be a non-negative number');
        }
        if (errorHandling.fallbackSteps) {
            if (!Array.isArray(errorHandling.fallbackSteps)) {
                throw new common_1.BadRequestException('Fallback steps must be an array');
            }
            for (const step of errorHandling.fallbackSteps) {
                if (typeof step !== 'string') {
                    throw new common_1.BadRequestException('Fallback steps must be string IDs');
                }
            }
        }
    }
    async validateDependencies(steps) {
        const stepIds = new Set(steps.map(step => step.id));
        const visited = new Set();
        const recursionStack = new Set();
        for (const step of steps) {
            if (step.dependencies) {
                for (const depId of step.dependencies) {
                    if (!stepIds.has(depId)) {
                        throw new common_1.BadRequestException(`Step ${step.id} depends on non-existent step ${depId}`);
                    }
                }
            }
        }
        for (const step of steps) {
            if (!visited.has(step.id)) {
                if (this.hasCircularDependency(step.id, steps, visited, recursionStack)) {
                    throw new common_1.BadRequestException(`Circular dependency detected involving step ${step.id}`);
                }
            }
        }
    }
    hasCircularDependency(stepId, steps, visited, recursionStack) {
        if (recursionStack.has(stepId)) {
            return true;
        }
        if (visited.has(stepId)) {
            return false;
        }
        visited.add(stepId);
        recursionStack.add(stepId);
        const step = steps.find(s => s.id === stepId);
        if (step && step.dependencies) {
            for (const depId of step.dependencies) {
                if (this.hasCircularDependency(depId, steps, visited, recursionStack)) {
                    return true;
                }
            }
        }
        recursionStack.delete(stepId);
        return false;
    }
    async validateStepTypeConfig(type, config) {
        switch (type) {
            case 'http_request':
                this.validateHttpRequestConfig(config);
                break;
            case 'data_transformation':
                this.validateDataTransformationConfig(config);
                break;
            case 'condition':
                this.validateConditionConfig(config);
                break;
            case 'loop':
                this.validateLoopConfig(config);
                break;
            default:
                if (typeof config !== 'object') {
                    throw new common_1.BadRequestException(`Invalid configuration for step type: ${type}`);
                }
        }
    }
    async validateTriggerTypeConfig(type, config) {
        switch (type) {
            case 'webhook':
                this.validateWebhookConfig(config);
                break;
            case 'scheduled':
                this.validateScheduledConfig(config);
                break;
            case 'event':
                this.validateEventConfig(config);
                break;
            default:
                if (typeof config !== 'object') {
                    throw new common_1.BadRequestException(`Invalid configuration for trigger type: ${type}`);
                }
        }
    }
    validateHttpRequestConfig(config) {
        if (!config.url || typeof config.url !== 'string') {
            throw new common_1.BadRequestException('HTTP request step must have a valid URL');
        }
        if (config.method && !['GET', 'POST', 'PUT', 'PATCH', 'DELETE'].includes(config.method)) {
            throw new common_1.BadRequestException('HTTP request step must have a valid method');
        }
    }
    validateDataTransformationConfig(config) {
        if (!config.transformation || typeof config.transformation !== 'object') {
            throw new common_1.BadRequestException('Data transformation step must have a valid transformation configuration');
        }
    }
    validateConditionConfig(config) {
        if (!config.condition || typeof config.condition !== 'object') {
            throw new common_1.BadRequestException('Condition step must have a valid condition configuration');
        }
    }
    validateLoopConfig(config) {
        if (!config.iterator || typeof config.iterator !== 'string') {
            throw new common_1.BadRequestException('Loop step must have a valid iterator configuration');
        }
    }
    validateWebhookConfig(config) {
        if (!config.path || typeof config.path !== 'string') {
            throw new common_1.BadRequestException('Webhook trigger must have a valid path');
        }
    }
    validateScheduledConfig(config) {
        if (!config.cron || typeof config.cron !== 'string') {
            throw new common_1.BadRequestException('Scheduled trigger must have a valid cron expression');
        }
    }
    validateEventConfig(config) {
        if (!config.eventType || typeof config.eventType !== 'string') {
            throw new common_1.BadRequestException('Event trigger must have a valid event type');
        }
    }
    isValidVersion(version) {
        return /^\d+\.\d+\.\d+$/.test(version);
    }
};
exports.WorkflowValidationService = WorkflowValidationService;
exports.WorkflowValidationService = WorkflowValidationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [logger_service_1.CustomLoggerService])
], WorkflowValidationService);
//# sourceMappingURL=workflow-validation.service.js.map