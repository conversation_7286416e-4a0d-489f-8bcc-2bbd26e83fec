import { Workflow } from './workflow.entity';
import { CustomLoggerService } from '../logging/logger.service';
import { RedisService } from '../cache/redis.service';
export interface WorkflowAnalytics {
    executionMetrics: {
        totalExecutions: number;
        successfulExecutions: number;
        failedExecutions: number;
        successRate: number;
        averageExecutionTime: number;
        totalCost: number;
        averageCost: number;
    };
    performanceMetrics: {
        stepPerformance: Array<{
            stepId: string;
            stepName: string;
            averageDuration: number;
            successRate: number;
            failureRate: number;
            totalExecutions: number;
        }>;
        bottleneckSteps: Array<{
            stepId: string;
            stepName: string;
            averageDuration: number;
            impact: number;
        }>;
        throughput: {
            executionsPerHour: number;
            executionsPerDay: number;
            peakHour: string;
            peakDay: string;
        };
    };
    costAnalysis: {
        totalCost: number;
        costPerExecution: number;
        costByStep: Array<{
            stepId: string;
            stepName: string;
            totalCost: number;
            averageCost: number;
        }>;
        costTrend: Array<{
            date: string;
            cost: number;
            executions: number;
        }>;
    };
    errorAnalysis: {
        errorRate: number;
        commonErrors: Array<{
            error: string;
            count: number;
            percentage: number;
        }>;
        errorTrend: Array<{
            date: string;
            errors: number;
            total: number;
        }>;
    };
    optimization: {
        recommendations: Array<{
            type: 'performance' | 'cost' | 'reliability';
            title: string;
            description: string;
            impact: 'high' | 'medium' | 'low';
            estimatedSavings?: number;
        }>;
        bottlenecks: Array<{
            stepId: string;
            stepName: string;
            issue: string;
            recommendation: string;
        }>;
    };
}
export declare class WorkflowAnalyticsService {
    private readonly logger;
    private readonly redisService;
    constructor(logger: CustomLoggerService, redisService: RedisService);
    getWorkflowAnalytics(workflow: Workflow): Promise<WorkflowAnalytics>;
    getOrganizationAnalytics(organizationId: string, timeRange: {
        start: Date;
        end: Date;
    }): Promise<any>;
    getRealTimeMetrics(workflowId: string): Promise<any>;
    private calculateExecutionMetrics;
    private calculatePerformanceMetrics;
    private calculateCostAnalysis;
    private calculateErrorAnalysis;
    private generateOptimizationRecommendations;
    private analyzeStepPerformance;
    private identifyBottlenecks;
    private calculateThroughput;
    private calculateCostByStep;
    private calculateCostTrend;
    private analyzeCommonErrors;
    private calculateErrorTrend;
    private generateRecommendations;
    private identifyOptimizationBottlenecks;
    private calculateHourlyStats;
    private calculateDailyStats;
    private getRecentExecutions;
    private getWorkflowsByOrganization;
    private calculateAverageSuccessRate;
    private getTopPerformingWorkflows;
    private getOrganizationCostTrend;
    private getActiveExecutions;
    private getCurrentStep;
    private getQueueLength;
    private getLastExecution;
    private getSystemHealth;
}
